import { pgTable, text, serial, integer, boolean, timestamp, decimal, uuid } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const services = pgTable("services", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

export const customers = pgTable("customers", {
  id: uuid("id").primaryKey().defaultRandom(),
  fullName: text("full_name").notNull(),
  phoneNumber: text("phone_number").notNull(),
  email: text("email"), // Email alanını opsiyonel yap
  createdAt: timestamp("created_at").defaultNow(),
});

export const organizations = pgTable("organizations", {
  id: uuid("id").primaryKey().defaultRandom(),
  customerId: uuid("customer_id").notNull().references(() => customers.id),
  eventDate: text("event_date").notNull(), // YYYY-MM-DD format
  startTime: text("start_time").notNull(), // HH:MM format
  endTime: text("end_time").notNull(), // HH:MM format
  totalPrice: decimal("total_price", { precision: 10, scale: 2 }).notNull(),
  discountPercentage: decimal("discount_percentage", { precision: 5, scale: 2 }).default("0"),
  finalPrice: decimal("final_price", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull().default("Teklif"), // "Teklif", "Kesinleşti", "İptal"
  createdAt: timestamp("created_at").defaultNow(),
});

export const organizationServices = pgTable("organization_services", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id, { onDelete: "cascade" }),
  serviceId: uuid("service_id").notNull().references(() => services.id),
});

// Insert schemas
export const insertServiceSchema = createInsertSchema(services).omit({
  id: true,
  createdAt: true,
});

export const insertCustomerSchema = createInsertSchema(customers).omit({
  id: true,
  createdAt: true,
});

export const insertOrganizationSchema = createInsertSchema(organizations).omit({
  id: true,
  createdAt: true,
});

export const createOrganizationSchema = z.object({
  customer: insertCustomerSchema,
  serviceIds: z.array(z.string().uuid()),
  eventDate: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  discountPercentage: z.number().min(0).max(100).default(0),
});

// Types
export type Service = typeof services.$inferSelect;
export type InsertService = z.infer<typeof insertServiceSchema>;
export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type Organization = typeof organizations.$inferSelect;
export type InsertOrganization = z.infer<typeof insertOrganizationSchema>;
export type CreateOrganization = z.infer<typeof createOrganizationSchema>;
export type OrganizationService = typeof organizationServices.$inferSelect;

// DTOs
export interface OrganizationDetailsDto {
  id: string;
  customer: Customer;
  services: Service[];
  eventDate: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  discountPercentage: number;
  finalPrice: number;
  status: string;
  createdAt: Date;
}

export interface CalendarEventDto {
  id: string;
  title: string;
  start: Date;
  end: Date;
  customerName: string;
  status: string;
}
