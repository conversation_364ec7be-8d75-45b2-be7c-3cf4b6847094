import React from 'react'
import { Button } from '../ui/button'

export const EmergencyAuth: React.FC = () => {
  const handleEmergencyReset = () => {
    console.log('🚨 Emergency auth reset triggered')
    
    // Clear all storage
    localStorage.clear()
    sessionStorage.clear()
    
    // Clear cookies
    document.cookie.split(";").forEach((c) => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/")
    })
    
    // Reload page
    window.location.reload()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-red-50">
      <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
        <div className="text-6xl mb-4">🚨</div>
        <h1 className="text-2xl font-bold text-red-600 mb-4">
          Kimlik Doğrulama Sorunu
        </h1>
        <p className="text-gray-600 mb-4">
          Sistem yüklenirken bir sorun oluştu. Bu genellikle ağ bağlantısı veya oturum sorunlarından kaynaklanır.
        </p>
        <div className="text-left bg-gray-50 p-3 rounded mb-4 text-xs">
          <p><strong>Olası nedenler:</strong></p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Ağ bağlantısı yavaş veya kesintili</li>
            <li>Tarayıcı önbelleği sorunu</li>
            <li>Oturum süresi dolmuş</li>
          </ul>
        </div>
        <Button 
          onClick={handleEmergencyReset}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          Sistemi Sıfırla
        </Button>
        <p className="text-xs text-gray-400 mt-4">
          Bu işlem tüm yerel verileri temizleyecektir.
        </p>
      </div>
    </div>
  )
}
