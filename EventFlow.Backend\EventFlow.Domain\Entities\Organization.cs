using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EventFlow.Domain.Entities;

public class Organization : BaseEntity
{
    [Required]
    public Guid CustomerId { get; set; }
    
    [Required]
    public DateTime EventDate { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string Status { get; set; } = "Teklif"; // <PERSON><PERSON><PERSON><PERSON>, Kesinle<PERSON>ti, İptal
    
    [Range(0, 100)]
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; } = 0;
    
    [Required]
    [Range(0, double.MaxValue)]
    [Column(TypeName = "decimal(12,2)")]
    public decimal TotalAmount { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    [ForeignKey("CustomerId")]
    public virtual Customer Customer { get; set; } = null!;
    
    public virtual ICollection<OrganizationService> OrganizationServices { get; set; } = new List<OrganizationService>();
}
