import { useFormContext } from "react-hook-form";
import { motion } from "framer-motion";
import { User, Phone, Mail, AlertCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { cn } from "@/lib/utils";

export function CustomerStep() {
  const { control, formState: { errors } } = useFormContext();

  const inputVariants = {
    focus: { scale: 1.02, transition: { duration: 0.2 } },
    blur: { scale: 1, transition: { duration: 0.2 } }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <User className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900">Müşteri Bilgileri</h3>
        <p className="text-sm sm:text-base text-gray-600 px-2">Müşterinizin iletişim bilgilerini girin</p>
      </div>

      {/* Form Fields */}
      <div className="grid grid-cols-1 gap-4 sm:gap-6">
        {/* Full Name */}
        <FormField
          control={control}
          name="customer.fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center space-x-2 text-sm sm:text-base">
                <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
                <span>Ad Soyad</span>
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <motion.div
                  variants={inputVariants}
                  whileFocus="focus"
                  initial="blur"
                >
                  <Input
                    {...field}
                    placeholder="Müşteri adı soyadı"
                    className={cn(
                      "h-10 sm:h-12 text-sm sm:text-base transition-all duration-200",
                      "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                      errors.customer?.fullName && "border-red-500 focus:ring-red-500"
                    )}
                  />
                </motion.div>
              </FormControl>
              <FormMessage className="flex items-center space-x-1">
                {errors.customer?.fullName && (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                )}
              </FormMessage>
            </FormItem>
          )}
        />

        {/* Phone Number */}
        <FormField
          control={control}
          name="customer.phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>Telefon Numarası</span>
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <motion.div
                  variants={inputVariants}
                  whileFocus="focus"
                  initial="blur"
                >
                  <Input
                    {...field}
                    type="tel"
                    placeholder="0555 123 45 67"
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                      errors.customer?.phoneNumber && "border-red-500 focus:ring-red-500"
                    )}
                  />
                </motion.div>
              </FormControl>
              <FormMessage className="flex items-center space-x-1">
                {errors.customer?.phoneNumber && (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                )}
              </FormMessage>
            </FormItem>
          )}
        />

        {/* Email */}
        <FormField
          control={control}
          name="customer.email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <span>E-posta</span>
                <span className="text-gray-400 text-sm">(opsiyonel)</span>
              </FormLabel>
              <FormControl>
                <motion.div
                  variants={inputVariants}
                  whileFocus="focus"
                  initial="blur"
                >
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                      errors.customer?.email && "border-red-500 focus:ring-red-500"
                    )}
                  />
                </motion.div>
              </FormControl>
              <FormMessage className="flex items-center space-x-1">
                {errors.customer?.email && (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                )}
              </FormMessage>
            </FormItem>
          )}
        />
      </div>

      {/* Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-blue-900">İpuçları</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Telefon numarasını uluslararası format olmadan girin</li>
              <li>• E-posta adresi opsiyoneldir ancak iletişim için önerilir</li>
              <li>• Tüm bilgilerin doğru olduğundan emin olun</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>Adım 1/4 - Müşteri Bilgileri</span>
        </div>
      </div>
    </div>
  );
}
