import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OrganizationDetailsDto } from "../../types";
import { Edit } from "lucide-react";

const editOrganizationSchema = z.object({
  customerName: z.string().min(1, "Müşteri adı gereklidir"),
  customerEmail: z.string().email("Geçerli bir e-posta adresi giriniz").optional().or(z.literal("")),
  customerPhone: z.string().min(1, "Telefon numarası gereklidir"),
  eventDate: z.string().min(1, "Etkinlik tarihi gereklidir"),
  status: z.string().min(1, "Durum seçimi gereklidir"),
  discountPercentage: z.number().min(0).max(100),
  notes: z.string().optional(),
});

type EditOrganizationFormData = z.infer<typeof editOrganizationSchema>;

interface OrganizationEditModalProps {
  organization: OrganizationDetailsDto | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: EditOrganizationFormData) => void;
  isLoading?: boolean;
}

export function OrganizationEditModal({
  organization,
  open,
  onOpenChange,
  onSave,
  isLoading = false,
}: OrganizationEditModalProps) {
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<EditOrganizationFormData>({
    resolver: zodResolver(editOrganizationSchema),
  });

  const statusValue = watch("status");

  useEffect(() => {
    if (organization) {
      reset({
        customerName: organization.customerName,
        customerEmail: organization.customerEmail || "",
        customerPhone: organization.customerPhone || "",
        eventDate: new Date(organization.eventDate).toISOString().split('T')[0],
        status: organization.status,
        discountPercentage: organization.discountPercentage,
        notes: organization.notes || "",
      });
    }
  }, [organization, reset]);

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  const onSubmit = (data: EditOrganizationFormData) => {
    onSave(data);
  };

  if (!organization) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Organizasyon Düzenle
          </DialogTitle>
          <DialogDescription>
            Organizasyon bilgilerini güncelleyin
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Müşteri Bilgileri */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Müşteri Bilgileri</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customerName">Müşteri Adı *</Label>
                <Input
                  id="customerName"
                  {...register("customerName")}
                  placeholder="Müşteri adı"
                />
                {errors.customerName && (
                  <p className="text-sm text-red-500">{errors.customerName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="customerPhone">Telefon *</Label>
                <Input
                  id="customerPhone"
                  {...register("customerPhone")}
                  placeholder="Telefon numarası"
                />
                {errors.customerPhone && (
                  <p className="text-sm text-red-500">{errors.customerPhone.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="customerEmail">E-posta</Label>
              <Input
                id="customerEmail"
                type="email"
                {...register("customerEmail")}
                placeholder="E-posta adresi"
              />
              {errors.customerEmail && (
                <p className="text-sm text-red-500">{errors.customerEmail.message}</p>
              )}
            </div>
          </div>

          {/* Etkinlik Bilgileri */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Etkinlik Bilgileri</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="eventDate">Etkinlik Tarihi *</Label>
                <Input
                  id="eventDate"
                  type="date"
                  {...register("eventDate")}
                />
                {errors.eventDate && (
                  <p className="text-sm text-red-500">{errors.eventDate.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Durum *</Label>
                <Select value={statusValue} onValueChange={(value) => setValue("status", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Durum seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Teklif">Teklif</SelectItem>
                    <SelectItem value="Kesinleşti">Kesinleşti</SelectItem>
                    <SelectItem value="İptal">İptal</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="discountPercentage">İskonto Oranı (%)</Label>
              <Input
                id="discountPercentage"
                type="number"
                min="0"
                max="100"
                step="0.1"
                {...register("discountPercentage", { valueAsNumber: true })}
                placeholder=""
              />
              {errors.discountPercentage && (
                <p className="text-sm text-red-500">{errors.discountPercentage.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notlar</Label>
              <Textarea
                id="notes"
                {...register("notes")}
                placeholder="Organizasyon hakkında notlar..."
                rows={3}
              />
              {errors.notes && (
                <p className="text-sm text-red-500">{errors.notes.message}</p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              İptal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Kaydediliyor..." : "Kaydet"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
