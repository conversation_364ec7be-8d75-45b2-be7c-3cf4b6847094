import { useState, useEffect } from "react";
import { Calendar as BigCalendar, dateFnsLocalizer, Views } from "react-big-calendar";
import { format, parse, startOfWeek, getDay, addMonths, subMonths } from "date-fns";
import { tr } from "date-fns/locale";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { api } from "../../lib/api";
import { supabase } from "../../lib/supabase";
import { LoadingSpinner } from "../ui/LoadingSpinner";

// Create date-fns localizer with Turkish locale
const localizer = dateFnsLocalizer({
  format: (date: Date, formatStr: string) => format(date, formatStr, { locale: tr }),
  parse: (str: string, formatStr: string) => parse(str, formatStr, new Date(), { locale: tr }),
  startOfWeek: (date: Date) => startOfWeek(date, { locale: tr }),
  getDay: (date: Date) => getDay(date),
  locales: { tr },
});

const messages = {
  allDay: "Tüm Gün",
  previous: "Önceki",
  next: "Sonraki",
  today: "Bugün",
  month: "Ay",
  week: "Hafta",
  day: "Gün",
  agenda: "Ajanda",
  date: "Tarih",
  time: "Saat",
  event: "Etkinlik",
  noEventsInRange: "Bu tarih aralığında etkinlik yok",
  showMore: (total: number) => `+${total} daha`,
};

const formats = {
  monthHeaderFormat: "MMMM yyyy",
  dayHeaderFormat: "eeee, dd MMMM",
  dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
    `${format(start, "dd MMMM", { locale: tr })} - ${format(end, "dd MMMM yyyy", { locale: tr })}`,
  agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
    `${format(start, "dd MMMM", { locale: tr })} - ${format(end, "dd MMMM yyyy", { locale: tr })}`,
  agendaDateFormat: "eeee, dd MMMM",
  agendaTimeFormat: "HH:mm",
  agendaTimeRangeFormat: ({ start, end }: { start: Date; end: Date }) =>
    `${format(start, "HH:mm", { locale: tr })} - ${format(end, "HH:mm", { locale: tr })}`,
  weekHeaderFormat: "dd MMMM yyyy",
  dayFormat: "dd",
  dateFormat: "dd",
  timeGutterFormat: "HH:mm",
};

export function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const queryClient = useQueryClient();

  const { data: events, isLoading, error } = useQuery({
    queryKey: ["calendar"],
    queryFn: api.calendar.getEvents,
    staleTime: 10 * 1000, // 10 saniye fresh tut
    refetchOnWindowFocus: true,
  });

  // Realtime subscription for calendar events
  useEffect(() => {
    const channel = supabase
      .channel('calendar-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'eventflow_organizations'
        },
        (payload) => {
          console.log('🔄 Calendar realtime update:', payload);
          // Invalidate calendar queries to refetch data
          queryClient.invalidateQueries({ queryKey: ["/api/organizations/calendar"] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-96">
        <p className="text-red-500">Ajanda yüklenirken hata oluştu</p>
      </div>
    );
  }

  const calendarEvents = events?.map(event => ({
    ...event,
    start: new Date(event.start),
    end: new Date(event.end),
  })) || [];

  const eventStyleGetter = (event: any) => {
    let backgroundColor = "#3b82f6";
    
    if (event.status === "Kesinleşti") {
      backgroundColor = "#10b981";
    } else if (event.status === "İptal") {
      backgroundColor = "#ef4444";
    }

    return {
      style: {
        backgroundColor,
        borderRadius: "4px",
        opacity: 0.8,
        color: "white",
        border: "0px",
        display: "block",
      },
    };
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-3 sm:p-6">
      {/* Mobile-first responsive calendar */}
      <div className="h-[500px] sm:h-[600px] lg:h-[700px]">
        <BigCalendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          messages={messages}
          formats={formats}
          eventPropGetter={eventStyleGetter}
          views={[Views.MONTH, Views.WEEK, Views.DAY]}
          defaultView={Views.MONTH}
          date={currentDate}
          onNavigate={(date) => setCurrentDate(date)}
          popup
          showMultiDayTimes
          culture="tr-TR"
          components={{
            toolbar: ({ onNavigate, onView, view }) => (
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
                {/* Navigation and Title */}
                <div className="flex items-center justify-between w-full sm:w-auto">
                  <div className="flex gap-1 sm:gap-2">
                    <button
                      onClick={() => {
                        const newDate = subMonths(currentDate, 1);
                        setCurrentDate(newDate);
                        onNavigate("PREV");
                      }}
                      className="px-2 sm:px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs sm:text-sm"
                    >
                      <span className="hidden sm:inline">Önceki</span>
                      <span className="sm:hidden">←</span>
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        setCurrentDate(today);
                        onNavigate("TODAY");
                      }}
                      className="px-2 sm:px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs sm:text-sm"
                    >
                      Bugün
                    </button>
                    <button
                      onClick={() => {
                        const newDate = addMonths(currentDate, 1);
                        setCurrentDate(newDate);
                        onNavigate("NEXT");
                      }}
                      className="px-2 sm:px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs sm:text-sm"
                    >
                      <span className="hidden sm:inline">Sonraki</span>
                      <span className="sm:hidden">→</span>
                    </button>
                  </div>
                </div>

                {/* Title */}
                <h2 className="text-lg sm:text-xl font-semibold text-center w-full sm:w-auto">
                  {format(currentDate, "MMMM yyyy", { locale: tr })}
                </h2>

                {/* View Buttons */}
                <div className="flex gap-1 sm:gap-2 w-full sm:w-auto">
                  <button
                    onClick={() => onView("month")}
                    className={`flex-1 sm:flex-none px-2 sm:px-3 py-1 rounded text-xs sm:text-sm ${view === "month" ? "bg-blue-500 text-white" : "bg-gray-100 hover:bg-gray-200"}`}
                  >
                    Ay
                  </button>
                  <button
                    onClick={() => onView("week")}
                    className={`flex-1 sm:flex-none px-2 sm:px-3 py-1 rounded text-xs sm:text-sm ${view === "week" ? "bg-blue-500 text-white" : "bg-gray-100 hover:bg-gray-200"}`}
                  >
                    Hafta
                  </button>
                  <button
                    onClick={() => onView("day")}
                    className={`flex-1 sm:flex-none px-2 sm:px-3 py-1 rounded text-xs sm:text-sm ${view === "day" ? "bg-blue-500 text-white" : "bg-gray-100 hover:bg-gray-200"}`}
                  >
                    Gün
                  </button>
                </div>
              </div>
            ),
          }}
        />
      </div>
    </div>
  );
}
