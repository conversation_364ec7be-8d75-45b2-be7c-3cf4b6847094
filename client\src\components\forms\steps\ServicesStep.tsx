import { useFormContext } from "react-hook-form";
import { motion } from "framer-motion";
import { Package, Check, AlertCircle, TrendingUp, Search, Filter, Grid, List, ChevronDown, ChevronUp } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useServices } from "../../../hooks/useServices";
import { LoadingSpinner } from "../../ui/LoadingSpinner";
import { cn } from "@/lib/utils";
import { useState, useMemo } from "react";

// Service categories mapping
const SERVICE_CATEGORIES = [
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Se<PERSON>",
  "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>ğlence",
  "Mekan ve Ekipman",
  "Personel",
  "Dekorasyon",
  "Animasyon ve Eğlence",
  "Efekt ve Show",
  "Matbaa ve Davetiye",
  "Ulaşım",
  "Diğer"
];

export function ServicesStep() {
  const { control, formState: { errors }, watch, setValue } = useFormContext();
  const { services, isLoading } = useServices();

  const selectedServices = watch("services.selectedServices") || [];
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(SERVICE_CATEGORIES));

  const handleServiceToggle = (serviceId: string, checked: boolean) => {
    const currentServices = selectedServices;
    if (checked) {
      setValue("services.selectedServices", [...currentServices, serviceId]);
    } else {
      setValue("services.selectedServices", currentServices.filter((id: string) => id !== serviceId));
    }
  };

  const calculateTotal = () => {
    if (!services) return 0;
    return selectedServices.reduce((total: number, serviceId: string) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service?.basePrice || 0);
    }, 0);
  };

  // Group services by category and apply filters
  const groupedServices = useMemo(() => {
    if (!services) return {};

    // First filter by search term
    const filteredServices = services.filter(service =>
      service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Group by category (assuming services have a category field, if not we'll use a default)
    const grouped = filteredServices.reduce((acc, service) => {
      // Try to match service name with categories or use "Diğer" as default
      let category = "Diğer";

      // Simple category matching based on service name keywords
      const serviceName = service.name.toLowerCase();
      if (serviceName.includes("kamera") || serviceName.includes("fotoğraf") || serviceName.includes("drone") || serviceName.includes("çekim")) {
        category = "Görüntü ve Ses";
      } else if (serviceName.includes("orkestra") || serviceName.includes("bando") || serviceName.includes("davul") || serviceName.includes("ses") || serviceName.includes("dj")) {
        category = "Müzik ve Eğlence";
      } else if (serviceName.includes("sandalye") || serviceName.includes("masa") || serviceName.includes("çadır") || serviceName.includes("kazan") || serviceName.includes("projektör") || serviceName.includes("lamba")) {
        category = "Mekan ve Ekipman";
      } else if (serviceName.includes("host") || serviceName.includes("garson") || serviceName.includes("personel")) {
        category = "Personel";
      } else if (serviceName.includes("balon") || serviceName.includes("süsleme") || serviceName.includes("giriş") || serviceName.includes("masa") && serviceName.includes("gelin")) {
        category = "Dekorasyon";
      } else if (serviceName.includes("animasyon") || serviceName.includes("palyaço") || serviceName.includes("oyun")) {
        category = "Animasyon ve Eğlence";
      } else if (serviceName.includes("sis") || serviceName.includes("show") || serviceName.includes("efekt")) {
        category = "Efekt ve Show";
      } else if (serviceName.includes("davetiye") || serviceName.includes("tag") || serviceName.includes("matbaa")) {
        category = "Matbaa ve Davetiye";
      } else if (serviceName.includes("araç") || serviceName.includes("ulaşım")) {
        category = "Ulaşım";
      }

      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(service);
      return acc;
    }, {} as Record<string, typeof services>);

    // Filter by selected category
    if (selectedCategory !== "all") {
      return { [selectedCategory]: grouped[selectedCategory] || [] };
    }

    return grouped;
  }, [services, searchTerm, selectedCategory]);

  const total = calculateTotal();

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <Package className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900">Hizmet Seçimi</h3>
        <p className="text-sm sm:text-base text-gray-600 px-2">Sunmak istediğiniz hizmetleri seçin</p>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Hizmet ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 h-12 border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory("all")}
              className={cn(
                "transition-all duration-200",
                selectedCategory === "all" && "bg-purple-600 hover:bg-purple-700"
              )}
            >
              Tümü
            </Button>
            {SERVICE_CATEGORIES.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  "transition-all duration-200 text-xs",
                  selectedCategory === category && "bg-purple-600 hover:bg-purple-700"
                )}
              >
                {category}
              </Button>
            ))}
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className={cn(
                "transition-all duration-200",
                viewMode === "grid" && "bg-purple-600 hover:bg-purple-700"
              )}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
              className={cn(
                "transition-all duration-200",
                viewMode === "list" && "bg-purple-600 hover:bg-purple-700"
              )}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Services by Category */}
      <FormField
        control={control}
        name="services.selectedServices"
        render={() => (
          <FormItem>
            <FormControl>
              <div className="space-y-6">
                {Object.entries(groupedServices).map(([category, categoryServices]) => (
                  <div key={category} className="space-y-3">
                    {/* Category Header */}
                    <div
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors duration-200"
                      onClick={() => toggleCategory(category)}
                    >
                      <div className="flex items-center space-x-3">
                        <h4 className="font-semibold text-gray-900">{category}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {categoryServices.length} hizmet
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {categoryServices.filter(s => selectedServices.includes(s.id)).length} seçili
                        </Badge>
                      </div>
                      {expandedCategories.has(category) ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </div>

                    {/* Category Services */}
                    {expandedCategories.has(category) && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className={cn(
                          "grid gap-3",
                          viewMode === "grid"
                            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                            : "grid-cols-1"
                        )}
                      >
                        {categoryServices.map((service) => {
                          const isSelected = selectedServices.includes(service.id);

                          return (
                            <motion.div
                              key={service.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3 }}
                              className={cn(
                                "relative border-2 rounded-lg cursor-pointer transition-all duration-200",
                                "hover:shadow-md hover:scale-[1.02]",
                                viewMode === "grid" ? "p-4" : "p-3",
                                isSelected
                                  ? "border-purple-500 bg-purple-50 shadow-md"
                                  : "border-gray-200 bg-white hover:border-gray-300"
                              )}
                              onClick={() => handleServiceToggle(service.id, !isSelected)}
                            >
                              {/* Selection Indicator */}
                              <div className={cn(
                                "absolute top-3 right-3 w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200",
                                isSelected
                                  ? "border-purple-500 bg-purple-500"
                                  : "border-gray-300 bg-white"
                              )}>
                                {isSelected && (
                                  <Check className="w-4 h-4 text-white" />
                                )}
                              </div>

                              {/* Service Content */}
                              <div className={cn(
                                "space-y-2 pr-8",
                                viewMode === "list" && "flex items-center justify-between pr-12"
                              )}>
                                <div className={cn(viewMode === "list" && "flex-1")}>
                                  <h4 className={cn(
                                    "font-semibold transition-colors duration-200",
                                    viewMode === "grid" ? "text-sm" : "text-base",
                                    isSelected ? "text-purple-900" : "text-gray-900"
                                  )}>
                                    {service.name}
                                  </h4>
                                  {viewMode === "grid" && (
                                    <p className={cn(
                                      "text-xs transition-colors duration-200",
                                      isSelected ? "text-purple-700" : "text-gray-600"
                                    )}>
                                      {service.description}
                                    </p>
                                  )}
                                </div>

                                <div className={cn(
                                  "flex items-center",
                                  viewMode === "grid" ? "justify-between" : "space-x-3"
                                )}>
                                  <Badge
                                    variant={isSelected ? "default" : "secondary"}
                                    className={cn(
                                      "transition-all duration-200",
                                      viewMode === "list" ? "text-sm" : "text-xs",
                                      isSelected && "bg-purple-600 hover:bg-purple-700"
                                    )}
                                  >
                                    ₺{service.basePrice.toLocaleString('tr-TR')}
                                  </Badge>

                                  {service.isActive && viewMode === "grid" && (
                                    <Badge variant="outline" className="text-xs">
                                      Aktif
                                    </Badge>
                                  )}
                                </div>
                              </div>

                              {/* Hover Effect */}
                              <motion.div
                                className={cn(
                                  "absolute inset-0 rounded-lg transition-opacity duration-200",
                                  isSelected
                                    ? "bg-purple-500/5"
                                    : "bg-gray-500/0 hover:bg-gray-500/5"
                                )}
                                whileHover={{ opacity: 1 }}
                              />
                            </motion.div>
                          );
                        })}
                      </motion.div>
                    )}
                  </div>
                ))}
              </div>
            </FormControl>
            <FormMessage className="flex items-center space-x-1">
              {errors.services?.selectedServices && (
                <>
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span>{errors.services.selectedServices.message}</span>
                </>
              )}
            </FormMessage>
          </FormItem>
        )}
      />

      {/* Selected Services Summary */}
      {selectedServices.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-purple-50 border border-purple-200 rounded-lg p-4"
        >
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-purple-900">Seçilen Hizmetler</h4>
            <Badge variant="secondary">
              {selectedServices.length} hizmet
            </Badge>
          </div>
          
          <div className="space-y-2">
            {selectedServices.map((serviceId: string) => {
              const service = services?.find(s => s.id === serviceId);
              if (!service) return null;
              
              return (
                <div key={serviceId} className="flex items-center justify-between text-sm">
                  <span className="text-purple-800">{service.name}</span>
                  <span className="font-medium text-purple-900">
                    ₺{service.basePrice.toLocaleString('tr-TR')}
                  </span>
                </div>
              );
            })}
            
            <div className="border-t border-purple-200 pt-2 mt-3">
              <div className="flex items-center justify-between font-semibold">
                <span className="text-purple-900">Toplam</span>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                  <span className="text-purple-900">
                    ₺{total.toLocaleString('tr-TR')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Quick Stats */}
      {Object.keys(groupedServices).length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-blue-900">{services?.length || 0}</div>
            <div className="text-xs text-blue-700">Toplam Hizmet</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-green-900">{selectedServices.length}</div>
            <div className="text-xs text-green-700">Seçilen</div>
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-orange-900">{Object.keys(groupedServices).length}</div>
            <div className="text-xs text-orange-700">Kategori</div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
            <div className="text-lg font-bold text-purple-900">₺{total.toLocaleString('tr-TR')}</div>
            <div className="text-xs text-purple-700">Toplam</div>
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">💡</span>
          </div>
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-purple-900">İpuçları</h4>
            <ul className="text-sm text-purple-800 space-y-1">
              <li>• Arama kutusunu kullanarak hızlıca hizmet bulabilirsiniz</li>
              <li>• Kategorilere tıklayarak hizmetleri gizleyebilir/gösterebilirsiniz</li>
              <li>• Grid/Liste görünümü arasında geçiş yapabilirsiniz</li>
              <li>• En az bir hizmet seçmelisiniz</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
          <span>Adım 3/4 - Hizmet Seçimi</span>
        </div>
      </div>
    </div>
  );
}
