// Simple services function without Supabase for testing
// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

// JWT decode function for extracting user ID from token
function decodeJWT(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('JWT decode error:', error);
    return null;
  }
}

// Extract user ID from Authorization header
function getUserIdFromAuth(event) {
  const authHeader = event.headers.authorization || event.headers.Authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = decodeJWT(token);
  return payload?.sub || null;
}

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod === 'GET') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      // Fetch real data from Supabase with user filter
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services?select=*&user_id=eq.${userId}&order=created_at.desc`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      const supabaseServices = await response.json();

      // Convert Supabase format to frontend format
      const services = supabaseServices.map(service => ({
        id: service.id,
        name: service.name,
        description: service.description,
        basePrice: parseFloat(service.base_price),
        isActive: service.is_active,
        createdAt: service.created_at
      }));

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(services),
      };
    } catch (error) {
      console.error('Error fetching services:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Hizmetler yüklenirken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'POST') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      const requestData = JSON.parse(event.body);

      // Create new service in Supabase with user_id
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services`, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: JSON.stringify({
          name: requestData.name,
          description: requestData.description,
          base_price: requestData.basePrice,
          is_active: requestData.isActive !== false,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      const newService = await response.json();

      // Convert to frontend format
      const service = {
        id: newService[0].id,
        name: newService[0].name,
        description: newService[0].description,
        basePrice: parseFloat(newService[0].base_price),
        isActive: newService[0].is_active,
        createdAt: newService[0].created_at
      };

      return {
        statusCode: 201,
        headers,
        body: JSON.stringify(service),
      };
    } catch (error) {
      console.error('Error creating service:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Hizmet oluşturulurken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'PUT') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      const requestData = JSON.parse(event.body);
      // Get service ID from URL path
      const pathParts = (event.rawUrl || event.path || '').split('/');
      const serviceId = pathParts[pathParts.length - 1];

      // Update service in Supabase with user_id filter for security
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services?id=eq.${serviceId}&user_id=eq.${userId}`, {
        method: 'PATCH',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: JSON.stringify({
          name: requestData.name,
          description: requestData.description,
          base_price: requestData.basePrice,
          is_active: requestData.isActive,
          updated_at: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      const updatedService = await response.json();

      if (updatedService.length === 0) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ message: 'Hizmet bulunamadı' }),
        };
      }

      // Convert to frontend format
      const service = {
        id: updatedService[0].id,
        name: updatedService[0].name,
        description: updatedService[0].description,
        basePrice: parseFloat(updatedService[0].base_price),
        isActive: updatedService[0].is_active,
        createdAt: updatedService[0].created_at
      };

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(service),
      };
    } catch (error) {
      console.error('Error updating service:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Hizmet güncellenirken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'DELETE') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      // Get service ID from URL path
      const pathParts = (event.rawUrl || event.path || '').split('/');
      const serviceId = pathParts[pathParts.length - 1];

      // Delete service from Supabase with user_id filter for security
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services?id=eq.${serviceId}&user_id=eq.${userId}`, {
        method: 'DELETE',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ message: 'Hizmet başarıyla silindi' }),
      };
    } catch (error) {
      console.error('Error deleting service:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Hizmet silinirken hata oluştu' }),
      };
    }
  }

  return {
    statusCode: 405,
    headers,
    body: JSON.stringify({ message: 'Method not allowed' }),
  };
};
