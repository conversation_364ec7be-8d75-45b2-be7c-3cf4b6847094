using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using EventFlow.Shared.DTOs;
using Microsoft.AspNetCore.Http;
using EventFlow.Infrastructure.Extensions;

namespace EventFlow.Infrastructure.Services;

public class SupabaseService
{
    private readonly HttpClient _httpClient;
    private readonly string _supabaseUrl;
    private readonly string _supabaseKey;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SupabaseService(HttpClient httpClient, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
    {
        _httpClient = httpClient;
        _httpContextAccessor = httpContextAccessor;
        _supabaseUrl = "https://yqnhitvatsnrjdabsgzv.supabase.co";
        _supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc";

        _httpClient.BaseAddress = new Uri(_supabaseUrl);
        _httpClient.DefaultRequestHeaders.Add("apikey", _supabaseKey);
        // Authorization header'ını her request'te dinamik olarak ekleyeceğiz
        _httpClient.DefaultRequestHeaders.Add("Prefer", "return=representation");
    }

    private string? GetCurrentUserId()
    {
        return _httpContextAccessor.HttpContext?.GetUserId();
    }

    private string? GetCurrentUserToken()
    {
        var authHeader = _httpContextAccessor.HttpContext?.Request.Headers["Authorization"].FirstOrDefault();
        if (authHeader?.StartsWith("Bearer ") == true)
        {
            return authHeader.Substring("Bearer ".Length);
        }
        return null;
    }

    private HttpClient GetAuthenticatedHttpClient()
    {
        var userToken = GetCurrentUserToken();
        if (!string.IsNullOrEmpty(userToken))
        {
            // Kullanıcının JWT token'ını kullan (RLS için gerekli)
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {userToken}");
        }
        else
        {
            // Fallback olarak anon key kullan
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_supabaseKey}");
        }
        return _httpClient;
    }

    // Services CRUD Operations
    public async Task<List<ServiceDto>> GetServicesAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.GetAsync($"/rest/v1/eventflow_services?user_id=eq.{userId}&select=*");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            var services = JsonSerializer.Deserialize<List<SupabaseServiceModel>>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return services?.Select(s => new ServiceDto
            {
                Id = s.Id,
                Name = s.Name,
                Description = s.Description,
                BasePrice = s.BasePrice,
                IsActive = s.IsActive,
                CreatedAt = s.CreatedAt,
                UpdatedAt = s.UpdatedAt
            }).ToList() ?? new List<ServiceDto>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error fetching services: {ex.Message}", ex);
        }
    }

    public async Task<ServiceDto?> GetServiceByIdAsync(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.GetAsync($"/rest/v1/eventflow_services?id=eq.{id}&user_id=eq.{userId}&select=*");
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync();
            var services = JsonSerializer.Deserialize<List<SupabaseServiceModel>>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var service = services?.FirstOrDefault();
            if (service == null) return null;

            return new ServiceDto
            {
                Id = service.Id,
                Name = service.Name,
                Description = service.Description,
                BasePrice = service.BasePrice,
                IsActive = service.IsActive,
                CreatedAt = service.CreatedAt,
                UpdatedAt = service.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"Error fetching service: {ex.Message}", ex);
        }
    }

    public async Task<ServiceDto> CreateServiceAsync(CreateServiceDto createServiceDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var supabaseModel = new SupabaseServiceModel
            {
                Id = Guid.NewGuid(),
                Name = createServiceDto.Name,
                Description = createServiceDto.Description,
                BasePrice = createServiceDto.BasePrice,
                IsActive = createServiceDto.IsActive,
                UserId = Guid.Parse(userId),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(supabaseModel, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.PostAsync("/rest/v1/eventflow_services", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var createdServices = JsonSerializer.Deserialize<List<SupabaseServiceModel>>(responseJson, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var createdService = createdServices?.FirstOrDefault();
            if (createdService == null)
                throw new Exception("Failed to create service");

            return new ServiceDto
            {
                Id = createdService.Id,
                Name = createdService.Name,
                Description = createdService.Description,
                BasePrice = createdService.BasePrice,
                IsActive = createdService.IsActive,
                CreatedAt = createdService.CreatedAt,
                UpdatedAt = createdService.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"Error creating service: {ex.Message}", ex);
        }
    }

    public async Task<ServiceDto?> UpdateServiceAsync(Guid id, UpdateServiceDto updateServiceDto)
    {
        try
        {
            var updateData = new Dictionary<string, object>();
            
            if (!string.IsNullOrEmpty(updateServiceDto.Name))
                updateData["name"] = updateServiceDto.Name;
            
            if (updateServiceDto.Description != null)
                updateData["description"] = updateServiceDto.Description;
            
            if (updateServiceDto.BasePrice.HasValue)
                updateData["base_price"] = updateServiceDto.BasePrice.Value;
            
            if (updateServiceDto.IsActive.HasValue)
                updateData["is_active"] = updateServiceDto.IsActive.Value;

            updateData["updated_at"] = DateTime.UtcNow;

            var json = JsonSerializer.Serialize(updateData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.PatchAsync($"/rest/v1/eventflow_services?id=eq.{id}", content);
            response.EnsureSuccessStatusCode();

            var responseJson = await response.Content.ReadAsStringAsync();
            var updatedServices = JsonSerializer.Deserialize<List<SupabaseServiceModel>>(responseJson, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var updatedService = updatedServices?.FirstOrDefault();
            if (updatedService == null) return null;

            return new ServiceDto
            {
                Id = updatedService.Id,
                Name = updatedService.Name,
                Description = updatedService.Description,
                BasePrice = updatedService.BasePrice,
                IsActive = updatedService.IsActive,
                CreatedAt = updatedService.CreatedAt,
                UpdatedAt = updatedService.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"Error updating service: {ex.Message}", ex);
        }
    }

    public async Task<bool> DeleteServiceAsync(Guid id)
    {
        try
        {
            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.DeleteAsync($"/rest/v1/eventflow_services?id=eq.{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            throw new Exception($"Error deleting service: {ex.Message}", ex);
        }
    }

    // Organizations
    public async Task<List<OrganizationDto>> GetOrganizationsAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.GetAsync($"/rest/v1/eventflow_organization_details?user_id=eq.{userId}&select=*");
            response.EnsureSuccessStatusCode();

            var jsonResponse = await response.Content.ReadAsStringAsync();
            var organizations = JsonSerializer.Deserialize<List<SupabaseOrganizationModel>>(jsonResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return organizations?.Select(org => new OrganizationDto
            {
                Id = org.Id,
                CustomerId = org.CustomerId,
                CustomerName = org.CustomerName,
                CustomerEmail = org.CustomerEmail,
                CustomerPhone = org.CustomerPhone,
                EventDate = org.EventDate,
                Status = org.Status,
                DiscountPercentage = org.DiscountPercentage,
                TotalAmount = org.TotalAmount,
                Notes = org.Notes,
                CreatedAt = org.CreatedAt,
                UpdatedAt = org.UpdatedAt,
                Services = org.Services?.Select(s => new OrganizationServiceDto
                {
                    ServiceId = s.ServiceId,
                    ServiceName = s.ServiceName,
                    Quantity = s.Quantity,
                    UnitPrice = s.UnitPrice,
                    TotalPrice = s.TotalPrice
                }).ToList() ?? new List<OrganizationServiceDto>()
            }).ToList() ?? new List<OrganizationDto>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error fetching organizations: {ex.Message}", ex);
        }
    }

    // Calendar Events
    public async Task<List<CalendarEventDto>> GetCalendarEventsAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.GetAsync($"/rest/v1/eventflow_organizations?user_id=eq.{userId}&select=*,eventflow_customers!inner(name)");
            response.EnsureSuccessStatusCode();

            var jsonResponse = await response.Content.ReadAsStringAsync();
            var organizations = JsonSerializer.Deserialize<List<SupabaseOrganizationWithCustomerModel>>(jsonResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return organizations?.Select(org => new CalendarEventDto
            {
                Id = org.Id,
                Title = $"{org.EventflowCustomers?.Name ?? "Müşteri"} - {org.Status}",
                StartDate = org.EventDate,
                EndDate = org.EventDate.AddHours(3), // Default 3 saat etkinlik süresi
                Status = org.Status ?? "Teklif",
                TotalAmount = org.TotalAmount ?? 0,
                CustomerName = org.EventflowCustomers?.Name ?? "Bilinmeyen Müşteri"
            }).ToList() ?? new List<CalendarEventDto>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error fetching calendar events: {ex.Message}", ex);
        }
    }

    // Customers
    public async Task<List<CustomerDto>> GetCustomersAsync()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.GetAsync($"/rest/v1/eventflow_customers?user_id=eq.{userId}&select=*");
            response.EnsureSuccessStatusCode();

            var jsonResponse = await response.Content.ReadAsStringAsync();
            var customers = JsonSerializer.Deserialize<List<SupabaseCustomerModel>>(jsonResponse, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            return customers?.Select(c => new CustomerDto
            {
                Id = c.Id,
                Name = c.Name,
                Email = c.Email,
                Phone = c.Phone,
                CreatedAt = c.CreatedAt,
                UpdatedAt = c.UpdatedAt
            }).ToList() ?? new List<CustomerDto>();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error fetching customers: {ex.Message}", ex);
        }
    }

    // Create organization
    public async Task<OrganizationDto> CreateOrganizationAsync(CreateOrganizationDto createOrganizationDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            // First create customer
            var customerId = Guid.NewGuid();

            // Email alanı boş ise null olarak gönder
            var emailValue = string.IsNullOrWhiteSpace(createOrganizationDto.Customer.Email)
                ? null
                : createOrganizationDto.Customer.Email;

            var customerData = new Dictionary<string, object>
            {
                ["id"] = customerId,
                ["name"] = createOrganizationDto.Customer.FullName,
                ["phone"] = createOrganizationDto.Customer.PhoneNumber,
                ["user_id"] = userId,
                ["created_at"] = DateTime.UtcNow,
                ["updated_at"] = DateTime.UtcNow
            };

            // Email değeri varsa ekle
            if (emailValue != null)
            {
                customerData["email"] = emailValue;
            }

            var customerJson = JsonSerializer.Serialize(customerData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var httpClient = GetAuthenticatedHttpClient();
            var customerContent = new StringContent(customerJson, Encoding.UTF8, "application/json");
            var customerResponse = await httpClient.PostAsync("/rest/v1/eventflow_customers", customerContent);
            customerResponse.EnsureSuccessStatusCode();

            // Then create organization
            var organizationId = Guid.NewGuid();
            var organizationData = new Dictionary<string, object>
            {
                ["id"] = organizationId,
                ["customer_id"] = customerId,
                ["event_date"] = createOrganizationDto.EventDate,
                ["status"] = createOrganizationDto.Status,
                ["discount_percentage"] = createOrganizationDto.DiscountPercentage,
                ["total_amount"] = createOrganizationDto.TotalAmount,
                ["notes"] = createOrganizationDto.Notes ?? "",
                ["user_id"] = userId,
                ["created_at"] = DateTime.UtcNow,
                ["updated_at"] = DateTime.UtcNow
            };

            var organizationJson = JsonSerializer.Serialize(organizationData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var organizationContent = new StringContent(organizationJson, Encoding.UTF8, "application/json");
            var organizationResponse = await httpClient.PostAsync("/rest/v1/eventflow_organizations", organizationContent);
            organizationResponse.EnsureSuccessStatusCode();

            // Create organization services if provided
            if (createOrganizationDto.Services != null && createOrganizationDto.Services.Count > 0)
            {
                var serviceData = createOrganizationDto.Services.Select(service => new Dictionary<string, object>
                {
                    ["organization_id"] = organizationId,
                    ["service_id"] = service.ServiceId,
                    ["quantity"] = service.Quantity,
                    ["unit_price"] = service.UnitPrice,
                    ["user_id"] = userId,
                    ["created_at"] = DateTime.UtcNow,
                    ["updated_at"] = DateTime.UtcNow
                }).ToList();

                var servicesJson = JsonSerializer.Serialize(serviceData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });

                var servicesContent = new StringContent(servicesJson, Encoding.UTF8, "application/json");
                var servicesResponse = await httpClient.PostAsync("/rest/v1/eventflow_organization_services", servicesContent);
                servicesResponse.EnsureSuccessStatusCode();
            }

            // Return created organization
            return new OrganizationDto
            {
                Id = organizationId,
                CustomerId = customerId,
                CustomerName = createOrganizationDto.Customer.FullName,
                CustomerEmail = createOrganizationDto.Customer.Email,
                CustomerPhone = createOrganizationDto.Customer.PhoneNumber,
                EventDate = createOrganizationDto.EventDate,
                Status = createOrganizationDto.Status,
                DiscountPercentage = createOrganizationDto.DiscountPercentage,
                TotalAmount = createOrganizationDto.TotalAmount,
                Notes = createOrganizationDto.Notes,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Services = new List<OrganizationServiceDto>()
            };
        }
        catch (Exception ex)
        {
            throw new Exception($"Error creating organization: {ex.Message}", ex);
        }
    }

    // Update organization
    public async Task<OrganizationDto> UpdateOrganizationAsync(Guid id, UpdateOrganizationDto updateOrganizationDto)
    {
        try
        {
            // First get the organization to find customer ID
            var organizations = await GetOrganizationsAsync();
            var organization = organizations.FirstOrDefault(o => o.Id == id);
            if (organization == null)
                throw new Exception("Organization not found");

            // Update customer information if provided
            if (!string.IsNullOrEmpty(updateOrganizationDto.CustomerName) ||
                !string.IsNullOrEmpty(updateOrganizationDto.CustomerEmail) ||
                !string.IsNullOrEmpty(updateOrganizationDto.CustomerPhone))
            {
                var customerUpdateData = new Dictionary<string, object>
                {
                    ["updated_at"] = DateTime.UtcNow
                };

                if (!string.IsNullOrEmpty(updateOrganizationDto.CustomerName))
                    customerUpdateData["name"] = updateOrganizationDto.CustomerName;

                if (!string.IsNullOrEmpty(updateOrganizationDto.CustomerEmail))
                    customerUpdateData["email"] = updateOrganizationDto.CustomerEmail;

                if (!string.IsNullOrEmpty(updateOrganizationDto.CustomerPhone))
                    customerUpdateData["phone"] = updateOrganizationDto.CustomerPhone;

                var customerJson = JsonSerializer.Serialize(customerUpdateData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });

                var httpClient = GetAuthenticatedHttpClient();
                var customerContent = new StringContent(customerJson, Encoding.UTF8, "application/json");
                var customerResponse = await httpClient.PatchAsync($"/rest/v1/eventflow_customers?id=eq.{organization.CustomerId}", customerContent);
                customerResponse.EnsureSuccessStatusCode();
            }

            // Update organization information
            var updateData = new Dictionary<string, object>
            {
                ["updated_at"] = DateTime.UtcNow
            };

            if (updateOrganizationDto.EventDate.HasValue)
                updateData["event_date"] = updateOrganizationDto.EventDate.Value;

            if (!string.IsNullOrEmpty(updateOrganizationDto.Status))
                updateData["status"] = updateOrganizationDto.Status;

            if (updateOrganizationDto.DiscountPercentage.HasValue)
                updateData["discount_percentage"] = updateOrganizationDto.DiscountPercentage.Value;

            if (!string.IsNullOrEmpty(updateOrganizationDto.Notes))
                updateData["notes"] = updateOrganizationDto.Notes;

            var json = JsonSerializer.Serialize(updateData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var httpClient = GetAuthenticatedHttpClient();
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await httpClient.PatchAsync($"/rest/v1/eventflow_organizations?id=eq.{id}", content);
            response.EnsureSuccessStatusCode();

            // Return updated organization
            var updatedOrganizations = await GetOrganizationsAsync();
            return updatedOrganizations.FirstOrDefault(o => o.Id == id) ?? throw new Exception("Organization not found after update");
        }
        catch (Exception ex)
        {
            throw new Exception($"Error updating organization: {ex.Message}", ex);
        }
    }

    // Update organization status
    public async Task UpdateOrganizationStatusAsync(Guid id, string status)
    {
        try
        {
            var updateData = new Dictionary<string, object>
            {
                ["status"] = status,
                ["updated_at"] = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(updateData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var httpClient = GetAuthenticatedHttpClient();
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await httpClient.PatchAsync($"/rest/v1/eventflow_organizations?id=eq.{id}", content);
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error updating organization status: {ex.Message}", ex);
        }
    }

    // Delete organization
    public async Task DeleteOrganizationAsync(Guid id)
    {
        try
        {
            var httpClient = GetAuthenticatedHttpClient();
            var response = await httpClient.DeleteAsync($"/rest/v1/eventflow_organizations?id=eq.{id}");
            response.EnsureSuccessStatusCode();
        }
        catch (Exception ex)
        {
            throw new Exception($"Error deleting organization: {ex.Message}", ex);
        }
    }
}

// Supabase models for JSON serialization
public class SupabaseServiceModel
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal BasePrice { get; set; }
    public bool IsActive { get; set; }
    public Guid? UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SupabaseOrganizationModel
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public DateTime EventDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal DiscountPercentage { get; set; }
    public decimal TotalAmount { get; set; }
    public string? Notes { get; set; }
    public Guid? UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<SupabaseOrganizationServiceModel>? Services { get; set; }
}

public class SupabaseOrganizationServiceModel
{
    public Guid ServiceId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
}

public class SupabaseCalendarEventModel
{
    public Guid Id { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string CustomerName { get; set; } = string.Empty;
}

public class SupabaseCustomerModel
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public Guid? UserId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SupabaseOrganizationWithCustomerModel
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public DateTime EventDate { get; set; }
    public string? Status { get; set; }
    public decimal? DiscountPercentage { get; set; }
    public decimal? TotalAmount { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public SupabaseCustomerModel? EventflowCustomers { get; set; }
}
