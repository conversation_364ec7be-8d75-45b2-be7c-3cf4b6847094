namespace EventFlow.Shared.DTOs;

public class OrganizationDto
{
    public Guid Id { get; set; }
    public Guid CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string? CustomerEmail { get; set; }
    public string? CustomerPhone { get; set; }
    public DateTime EventDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public decimal DiscountPercentage { get; set; }
    public decimal TotalAmount { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public List<OrganizationServiceDto> Services { get; set; } = new();
}

public class CreateOrganizationDto
{
    public CreateCustomerDto Customer { get; set; } = new();
    public DateTime EventDate { get; set; }
    public string Status { get; set; } = "Teklif";
    public decimal DiscountPercentage { get; set; } = 0;
    public decimal TotalAmount { get; set; } = 0;
    public string? Notes { get; set; }
    public List<OrganizationServiceCreateDto> Services { get; set; } = new();
}

public class OrganizationServiceCreateDto
{
    public string ServiceId { get; set; } = string.Empty;
    public int Quantity { get; set; } = 1;
    public decimal UnitPrice { get; set; } = 0;
}

public class UpdateOrganizationDto
{
    public string? CustomerName { get; set; }
    public string? CustomerEmail { get; set; }
    public string? CustomerPhone { get; set; }
    public DateTime? EventDate { get; set; }
    public string? Status { get; set; }
    public decimal? DiscountPercentage { get; set; }
    public string? Notes { get; set; }
}

public class OrganizationServiceDto
{
    public Guid ServiceId { get; set; }
    public string ServiceName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
}

public class CreateOrganizationServiceDto
{
    public Guid ServiceId { get; set; }
    public int Quantity { get; set; } = 1;
    public decimal UnitPrice { get; set; }
}

public class CalendarEventDto
{
    public Guid Id { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public decimal TotalAmount { get; set; }
    public string CustomerName { get; set; } = string.Empty;
}
