import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UserCircle } from "lucide-react";

const customerSchema = z.object({
  fullName: z.string().min(2, "Ad soyad en az 2 karakter olmalıdır"),
  phoneNumber: z.string().min(10, "Geçerli bir telefon numarası giriniz"),
  email: z.string().email("Geçerli bir e-posta adresi giriniz").optional().or(z.literal("")),
});

export type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerFormProps {
  onDataChange: (data: CustomerFormData) => void;
  defaultValues?: Partial<CustomerFormData>;
}

export function CustomerForm({ onDataChange, defaultValues }: CustomerFormProps) {
  const {
    register,
    formState: { errors },
    watch,
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: defaultValues || {
      fullName: "",
      phoneNumber: "",
      email: "",
    },
  });

  const formData = watch();

  // Notify parent of data changes
  React.useEffect(() => {
    onDataChange(formData);
  }, [formData, onDataChange]);

  return (
    <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border-0 p-6 sm:p-8">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
          <UserCircle className="text-white text-xl" />
        </div>
        <h3 className="text-lg sm:text-xl font-bold text-gray-900">Müşteri Bilgileri</h3>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div className="space-y-2">
          <Label htmlFor="fullName">
            Ad Soyad <span className="text-red-500">*</span>
          </Label>
          <Input
            id="fullName"
            {...register("fullName")}
            className="w-full"
            placeholder="Müşteri adı soyadı"
          />
          {errors.fullName && (
            <p className="text-sm text-red-500">{errors.fullName.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="phoneNumber">
            Telefon <span className="text-red-500">*</span>
          </Label>
          <Input
            id="phoneNumber"
            type="tel"
            {...register("phoneNumber")}
            className="w-full"
            placeholder="0555 123 45 67"
          />
          {errors.phoneNumber && (
            <p className="text-sm text-red-500">{errors.phoneNumber.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">
            E-posta <span className="text-gray-400">(opsiyonel)</span>
          </Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            className="w-full"
            placeholder="<EMAIL> (opsiyonel)"
          />
          {errors.email && (
            <p className="text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>
      </div>
    </div>
  );
}
