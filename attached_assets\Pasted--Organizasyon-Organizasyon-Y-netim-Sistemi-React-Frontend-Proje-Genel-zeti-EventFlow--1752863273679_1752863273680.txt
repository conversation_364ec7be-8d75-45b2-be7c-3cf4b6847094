 
# Organizasyon - Organizasyon Yönetim Sistemi React Frontend

## Proje Genel Özeti
EventFlow, organizasyon firmaları için uçtan uca müşteri, hizmet, fiyatlandırma ve takvim yönetimi sistemi. Organizatörler hizmetlerini listeleyecek, müşteriye özel teklifler oluşturacak, anında fiyat hesaplayacak, indirim uygulayacak ve ajandaya kaydedecek. Arayüz tamamen türkçe olacak

## Teknolojik Altyapı
- **Framework**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS
- **Routing**: react-router-dom
- **HTTP Client**: axios
- **<PERSON><PERSON>h <PERSON>**: date-fns
- **Takvim**: react-big-calendar
- **Backend API**: .NET 8 Web API (localhost:5000)
- **Veritabanı**: PostgreSQL

## Entity Modelleri (TypeScript Interfaces)
```typescript
// Service Entity
interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
}

// Customer Entity
interface Customer {
  id: string;
  fullName: string;
  phoneNumber: string;
  email: string;
}

// Organization Entity
interface Organization {
  id: string;
  customerId: string;
  eventDate: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  discountPercentage: number;
  finalPrice: number;
  status: string; // "Teklif", "Kesinleşti", "İptal"
}

// Many-to-Many Relationship
interface OrganizationService {
  organizationId: string;
  serviceId: string;
}

// DTO'lar
interface ServiceDto {
  id: string;
  name: string;
  description: string;
  price: number;
}

interface CreateOrganizationDto {
  customer: {
    fullName: string;
    phoneNumber: string;
    email: string;
  };
  serviceIds: string[];
  eventDate: string;
  startTime: string;
  endTime: string;
  discountPercentage: number;
}

interface OrganizationDetailsDto {
  id: string;
  customer: Customer;
  services: Service[];
  eventDate: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  discountPercentage: number;
  finalPrice: number;
  status: string;
}

interface CalendarEventDto {
  id: string;
  title: string;
  start: Date;
  end: Date;
  customerName: string;
}
```

## API Endpoints
```
GET /api/services           - Tüm hizmetleri listele
POST /api/services          - Yeni hizmet ekle
PUT /api/services/{id}      - Hizmeti güncelle
DELETE /api/services/{id}   - Hizmeti sil

POST /api/organizations     - Yeni organizasyon oluştur
GET /api/organizations      - Tüm organizasyonları listele
GET /api/organizations/{id} - Organizasyon detayı
GET /api/calendar           - Ajanda verisi (CalendarEventDto[])
```

## Klasör Yapısı
```
src/
├── components/
│   ├── ui/                 # Temel UI bileşenleri
│   │   ├── Button.tsx
│   │   ├── Modal.tsx
│   │   ├── Input.tsx
│   │   ├── Table.tsx
│   │   └── LoadingSpinner.tsx
│   ├── forms/              # Form bileşenleri
│   │   ├── ServiceForm.tsx
│   │   └── CustomerForm.tsx
│   ├── layout/             # Layout bileşenleri
│   │   ├── Header.tsx
│   │   ├── Navigation.tsx
│   │   └── Layout.tsx
│   └── calendar/           # Takvim bileşenleri
│       ├── Calendar.tsx
│       └── EventModal.tsx
├── pages/
│   ├── HomePage.tsx
│   ├── ServicesPage.tsx
│   ├── NewOrganizationPage.tsx
│   └── AgendaPage.tsx
├── services/
│   ├── api.ts              # Axios konfigürasyonu
│   ├── serviceService.ts   # Hizmet API çağrıları
│   └── organizationService.ts # Organizasyon API çağrıları
├── models/
│   └── index.ts            # TypeScript interface'leri
├── hooks/
│   ├── useServices.ts      # Hizmet yönetimi hook'u
│   ├── useOrganizations.ts # Organizasyon yönetimi hook'u
│   └── usePriceCalculator.ts # Fiyat hesaplama hook'u
├── contexts/
│   └── AppContext.tsx      # Global state yönetimi
├── utils/
│   ├── formatters.ts       # Tarih, para formatlayıcıları
│   └── validators.ts       # Form validasyonları
└── App.tsx
```

## Sayfa Detayları

### 1. HomePage.tsx
- Dashboard benzeri ana sayfa
- Hızlı istatistikler (toplam organizasyon, bugünkü etkinlikler vs.)
- Son organizasyonlar listesi
- Hızlı eylem butonları

### 2. ServicesPage.tsx
- **Tablo Görünümü**: Hizmet listesi (Ad, Açıklama, Fiyat, Eylemler)
- **Yeni Hizmet Butonu**: Modal açar
- **Düzenleme**: Satır başına edit butonu, modal açar
- **Silme**: Onay modal'ı ile silme
- **Arama/Filtreleme**: Hizmet adına göre arama
- **Loading States**: Veri yüklenirken spinner
- **Error Handling**: API hatalarını göster

### 3. NewOrganizationPage.tsx (EN KRİTİK SAYFA)
Bu sayfa en karmaşık iş akışını içerir:

#### Müşteri Bilgileri Bölümü:
- Ad Soyad (zorunlu)
- Telefon (zorunlu, format kontrolü)
- E-posta (zorunlu, email format kontrolü)

#### Hizmet Seçimi Bölümü:
- Tüm hizmetleri checkbox listesi olarak göster
- Her hizmetin yanında adı, açıklaması ve fiyatı görünsün
- Checkbox değiştiğinde anında fiyat hesaplansın

#### Fiyat Hesaplama Motoru (REALTİME):
```typescript
// Seçili hizmetlerin toplam fiyatı
const totalPrice = ...

diğer detaylı bilgiler : (organizasyon işiyle uğraşan birinin kullanacağı bir ajandada olması gereken özellikler nelerdir. 

 KAMERA ÇEKİMİ
 FOTOĞRAF ÇEKİMİ
 DRONE ÇEKİMİ
 DIŞ ÇEKİM
 ORKESTRA
 BANDO EKİBİ
 DAVUL ZURNA EKİBİ
 SES SİSTEMİ
 KADIN ERKEK DJ
 SANDALYE MASA ÇADIR
 ÇAY KAZANI
 HOST/HOSTES/GARSON
 ZİNCİR BALON ALAN SÜSLEME
 ‌PALYAÇO /ANİMATÖR
 PATLAMIŞ MISIR
 PAMUK ŞEKER
 DAVETİYE
 PROJEKTÖR
 SARKIT LAMBA 
 SİS/KONFETİ MAKİNESİ 
 VOLKAN/MEŞALE TABANCASI
KURU BUZ SİS SHOW 
 KINA TAHTI
 SÖZ NİŞAN D.GÜNÜ TAG
 GİRİŞ YOLU
 GELİN DAMAT MASASI
 ARAÇ KİRALAMA 
 EXTRALAR
 HEDİYELİKLER
 İKRAMLIKLAR

Hizmetler/Açıklama/Ücret

bu hizmetleri sunuyor. müşteri ulaştığında yazılıma girdiğinde bu hizmetleri seçip anında fiyat oluşturabilmesi gerekiyor.  ajandasında boş günlerinden seçip o günü o müşteri için kaydedebilmeli. ayrıca bu hizmetler için fiyat belirleyebilmeli sistemde bunlardan seçip oluşacak toplam fiyatı görebilmeli ve % lik iskonto uygulayabilmeli ve oluşacak yeni fiyatı görebilmeli. kaydete basınca o müşteri için belirlenen tarihte ajandada kayıt yapılabilmeli. o müşteriye o günün hangi saat aralığını ayırdığını görebilmeli. yani bir gününü istediği gibi organize edebilmeli. umarım anlatabilmişimdir. bu özelliklerin olduğu react ve .net sdk8 ile bir sistem hazırlamak istiyorum supabase alt yapısı kullanacak şekilde.)