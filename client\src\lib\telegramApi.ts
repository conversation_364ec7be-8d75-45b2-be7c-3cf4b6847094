import { supabase } from './supabase';

/**
 * Telegram Bot API integration for sending notifications
 */

export interface TelegramMessage {
  text: string;
  parse_mode?: 'HTML' | 'Markdown' | 'MarkdownV2';
  disable_web_page_preview?: boolean;
}

export interface TelegramSettings {
  botToken: string;
  chatId: string;
  enabled: boolean;
}

/**
 * Get Telegram settings from system_settings
 */
export async function getTelegramSettings(): Promise<TelegramSettings | null> {
  try {
    const { data, error } = await supabase
      .from('system_settings')
      .select('key, value')
      .in('key', ['telegram_bot_token', 'telegram_chat_id', 'telegram_notifications_enabled']);

    if (error) {
      console.error('Error fetching Telegram settings:', error);
      return null;
    }

    const settings: Record<string, any> = {};
    data?.forEach(setting => {
      settings[setting.key] = setting.value;
    });

    // Check if all required settings are present
    if (!settings.telegram_bot_token || !settings.telegram_chat_id) {
      console.warn('Telegram settings incomplete');
      return null;
    }

    return {
      botToken: settings.telegram_bot_token,
      chatId: settings.telegram_chat_id,
      enabled: settings.telegram_notifications_enabled || false
    };
  } catch (error) {
    console.error('Error getting Telegram settings:', error);
    return null;
  }
}

/**
 * Send message to Telegram bot
 */
export async function sendTelegramMessage(
  message: TelegramMessage,
  settings?: TelegramSettings
): Promise<boolean> {
  try {
    // Get settings if not provided
    const telegramSettings = settings || await getTelegramSettings();

    if (!telegramSettings || !telegramSettings.enabled) {
      console.log('Telegram notifications disabled or settings not found');
      return false;
    }

    const { botToken, chatId } = telegramSettings;

    // Telegram Bot API endpoint
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: message.text,
        parse_mode: message.parse_mode || 'HTML',
        disable_web_page_preview: message.disable_web_page_preview || true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Telegram API error:', errorData);
      return false;
    }

    const result = await response.json();
    console.log('✅ Telegram message sent successfully:', result.message_id);
    return true;
  } catch (error) {
    console.error('Error sending Telegram message:', error);
    return false;
  }
}

/**
 * Send notification to Telegram with organization branding
 */
export async function sendOrganizationNotification(
  title: string,
  message: string,
  type: 'info' | 'warning' | 'error' | 'success' = 'info'
): Promise<boolean> {
  try {
    // Get Telegram settings
    const settings = await getTelegramSettings();

    if (!settings || !settings.enabled) {
      return false;
    }

    // Format message with organization branding
    const typeEmojis = {
      info: '📢',
      warning: '⚠️',
      error: '🚨',
      success: '✅'
    };

    const emoji = typeEmojis[type];
    const formattedMessage = `
🏢 <b>Organizasyon Bildirimi</b>

${emoji} <b>${title}</b>

${message}

📅 <i>${new Date().toLocaleString('tr-TR')}</i>
    `.trim();

    return await sendTelegramMessage({
      text: formattedMessage,
      parse_mode: 'HTML',
      disable_web_page_preview: true
    }, settings);
  } catch (error) {
    console.error('Error sending organization notification:', error);
    return false;
  }
}

/**
 * Test Telegram bot connection
 */
export async function testTelegramBot(botToken: string, chatId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const testMessage = {
      text: `🧪 <b>Test Mesajı</b>

Bu bir test mesajıdır. Telegram bot entegrasyonu başarıyla çalışıyor!

📅 <i>${new Date().toLocaleString('tr-TR')}</i>`,
      parse_mode: 'HTML' as const,
      disable_web_page_preview: true
    };

    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: testMessage.text,
        parse_mode: testMessage.parse_mode,
        disable_web_page_preview: testMessage.disable_web_page_preview,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.description || 'Telegram API error'
      };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
