export interface Service {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  createdAt: Date;
}

export interface Organization {
  id: string;
  customerId: string;
  eventDate: string;
  startTime: string;
  endTime: string;
  totalPrice: string;
  discountPercentage: string;
  finalPrice: string;
  status: string;
  createdAt: Date;
}

export interface OrganizationDetailsDto {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  services: OrganizationServiceDto[];
  eventDate: string;
  status: string;
  discountPercentage: number;
  totalAmount: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminUser {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  created_at: string;
  updated_at?: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  phone?: string;
  avatar_url?: string;
  role?: string;
  is_active?: boolean;
}

export interface OrganizationServiceDto {
  serviceId: string;
  serviceName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CalendarEventDto {
  id: string;
  title: string;
  start: Date;
  end: Date;
  customerName: string;
  status: string;
  totalAmount: number;
}

export interface CreateOrganizationDto {
  customer: {
    fullName: string;
    phoneNumber: string;
    email?: string;
  };
  eventDate: string;
  status: string;
  discountPercentage: number;
  totalAmount: number;
  notes?: string;
  services: Array<{
    serviceId: string;
    quantity: number;
    unitPrice: number;
  }>;
}
