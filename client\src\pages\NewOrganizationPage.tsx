import { useLocation } from "wouter";
import { Layout } from "../components/layout/Layout";
import { ModernMultiStepForm, type OrganizationFormData } from "../components/forms/ModernMultiStepForm";
import { useOrganizations } from "../hooks/useOrganizations";
import { useToast } from "@/hooks/use-toast";
import { useServices } from "../hooks/useServices";

export default function NewOrganizationPage() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { createOrganization, isCreating } = useOrganizations();
  const { services } = useServices();

  const handleSubmit = (formData: OrganizationFormData) => {
    // Calculate total amount based on selected services and discount
    const calculateTotalAmount = () => {
      if (!services) return 0;

      const subtotal = formData.services.selectedServices.reduce((total: number, serviceId: string) => {
        const service = services.find(s => s.id === serviceId);
        return total + (service?.basePrice || 0);
      }, 0);

      const discount = (subtotal * formData.pricing.discountPercentage) / 100;
      return subtotal - discount;
    };

    const totalAmount = calculateTotalAmount();

    const organizationData = {
      customer: {
        fullName: formData.customer.fullName,
        phoneNumber: formData.customer.phoneNumber,
        email: formData.customer.email || undefined,
      },
      eventDate: formData.event.eventDate,
      startTime: formData.event.startTime,
      endTime: formData.event.endTime,
      status: "Teklif",
      discountPercentage: formData.pricing.discountPercentage,
      totalAmount: totalAmount,
      notes: formData.pricing.notes,
      services: formData.services.selectedServices.map(serviceId => {
        const service = services?.find(s => s.id === serviceId);
        return {
          serviceId: serviceId,
          quantity: 1, // Default quantity
          unitPrice: service?.basePrice || 0
        };
      })
    };

    createOrganization(organizationData, {
      onSuccess: () => {
        toast({
          title: "✅ Başarılı",
          description: "Organizasyon başarıyla oluşturuldu",
          variant: "success",
        });
        setLocation("/organizations");
      },
      onError: (error: any) => {
        console.error('Organization creation error:', error);
        const errorMessage = error?.message || "Organizasyon oluşturulurken bir hata oluştu";
        toast({
          title: "❌ Hata",
          description: errorMessage,
          variant: "destructive",
        });
      },
    });
  };

  return (
    <Layout
      title="Yeni Organizasyon"
      subtitle="Modern ve kullanıcı dostu form deneyimi"
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
        <div className="container mx-auto px-4">
          <ModernMultiStepForm
            onSubmit={handleSubmit}
            isSubmitting={isCreating}
          />
        </div>
      </div>
    </Layout>
  );
}
