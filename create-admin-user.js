// Script to create admin user in Supabase
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAdminUser() {
  try {
    console.log('🔍 Checking existing users...');
    
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('eventflow_profiles')
      .select('*')
      .limit(10);
    
    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError);
      return;
    }
    
    console.log('✅ Found profiles:', profiles?.length || 0);
    profiles?.forEach(profile => {
      console.log(`- ${profile.first_name} ${profile.last_name} (${profile.id})`);
    });
    
    // Check existing admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*');
    
    if (adminError) {
      console.error('❌ Error fetching admin users:', adminError);
    } else {
      console.log('✅ Found admin users:', adminUsers?.length || 0);
      adminUsers?.forEach(admin => {
        console.log(`- Admin: ${admin.user_id} (${admin.role})`);
      });
    }
    
    // If we have profiles but no admin users, create one
    if (profiles && profiles.length > 0 && (!adminUsers || adminUsers.length === 0)) {
      const firstUser = profiles[0];
      console.log(`🔧 Creating admin user for: ${firstUser.first_name} ${firstUser.last_name}`);
      
      const { data: newAdmin, error: createError } = await supabase
        .from('admin_users')
        .insert({
          user_id: firstUser.id,
          role: 'super_admin',
          permissions: {},
          is_active: true,
          created_by: firstUser.id
        })
        .select()
        .single();
      
      if (createError) {
        console.error('❌ Error creating admin user:', createError);
      } else {
        console.log('✅ Admin user created successfully:', newAdmin);
      }
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

createAdminUser();
