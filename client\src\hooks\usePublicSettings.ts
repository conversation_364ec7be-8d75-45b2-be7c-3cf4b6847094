import { useQuery } from '@tanstack/react-query';
import { publicApi, SystemSetting } from '@/lib/adminApi';

export interface PublicSettings {
  app_name: string;
  site_description: string;
  site_keywords: string;
  [key: string]: any;
}

export function usePublicSettings() {
  const { data: settings = [], isLoading, error } = useQuery({
    queryKey: ['public', 'settings'],
    queryFn: publicApi.settings.getPublic,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  });

  // Convert settings array to object for easier access
  const settingsObject = settings.reduce((acc: PublicSettings, setting: SystemSetting) => {
    try {
      // Parse JSON values
      acc[setting.key] = setting.value ? JSON.parse(setting.value) : null;
    } catch {
      // If parsing fails, use raw value
      acc[setting.key] = setting.value;
    }
    return acc;
  }, {} as PublicSettings);

  // Provide default values
  const publicSettings: PublicSettings = {
    app_name: settingsObject.app_name || 'EventFlow - Organizasyon Yönetimi',
    site_description: settingsObject.site_description || 'EventFlow ile etkinlik ve organizasyon yönetimini kolaylaştırın. Modern ve kullanıcı dostu arayüz ile organizasyonlarınızı verimli bir şekilde yönetin.',
    site_keywords: settingsObject.site_keywords || 'etkinlik yönetimi, organizasyon, event management, EventFlow, organizasyon defteri',
    ...settingsObject
  };

  return {
    settings: publicSettings,
    isLoading,
    error
  };
}
