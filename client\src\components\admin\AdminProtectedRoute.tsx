import React from 'react';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Shield, Home, Loader2 } from 'lucide-react';
import { Link } from 'wouter';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'super_admin' | 'admin' | 'moderator';
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

export const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermission,
  fallback
}) => {
  const adminAuth = useAdminAuth();

  // Loading state
  if (adminAuth.loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Yetki Kontrolü
            </h3>
            <p className="text-gray-600 text-center">
              Admin yetkileriniz kontrol ediliyor...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (adminAuth.error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="w-full max-w-md border-red-200">
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Yetki Kontrolü Hatası</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Admin yetkileriniz kontrol edilirken bir hata oluştu.
            </p>
            <p className="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
              {adminAuth.error}
            </p>
            <div className="flex gap-2 justify-center">
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <Shield className="h-4 w-4" />
                Tekrar Dene
              </Button>
              <Link href="/">
                <Button variant="default" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Ana Sayfa
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not admin
  if (!adminAuth.isAdmin) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="w-full max-w-md border-orange-200">
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-6 w-6 text-orange-600" />
            </div>
            <CardTitle className="text-orange-900">Yetkisiz Erişim</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Bu sayfaya erişim için admin yetkilerine sahip olmanız gerekiyor.
            </p>
            <div className="bg-orange-50 p-4 rounded-lg">
              <p className="text-sm text-orange-800">
                <strong>Bilgi:</strong> Eğer admin olmanız gerektiğini düşünüyorsanız, 
                sistem yöneticisi ile iletişime geçin.
              </p>
            </div>
            <Link href="/">
              <Button className="w-full flex items-center gap-2">
                <Home className="h-4 w-4" />
                Ana Sayfaya Dön
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check specific role requirement
  if (requiredRole && !adminAuth.hasRole(requiredRole)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="w-full max-w-md border-red-200">
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Yetersiz Yetki</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Bu sayfaya erişim için <strong>{getRoleDisplayName(requiredRole)}</strong> yetkisine sahip olmanız gerekiyor.
            </p>
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Mevcut Yetkiniz:</strong> {getRoleDisplayName(adminAuth.role)}
              </p>
            </div>
            <div className="flex gap-2 justify-center">
              <Link href="/admin">
                <Button variant="outline" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin Panel
                </Button>
              </Link>
              <Link href="/">
                <Button variant="default" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Ana Sayfa
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check specific permission requirement
  if (requiredPermission && !adminAuth.hasPermission(requiredPermission)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <Card className="w-full max-w-md border-red-200">
          <CardHeader className="text-center">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Yetersiz İzin</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Bu işlemi gerçekleştirmek için <strong>{requiredPermission}</strong> iznine sahip olmanız gerekiyor.
            </p>
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Mevcut Yetkiniz:</strong> {getRoleDisplayName(adminAuth.role)}
              </p>
            </div>
            <div className="flex gap-2 justify-center">
              <Link href="/admin">
                <Button variant="outline" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin Panel
                </Button>
              </Link>
              <Link href="/">
                <Button variant="default" className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  Ana Sayfa
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // All checks passed, render children
  return <>{children}</>;
};

// Helper function to get role display name
function getRoleDisplayName(role: string | null): string {
  switch (role) {
    case 'super_admin':
      return 'Süper Admin';
    case 'admin':
      return 'Admin';
    case 'moderator':
      return 'Moderatör';
    default:
      return 'Bilinmeyen';
  }
}

// Convenience components for specific roles
export const SuperAdminRoute: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <AdminProtectedRoute requiredRole="super_admin" fallback={fallback}>
    {children}
  </AdminProtectedRoute>
);

export const AdminRoute: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <AdminProtectedRoute requiredRole="admin" fallback={fallback}>
    {children}
  </AdminProtectedRoute>
);

export const ModeratorRoute: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback }) => (
  <AdminProtectedRoute requiredRole="moderator" fallback={fallback}>
    {children}
  </AdminProtectedRoute>
);
