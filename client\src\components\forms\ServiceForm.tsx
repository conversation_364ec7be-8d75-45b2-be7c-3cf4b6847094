import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import type { Service } from "../../types";

const serviceSchema = z.object({
  name: z.string().min(1, "Hizmet adı gereklidir"),
  description: z.string().min(1, "Açıklama gereklidir"),
  basePrice: z.number().min(0, "Fiyat 0'dan büyük olmalıdır"),
  isActive: z.boolean().default(true),
});

type ServiceFormData = z.infer<typeof serviceSchema>;

interface ServiceFormProps {
  service?: Service;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ServiceFormData) => void;
  isSubmitting?: boolean;
}

export function ServiceForm({ 
  service, 
  open, 
  onOpenChange, 
  onSubmit,
  isSubmitting = false 
}: ServiceFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: service ? {
      name: service.name,
      description: service.description,
      basePrice: service.basePrice,
      isActive: service.isActive,
    } : {
      name: "",
      description: "",
      basePrice: 0,
      isActive: true,
    },
  });

  const handleFormSubmit = (data: ServiceFormData) => {
    onSubmit(data);
    reset();
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  // Reset form when service prop changes
  useEffect(() => {
    if (service) {
      reset({
        name: service.name,
        description: service.description,
        basePrice: service.basePrice,
        isActive: service.isActive,
      });
    } else {
      reset({
        name: "",
        description: "",
        basePrice: 0,
        isActive: true,
      });
    }
  }, [service, reset]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {service ? "Hizmet Düzenle" : "Yeni Hizmet Ekle"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Hizmet Adı</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Hizmet adını giriniz"
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Açıklama</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Hizmet açıklamasını giriniz"
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="basePrice">Fiyat (₺)</Label>
            <Input
              id="basePrice"
              type="number"
              step="0.01"
              {...register("basePrice", { valueAsNumber: true })}
              placeholder="0.00"
            />
            {errors.basePrice && (
              <p className="text-sm text-red-500">{errors.basePrice.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              İptal
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Kaydediliyor..." : "Kaydet"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
