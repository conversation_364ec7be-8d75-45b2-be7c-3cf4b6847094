import { User, Menu, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "../../contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useScrollHeader } from "../../hooks/useScrollHeader";
import { useIsMobile } from "../../hooks/use-mobile";
import { cn } from "@/lib/utils";

interface HeaderProps {
  title: string;
  subtitle?: string;
  onMenuClick?: () => void;
}

export function Header({ title, subtitle, onMenuClick }: HeaderProps) {
  const { profile, signOut } = useAuth();
  const isMobile = useIsMobile();
  const { isVisible } = useScrollHeader({
    threshold: 10,
    hideOnScrollDown: true
  });

  const currentDate = new Date().toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className={cn(
      "bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-200/50 px-3 sm:px-6 py-3 sm:py-4 sticky top-0 z-40",
      isMobile && (isVisible ? "header-visible" : "header-hidden")
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden p-2"
            onClick={onMenuClick}
          >
            <Menu className="w-5 h-5" />
          </Button>

          <div className="min-w-0 flex-1">
            <h2 className="text-lg sm:text-xl lg:text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent truncate">
              {title}
            </h2>
            {subtitle && (
              <p className="text-xs sm:text-sm text-gray-600 mt-1 truncate">{subtitle}</p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2 sm:space-x-4">
          <div className="hidden md:block text-right">
            <p className="text-sm font-medium text-gray-900">
              {profile && profile.firstName && profile.lastName
                ? `${profile.firstName} ${profile.lastName}`
                : 'Organizatör'}
            </p>
            <p className="text-xs text-gray-500">
              {profile?.companyName || currentDate}
            </p>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg hover:from-blue-600 hover:to-blue-700">
                <User className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {profile && profile.firstName && profile.lastName
                      ? `${profile.firstName} ${profile.lastName}`
                      : 'Kullanıcı'}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {profile?.companyName || 'Şirket Adı'}
                  </p>
                  {/* Mobile-specific profile info */}
                  {isMobile && (
                    <p className="text-xs leading-none text-muted-foreground mt-1">
                      {currentDate}
                    </p>
                  )}
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="text-red-600 focus:text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Çıkış Yap</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
