import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import {
  Shield,
  UserPlus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Crown,
  User,
  Settings,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function AdminManagementPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [newAdminUserId, setNewAdminUserId] = useState('');
  const [newAdminRole, setNewAdminRole] = useState<'super_admin' | 'admin' | 'moderator'>('admin');
  const [searchMethod, setSearchMethod] = useState<'email' | 'userid'>('email');
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState<any>(null);
  const [editAdminRole, setEditAdminRole] = useState<'super_admin' | 'admin' | 'moderator'>('admin');
  const [editAdminActive, setEditAdminActive] = useState(true);

  // Fetch admin users
  const { data: adminUsers, isLoading, error, refetch } = useQuery({
    queryKey: ['admin', 'admin-users'],
    queryFn: adminApi.adminUsers.getAll,
  });

  // Create admin mutation
  const createAdminMutation = useMutation({
    mutationFn: ({ userId, role }: { userId: string; role: 'super_admin' | 'admin' | 'moderator' }) =>
      adminApi.adminUsers.create(userId, role),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'admin-users'] });
      toast({
        title: "✅ Admin oluşturuldu",
        description: "Yeni admin kullanıcısı başarıyla oluşturuldu.",
        variant: "success",
      });
      setShowAddDialog(false);
      setNewAdminEmail('');
      setNewAdminUserId('');
      setNewAdminRole('admin');
    },
    onError: (error: any) => {
      toast({
        title: "❌ Hata",
        description: error.message || "Admin oluşturulurken bir hata oluştu.",
        variant: "destructive",
      });
    },
  });

  // Update admin mutation
  const updateAdminMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: any }) =>
      adminApi.adminUsers.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'admin-users'] });
      toast({
        title: "✅ Admin güncellendi",
        description: "Admin kullanıcısı başarıyla güncellendi.",
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "❌ Hata",
        description: error.message || "Admin güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    },
  });

  // Delete admin mutation
  const deleteAdminMutation = useMutation({
    mutationFn: (id: string) => adminApi.adminUsers.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'admin-users'] });
      toast({
        title: "✅ Admin silindi",
        description: "Admin kullanıcısı başarıyla silindi.",
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "❌ Hata",
        description: error.message || "Admin silinirken bir hata oluştu.",
        variant: "destructive",
      });
    },
  });

  const filteredAdmins = adminUsers?.filter(admin =>
    admin.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.last_name?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleCreateAdmin = async () => {
    let userId = '';

    if (searchMethod === 'email') {
      if (!newAdminEmail.trim()) {
        toast({
          title: "❌ Hata",
          description: "Email adresi gereklidir.",
          variant: "destructive",
        });
        return;
      }

      try {
        // Find user by email
        const user = await adminApi.users.findByEmail(newAdminEmail.trim());

        if (!user) {
          toast({
            title: "❌ Kullanıcı Bulunamadı",
            description: "Bu email adresine sahip bir kullanıcı bulunamadı.",
            variant: "destructive",
          });
          return;
        }

        userId = user.id;
      } catch (error) {
        toast({
          title: "❌ Hata",
          description: (error as Error).message || "Kullanıcı aranırken hata oluştu.",
          variant: "destructive",
        });
        return;
      }
    } else {
      if (!newAdminUserId.trim()) {
        toast({
          title: "❌ Hata",
          description: "Kullanıcı ID gereklidir.",
          variant: "destructive",
        });
        return;
      }
      userId = newAdminUserId.trim();
    }

    // Check if user is already an admin
    const existingAdmin = adminUsers?.find(admin => admin.user_id === userId);
    if (existingAdmin) {
      toast({
        title: "❌ Hata",
        description: "Bu kullanıcı zaten admin olarak atanmış.",
        variant: "destructive",
      });
      return;
    }

    createAdminMutation.mutate({ userId, role: newAdminRole });
  };

  const handleEditAdmin = (admin: any) => {
    setSelectedAdmin(admin);
    setEditAdminRole(admin.role);
    setEditAdminActive(admin.is_active);
    setShowEditDialog(true);
  };

  const handleUpdateAdmin = () => {
    if (!selectedAdmin) return;

    const updates = {
      role: editAdminRole,
      is_active: editAdminActive
    };

    updateAdminMutation.mutate({ id: selectedAdmin.id, updates });
    setShowEditDialog(false);
    setSelectedAdmin(null);
  };

  const handleDeleteAdmin = (admin: any) => {
    setSelectedAdmin(admin);
    setShowDeleteDialog(true);
  };

  const confirmDeleteAdmin = () => {
    if (!selectedAdmin) return;

    deleteAdminMutation.mutate(selectedAdmin.id);
    setShowDeleteDialog(false);
    setSelectedAdmin(null);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'default';
      case 'admin':
        return 'secondary';
      case 'moderator':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Süper Admin';
      case 'admin':
        return 'Admin';
      case 'moderator':
        return 'Moderatör';
      default:
        return 'Bilinmeyen';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'moderator':
        return <User className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  return (
    <AdminLayout
      title="Admin Yönetimi"
      subtitle="Admin kullanıcılarını yönet ve yetkileri düzenle"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Admin ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Yenile
            </Button>
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Yeni Admin
                </Button>
              </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Yeni Admin Ekle</DialogTitle>
                <DialogDescription>
                  Mevcut bir kullanıcıyı admin olarak atayın.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Arama Yöntemi</Label>
                  <Select value={searchMethod} onValueChange={(value: any) => setSearchMethod(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email Adresi</SelectItem>
                      <SelectItem value="userid">Kullanıcı ID</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {searchMethod === 'email' ? (
                  <div>
                    <Label htmlFor="email">Email Adresi</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newAdminEmail}
                      onChange={(e) => setNewAdminEmail(e.target.value)}
                    />
                  </div>
                ) : (
                  <div>
                    <Label htmlFor="userid">Kullanıcı ID</Label>
                    <Input
                      id="userid"
                      type="text"
                      placeholder="4f718d53-06b7-4265-ae90-462f95935efc"
                      value={newAdminUserId}
                      onChange={(e) => setNewAdminUserId(e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Kullanıcı ID'sini Kullanıcı Yönetimi sayfasından bulabilirsiniz.
                    </p>
                  </div>
                )}

                <div>
                  <Label htmlFor="role">Rol</Label>
                  <Select value={newAdminRole} onValueChange={(value: any) => setNewAdminRole(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="moderator">Moderatör</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="super_admin">Süper Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  İptal
                </Button>
                <Button
                  onClick={handleCreateAdmin}
                  disabled={createAdminMutation.isPending}
                >
                  {createAdminMutation.isPending ? 'Oluşturuluyor...' : 'Admin Ekle'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          </div>
        </div>

        {/* Edit Admin Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Admin Düzenle</DialogTitle>
              <DialogDescription>
                {selectedAdmin?.first_name && selectedAdmin?.last_name
                  ? `${selectedAdmin.first_name} ${selectedAdmin.last_name}`
                  : selectedAdmin?.email} kullanıcısının admin bilgilerini düzenleyin.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-role">Rol</Label>
                <Select value={editAdminRole} onValueChange={(value: any) => setEditAdminRole(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="moderator">Moderatör</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="super_admin">Süper Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-active"
                  checked={editAdminActive}
                  onChange={(e) => setEditAdminActive(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="edit-active">Aktif</Label>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                İptal
              </Button>
              <Button
                onClick={handleUpdateAdmin}
                disabled={updateAdminMutation.isPending}
              >
                {updateAdminMutation.isPending ? 'Güncelleniyor...' : 'Güncelle'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Admin Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Admin Sil</DialogTitle>
              <DialogDescription>
                {selectedAdmin?.first_name && selectedAdmin?.last_name
                  ? `${selectedAdmin.first_name} ${selectedAdmin.last_name}`
                  : selectedAdmin?.email} kullanıcısını admin listesinden silmek istediğinizden emin misiniz?
              </DialogDescription>
            </DialogHeader>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <p className="text-sm text-red-800">
                  Bu işlem geri alınamaz. Kullanıcı admin yetkilerini kaybedecek.
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                İptal
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteAdmin}
                disabled={deleteAdminMutation.isPending}
              >
                {deleteAdminMutation.isPending ? 'Siliniyor...' : 'Sil'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Admin</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminUsers?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Süper Admin</CardTitle>
              <Crown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {adminUsers?.filter(admin => admin.role === 'super_admin').length || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aktif Admin</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {adminUsers?.filter(admin => admin.is_active).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admin Users Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Admin Kullanıcıları
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600 mb-2">Admin kullanıcıları yüklenirken hata oluştu</p>
                <p className="text-sm text-gray-500 mb-4">
                  {error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu'}
                </p>
                <p className="text-xs text-gray-400 mb-4">
                  Veritabanı ilişkileri eksik olabilir. database-fixes.sql dosyasını çalıştırın.
                </p>
                <Button variant="outline" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Tekrar Dene
                </Button>
              </div>
            ) : filteredAdmins.length === 0 ? (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {searchTerm ? 'Arama kriterine uygun admin bulunamadı' : 'Henüz admin kullanıcısı bulunmuyor'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Kullanıcı</TableHead>
                      <TableHead>Rol</TableHead>
                      <TableHead>Durum</TableHead>
                      <TableHead>Oluşturulma</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAdmins.map((admin) => (
                      <TableRow key={admin.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm font-semibold">
                                {admin.first_name?.charAt(0)?.toUpperCase() || 
                                 admin.email?.charAt(0)?.toUpperCase() || 'A'}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium">
                                {admin.first_name && admin.last_name 
                                  ? `${admin.first_name} ${admin.last_name}`
                                  : 'Admin Kullanıcı'
                                }
                              </p>
                              <p className="text-sm text-gray-600">{admin.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRoleBadgeVariant(admin.role)} className="flex items-center gap-1 w-fit">
                            {getRoleIcon(admin.role)}
                            {getRoleDisplayName(admin.role)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={admin.is_active ? "default" : "secondary"}>
                            {admin.is_active ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {new Date(admin.created_at).toLocaleDateString('tr-TR')}
                          </span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleEditAdmin(admin)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Düzenle
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditAdmin(admin)}>
                                <Settings className="mr-2 h-4 w-4" />
                                Yetkiler
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteAdmin(admin)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Sil
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
