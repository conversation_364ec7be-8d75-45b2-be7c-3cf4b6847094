import { useLocation } from "wouter";
import { Layout } from "../components/layout/Layout";
import { ModernMultiStepForm, type OrganizationFormData } from "../components/forms/ModernMultiStepForm";
import { useOrganizations } from "../hooks/useOrganizations";
import { useToast } from "@/hooks/use-toast";

export default function NewOrganizationPageModern() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { createOrganization, isCreating } = useOrganizations();

  const handleSubmit = (formData: OrganizationFormData) => {
    // Transform form data to match API expectations
    const organizationData = {
      customer: {
        fullName: formData.customer.fullName,
        phoneNumber: formData.customer.phoneNumber,
        email: formData.customer.email || undefined,
      },
      serviceIds: formData.services.selectedServices,
      eventDate: formData.event.eventDate,
      startTime: formData.event.startTime,
      endTime: formData.event.endTime,
      discountPercentage: formData.pricing.discountPercentage,
      notes: formData.pricing.notes,
    };

    createOrganization(organizationData, {
      onSuccess: () => {
        toast({
          title: "✅ Başarılı",
          description: "Organizasyon başarıyla oluşturuldu",
          variant: "success",
        });
        setLocation("/organizations");
      },
      onError: (error: any) => {
        console.error('Organization creation error:', error);
        const errorMessage = error?.message || "Organizasyon oluşturulurken bir hata oluştu";
        toast({
          title: "❌ Hata",
          description: errorMessage,
          variant: "destructive",
        });
      },
    });
  };

  return (
    <Layout 
      title="Yeni Organizasyon" 
      subtitle="Modern ve kullanıcı dostu form deneyimi"
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-0 sm:py-1 lg:py-2">
        <div className="w-full mx-auto px-0 sm:px-1 lg:px-2">
          <ModernMultiStepForm
            onSubmit={handleSubmit}
            isSubmitting={isCreating}
          />
        </div>
      </div>
    </Layout>
  );
}
