import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import {
  Home,
  Settings,
  PlusCircle,
  Calendar,
  List,
  Wrench,
  Shield
} from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";
import { useAdminAuth } from "../../hooks/useAdminAuth";

const navigation = [
  { name: "Ana Sayfa", href: "/", icon: Home },
  { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/services", icon: Settings },
  { name: "Yeni Organizasyon", href: "/new-organization", icon: PlusCircle },
  { name: "<PERSON><PERSON><PERSON>", href: "/calendar", icon: Calendar },
  { name: "Organizasyonlar", href: "/organizations", icon: List },
  { name: "Ayarlar", href: "/settings", icon: Wrench },
];

export function Sidebar() {
  const [location] = useLocation();
  const { profile } = useAuth();
  const adminAuth = useAdminAuth();

  return (
    <div className="w-72 bg-white/70 backdrop-blur-xl shadow-xl border-r border-gray-200/50 flex flex-col">
      <div className="p-6 border-b border-gray-200/50">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <Calendar className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Organizasyon Defteri
            </h1>
            <p className="text-sm text-gray-500">Organizasyon Yönetimi</p>
          </div>
        </div>
      </div>
      
      <nav className="flex-1 p-6 space-y-3">
        {navigation.map((item) => {
          const isActive = location === item.href;
          return (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  "flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-200 cursor-pointer group",
                  isActive
                    ? "text-white bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg shadow-blue-500/25"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900 hover:shadow-md"
                )}
              >
                <item.icon className={cn(
                  "w-5 h-5 mr-3 transition-transform duration-200",
                  isActive ? "scale-110" : "group-hover:scale-105"
                )} />
                {item.name}
              </div>
            </Link>
          );
        })}

        {/* Admin Panel Link - Only show for admin users */}
        {adminAuth.isAdmin && (
          <Link href="/admin">
            <div
              className={cn(
                "flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-200 cursor-pointer group border-2 border-dashed",
                location.startsWith('/admin')
                  ? "text-white bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25 border-purple-300"
                  : "text-purple-700 border-purple-200 hover:bg-purple-50 hover:text-purple-900 hover:shadow-md hover:border-purple-300"
              )}
            >
              <Shield className={cn(
                "w-5 h-5 mr-3 transition-transform duration-200",
                location.startsWith('/admin') ? "scale-110" : "group-hover:scale-105"
              )} />
              Admin Panel
            </div>
          </Link>
        )}
      </nav>
      
      <div className="p-6 border-t border-gray-200/50">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {profile && profile.firstName && profile.lastName
                  ? `${profile.firstName} ${profile.lastName}`
                  : 'Organizatör'}
              </p>
              <p className="text-xs text-gray-500">
                {profile?.companyName || 'Organizasyon Defteri'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
