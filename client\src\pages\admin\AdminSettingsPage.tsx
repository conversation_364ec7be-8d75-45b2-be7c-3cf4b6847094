import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import { testTelegramBot } from '../../lib/telegramApi';
import {
  Settings,
  Save,
  RefreshCw,
  Globe,
  Bell,
  Loader2,
  Send
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function AdminSettingsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [hasChanges, setHasChanges] = useState(false);
  const [testingTelegram, setTestingTelegram] = useState(false);

  // Fetch system settings
  const { data: settings, isLoading, error } = useQuery({
    queryKey: ['admin', 'settings'],
    queryFn: adminApi.settings.getAll,
  });

  // Update setting mutation
  const updateSettingMutation = useMutation({
    mutationFn: ({ key, value }: { key: string; value: any }) => 
      adminApi.settings.update(key, value),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'settings'] });
      toast({
        title: "✅ Ayar güncellendi",
        description: "Sistem ayarı başarıyla güncellendi.",
        variant: "success",
      });
      setHasChanges(false);
    },
    onError: (error: any) => {
      toast({
        title: "❌ Hata",
        description: error.message || "Ayar güncellenirken bir hata oluştu.",
        variant: "destructive",
      });
    },
  });

  const handleSettingChange = (key: string, value: any) => {
    updateSettingMutation.mutate({ key, value });
    setHasChanges(true);
  };

  // Test Telegram bot function
  const handleTestTelegram = async () => {
    setTestingTelegram(true);
    try {
      const botToken = getSettingValue('telegram_bot_token', '');
      const chatId = getSettingValue('telegram_chat_id', '');

      if (!botToken || !chatId) {
        toast({
          title: "❌ Eksik Bilgi",
          description: "Bot token ve Chat ID alanları doldurulmalıdır.",
          variant: "destructive",
        });
        return;
      }

      const result = await testTelegramBot(botToken, chatId);

      if (result.success) {
        toast({
          title: "✅ Test Başarılı",
          description: "Telegram bot test mesajı başarıyla gönderildi!",
          variant: "success",
        });
      } else {
        toast({
          title: "❌ Test Başarısız",
          description: result.error || "Telegram bot test edilemedi.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "❌ Hata",
        description: "Test sırasında bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setTestingTelegram(false);
    }
  };

  const getSettingValue = (key: string, defaultValue: any = '') => {
    const setting = settings?.find(s => s.key === key);
    if (!setting) return defaultValue;
    
    try {
      return typeof setting.value === 'string' ? JSON.parse(setting.value) : setting.value;
    } catch {
      return setting.value;
    }
  };

  if (isLoading) {
    return (
      <AdminLayout title="Sistem Ayarları" subtitle="Uygulama konfigürasyonu ve sistem ayarları">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout title="Sistem Ayarları" subtitle="Uygulama konfigürasyonu ve sistem ayarları">
        <Card className="border-red-200">
          <CardContent className="p-6 text-center">
            <Settings className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              Ayarlar Yüklenemedi
            </h3>
            <p className="text-red-600">
              Sistem ayarları yüklenirken bir hata oluştu.
            </p>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Sistem Ayarları" subtitle="Uygulama konfigürasyonu ve sistem ayarları">
      <div className="space-y-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Genel Ayarlar
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="app_name">Site Başlığı</Label>
                <Input
                  id="app_name"
                  value={getSettingValue('app_name', 'EventFlow')}
                  onChange={(e) => handleSettingChange('app_name', JSON.stringify(e.target.value))}
                  className="mt-1"
                  placeholder="EventFlow - Organizasyon Yönetimi"
                />
                <p className="text-xs text-gray-500 mt-1">Browser sekmesinde görünecek başlık</p>
              </div>
              <div>
                <Label htmlFor="app_version">Uygulama Versiyonu</Label>
                <Input
                  id="app_version"
                  value={getSettingValue('app_version', '1.0.0')}
                  onChange={(e) => handleSettingChange('app_version', JSON.stringify(e.target.value))}
                  className="mt-1"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="site_description">Site Açıklaması</Label>
              <textarea
                id="site_description"
                value={getSettingValue('site_description', 'EventFlow ile etkinlik ve organizasyon yönetimini kolaylaştırın. Modern ve kullanıcı dostu arayüz ile organizasyonlarınızı verimli bir şekilde yönetin.')}
                onChange={(e) => handleSettingChange('site_description', JSON.stringify(e.target.value))}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={3}
                placeholder="Site açıklaması (SEO için önemli)"
              />
              <p className="text-xs text-gray-500 mt-1">Arama motorlarında görünecek açıklama</p>
            </div>

            <div>
              <Label htmlFor="site_keywords">Anahtar Kelimeler</Label>
              <Input
                id="site_keywords"
                value={getSettingValue('site_keywords', 'etkinlik yönetimi, organizasyon, event management, EventFlow, organizasyon defteri')}
                onChange={(e) => handleSettingChange('site_keywords', JSON.stringify(e.target.value))}
                className="mt-1"
                placeholder="anahtar kelime, organizasyon, etkinlik"
              />
              <p className="text-xs text-gray-500 mt-1">Virgülle ayrılmış anahtar kelimeler</p>
            </div>

            <div className="border-t pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="maintenance_mode">Bakım Modu</Label>
                  <p className="text-sm text-gray-600">⚠️ Henüz implementasyon tamamlanmamış</p>
                  <p className="text-xs text-red-500">Bu özellik şu anda çalışmıyor - sadece ayar olarak mevcut</p>
                </div>
                <Switch
                  id="maintenance_mode"
                  checked={getSettingValue('maintenance_mode', false)}
                  onCheckedChange={(checked) => handleSettingChange('maintenance_mode', checked)}
                  disabled
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings - Working System */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Bildirim Ayarları
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Admin paneli bildirim sistemi ayarları
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Admin Bildirimleri</Label>
                <p className="text-sm text-gray-600">
                  Güvenlik uyarıları, yeni kullanıcı kayıtları ve sistem olayları
                </p>
              </div>
              <Switch
                checked={getSettingValue('admin_notifications_enabled', true)}
                onCheckedChange={(checked) => handleSettingChange('admin_notifications_enabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Güvenlik Bildirimleri</Label>
                <p className="text-sm text-gray-600">
                  Şüpheli giriş denemeleri ve hesap kilitleme bildirimleri
                </p>
              </div>
              <Switch
                checked={getSettingValue('security_notifications_enabled', true)}
                onCheckedChange={(checked) => handleSettingChange('security_notifications_enabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Organizasyon Bildirimleri</Label>
                <p className="text-sm text-gray-600">
                  Yeni organizasyon oluşturma ve silme bildirimleri
                </p>
              </div>
              <Switch
                checked={getSettingValue('organization_notifications_enabled', true)}
                onCheckedChange={(checked) => handleSettingChange('organization_notifications_enabled', checked)}
              />
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 text-blue-700 mb-2">
                <Bell className="h-4 w-4" />
                <span className="font-medium">Bildirim Sistemi Durumu</span>
              </div>
              <p className="text-sm text-blue-600">
                ✅ Bildirim sistemi aktif ve çalışıyor. Admin panelinde gerçek zamanlı bildirimler alıyorsunuz.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Telegram Notifications Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.81-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1l-5.52 3.47-2.38-.74c-.52-.16-.53-.52.11-.77l9.3-3.58c.43-.16.81.1.67.61z"/>
              </svg>
              Telegram Bildirimleri
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Telegram bot üzerinden bildirim gönderme ayarları
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Telegram Bildirimleri</Label>
                <p className="text-sm text-gray-600">
                  Admin bildirimlerini Telegram bot'una da gönder
                </p>
              </div>
              <Switch
                checked={getSettingValue('telegram_notifications_enabled', false)}
                onCheckedChange={(checked) => handleSettingChange('telegram_notifications_enabled', checked)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="telegram_bot_token">Bot Token</Label>
                <Input
                  id="telegram_bot_token"
                  type="password"
                  value={getSettingValue('telegram_bot_token', '')}
                  onChange={(e) => handleSettingChange('telegram_bot_token', JSON.stringify(e.target.value))}
                  className="mt-1"
                  placeholder="Bot token'ınızı girin"
                />
                <p className="text-xs text-gray-500 mt-1">@BotFather'dan aldığınız bot token</p>
              </div>
              <div>
                <Label htmlFor="telegram_chat_id">Chat ID</Label>
                <Input
                  id="telegram_chat_id"
                  value={getSettingValue('telegram_chat_id', '')}
                  onChange={(e) => handleSettingChange('telegram_chat_id', JSON.stringify(e.target.value))}
                  className="mt-1"
                  placeholder="Chat ID'nizi girin"
                />
                <p className="text-xs text-gray-500 mt-1">Bildirimlerin gönderileceği chat ID</p>
              </div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 text-orange-700 mb-2">
                <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.81-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1l-5.52 3.47-2.38-.74c-.52-.16-.53-.52.11-.77l9.3-3.58c.43-.16.81.1.67.61z"/>
                </svg>
                <span className="font-medium">Telegram Bot Kurulumu</span>
              </div>
              <p className="text-sm text-orange-600 mb-2">
                📱 Telegram bot kurulumu için:
              </p>
              <ol className="text-sm text-orange-600 list-decimal list-inside space-y-1 mb-3">
                <li>@BotFather'a mesaj gönderin ve /newbot komutunu kullanın</li>
                <li>Bot token'ınızı yukarıdaki alana girin</li>
                <li>Chat ID'nizi öğrenmek için @userinfobot'a mesaj gönderin</li>
                <li>Telegram bildirimlerini aktif edin</li>
              </ol>
              <Button
                onClick={handleTestTelegram}
                disabled={testingTelegram || !getSettingValue('telegram_bot_token', '') || !getSettingValue('telegram_chat_id', '')}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                {testingTelegram ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                {testingTelegram ? 'Test Ediliyor...' : 'Bot Bağlantısını Test Et'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        {hasChanges && (
          <div className="flex justify-end">
            <Button 
              onClick={() => setHasChanges(false)}
              disabled={updateSettingMutation.isPending}
              className="flex items-center gap-2"
            >
              {updateSettingMutation.isPending ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Değişiklikleri Kaydet
            </Button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
