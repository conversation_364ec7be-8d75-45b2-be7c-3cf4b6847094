[build]
  command = "npm run build:netlify"
  publish = "dist/public"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--production=false"

[functions] 
  node_bundler = "esbuild"

# SPA routing için tüm route'ları index.html'e yönlendir
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# API çağrıları için Netlify Functions'a yönlendirme
[[redirects]]
  from = "/api/test"
  to = "/.netlify/functions/test"
  status = 200

[[redirects]]
  from = "/api/public/settings"
  to = "/.netlify/functions/public-settings"
  status = 200

# Redirects are now handled by _redirects file

# No cache for API endpoints
[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"
    Pragma = "no-cache"
    Expires = "0"

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
