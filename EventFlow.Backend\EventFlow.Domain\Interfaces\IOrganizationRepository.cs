using EventFlow.Domain.Entities;

namespace EventFlow.Domain.Interfaces;

public interface IOrganizationRepository : IRepository<Organization>
{
    Task<IEnumerable<Organization>> GetByCustomerIdAsync(Guid customerId);
    Task<IEnumerable<Organization>> GetByStatusAsync(string status);
    Task<IEnumerable<Organization>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
    Task<Organization?> GetWithDetailsAsync(Guid id);
    Task<IEnumerable<Organization>> GetAllWithDetailsAsync();
    Task<IEnumerable<Organization>> GetCalendarEventsAsync(DateTime? startDate = null, DateTime? endDate = null);
}
