import { useState } from "react";
import { Layout } from "../components/layout/Layout";
import { useOrganizations } from "../hooks/useOrganizations";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Search, Eye, Edit, Trash2, Users, Calendar, TrendingUp, CheckCircle, Clock, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import { formatCurrency, formatDate } from "../utils/formatters";
import { OrganizationDetailModal } from "../components/modals/OrganizationDetailModal";
import { OrganizationEditModal } from "../components/modals/OrganizationEditModal";
import type { OrganizationDetailsDto } from "../types";

export default function OrganizationsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationDetailsDto | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [deletingOrganization, setDeletingOrganization] = useState<OrganizationDetailsDto | null>(null);

  const { toast } = useToast();

  const { organizations, isLoading, error, updateStatus, updateOrganization, deleteOrganization, isUpdating, isDeleting } = useOrganizations();

  const filteredOrganizations = organizations?.filter(org => {
    const matchesSearch = org.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (org.customerPhone && org.customerPhone.includes(searchTerm)) ||
                         (org.customerEmail && org.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === "all" || org.status === statusFilter;

    return matchesSearch && matchesStatus;
  }) || [];

  const handleStatusChange = (orgId: string, newStatus: string) => {
    updateStatus({ id: orgId, status: newStatus }, {
      onSuccess: () => {
        toast({
          title: "✅ Başarılı",
          description: "Organizasyon durumu güncellendi",
          variant: "success",
        });
      },
      onError: (error: any) => {
        toast({
          title: "❌ Hata",
          description: error?.message || "Durum güncellenirken bir hata oluştu",
          variant: "destructive",
        });
      },
    });
  };

  const handleViewOrganization = (orgId: string) => {
    const organization = organizations?.find(org => org.id === orgId);
    if (organization) {
      setSelectedOrganization(organization);
      setIsDetailModalOpen(true);
    }
  };

  const handleEditOrganization = (orgId: string) => {
    const organization = organizations?.find(org => org.id === orgId);
    if (organization) {
      setSelectedOrganization(organization);
      setIsEditModalOpen(true);
    }
  };

  const handleDeleteOrganization = (orgId: string) => {
    const organization = organizations?.find(org => org.id === orgId);
    if (organization) {
      setDeletingOrganization(organization);
    }
  };

  const confirmDelete = () => {
    if (deletingOrganization) {
      deleteOrganization(deletingOrganization.id, {
        onSuccess: () => {
          toast({
            title: "✅ Başarılı",
            description: "Organizasyon başarıyla silindi",
            variant: "success",
          });
          setDeletingOrganization(null);
        },
        onError: (error: any) => {
          toast({
            title: "❌ Hata",
            description: error?.message || "Organizasyon silinirken bir hata oluştu",
            variant: "destructive",
          });
        },
      });
    }
  };

  const handleEditSave = (data: any) => {
    if (selectedOrganization) {
      const updateData = {
        customerName: data.customerName,
        customerEmail: data.customerEmail,
        customerPhone: data.customerPhone,
        eventDate: data.eventDate ? new Date(data.eventDate).toISOString() : undefined,
        status: data.status,
        discountPercentage: data.discountPercentage,
        notes: data.notes,
      };
      updateOrganization({ id: selectedOrganization.id, data: updateData }, {
        onSuccess: () => {
          toast({
            title: "✅ Başarılı",
            description: "Organizasyon başarıyla güncellendi",
            variant: "success",
          });
          setIsEditModalOpen(false);
          setSelectedOrganization(null);
        },
        onError: (error: any) => {
          toast({
            title: "❌ Hata",
            description: error?.message || "Organizasyon güncellenirken bir hata oluştu",
            variant: "destructive",
          });
        },
      });
    }
  };



  if (isLoading) {
    return (
      <Layout title="Organizasyonlar" subtitle="Tüm organizasyonlar ve durumları">
        <div className="flex justify-center items-center h-96">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Organizasyonlar" subtitle="Tüm organizasyonlar ve durumları">
        <div className="flex justify-center items-center h-96">
          <p className="text-red-500">Organizasyonlar yüklenirken hata oluştu</p>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Layout title="Organizasyonlar" subtitle="Tüm organizasyonlar ve durumları">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
          <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
            {/* Header Section */}
            <div className="text-center space-y-4">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl shadow-lg mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900">Organizasyon Yönetimi</h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Müşterilerinizin organizasyonlarını takip edin, durumlarını güncelleyin ve detaylarını yönetin
              </p>
              {/* Force rebuild comment - fix syntax error */}
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Toplam Organizasyon</p>
                    <p className="text-3xl font-bold text-gray-900">{organizations?.length || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Kesinleşen</p>
                    <p className="text-3xl font-bold text-green-600">{organizations?.filter(o => o.status === 'Kesinleşti').length || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Teklif Aşamasında</p>
                    <p className="text-3xl font-bold text-yellow-600">{organizations?.filter(o => o.status === 'Teklif').length || 0}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <Clock className="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ortalama Tutar</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {formatCurrency((organizations?.reduce((sum, o) => sum + (o.totalPrice || 0), 0) || 0) / (organizations?.length || 1))}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-2xl p-6 shadow-lg border-0">
              <div className="flex flex-col lg:flex-row gap-4 justify-between items-start lg:items-center">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Müşteri ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 h-12 border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200 text-lg"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-[200px] h-12 border-2 border-gray-200 focus:border-purple-500 rounded-xl">
                    <SelectValue placeholder="Durum filtrele" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="Teklif">Teklif</SelectItem>
                    <SelectItem value="Kesinleşti">Kesinleşti</SelectItem>
                    <SelectItem value="İptal">İptal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

        {/* Organizations Grid */}
        {filteredOrganizations.length === 0 ? (
          <div className="bg-white rounded-2xl p-12 shadow-lg border-0 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Users className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              {searchTerm || statusFilter !== "all"
                ? "Arama sonucu bulunamadı"
                : "Henüz organizasyon bulunmuyor"}
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              {searchTerm || statusFilter !== "all"
                ? "Arama kriterinize uygun organizasyon bulunamadı. Farklı filtreler deneyin."
                : "İlk organizasyonunuzu oluşturmak için yeni organizasyon sayfasını ziyaret edin."
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredOrganizations.map((org) => (
              <div
                key={org.id}
                className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300 group"
              >
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-purple-600 transition-colors">
                      {org.customerName}
                    </h3>
                    <p className="text-gray-600 text-sm mb-1">{org.customerEmail}</p>
                    <p className="text-gray-600 text-sm">{org.customerPhone}</p>
                  </div>
                  <Badge
                    variant={org.status === 'Kesinleşti' ? 'default' : org.status === 'Teklif' ? 'secondary' : 'destructive'}
                    className={`ml-3 ${
                      org.status === 'Kesinleşti' ? 'bg-green-100 text-green-800' :
                      org.status === 'Teklif' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}
                  >
                    {org.status === 'Kesinleşti' && <CheckCircle className="w-3 h-3 mr-1" />}
                    {org.status === 'Teklif' && <Clock className="w-3 h-3 mr-1" />}
                    {org.status === 'İptal' && <XCircle className="w-3 h-3 mr-1" />}
                    {org.status}
                  </Badge>
                </div>

                {/* Details */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    {org.eventDate ? formatDate(org.eventDate) : "Tarih belirtilmemiş"}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      {org.services?.length || 0} hizmet
                    </span>
                    <span className="text-xl font-bold text-purple-600">
                      {formatCurrency(org.totalPrice || 0)}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4 border-t border-gray-100">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewOrganization(org.id)}
                    className="flex-1 hover:bg-blue-50 hover:text-blue-600 rounded-lg"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Görüntüle
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditOrganization(org.id)}
                    className="flex-1 hover:bg-green-50 hover:text-green-600 rounded-lg"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Düzenle
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteOrganization(org.id)}
                    disabled={isDeleting}
                    className="flex-1 hover:bg-red-50 hover:text-red-600 rounded-lg"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Sil
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
        </div>
      </div>
    </Layout>

    {/* Modals */}
    <OrganizationDetailModal
      organization={selectedOrganization}
      open={isDetailModalOpen}
      onOpenChange={setIsDetailModalOpen}
    />

    <OrganizationEditModal
      organization={selectedOrganization}
      open={isEditModalOpen}
      onOpenChange={setIsEditModalOpen}
      onSave={handleEditSave}
      isLoading={isUpdating}
    />

    {/* Delete Confirmation Dialog */}
    <AlertDialog open={!!deletingOrganization} onOpenChange={() => setDeletingOrganization(null)}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Organizasyonu Sil</AlertDialogTitle>
          <AlertDialogDescription>
            "{deletingOrganization?.customerName}" müşterisine ait organizasyonu silmek istediğinizden emin misiniz?
            Bu işlem geri alınamaz.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>İptal</AlertDialogCancel>
          <AlertDialogAction
            onClick={confirmDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Siliniyor..." : "Sil"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </>
);
}
                      <div>
                        <p className="font-medium">{org.customerName}</p>
                        <p className="text-sm text-gray-600">{org.customerEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>{org.customerPhone}</TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{formatDate(org.eventDate)}</p>
                        <p className="text-sm text-gray-600">
                          {org.status}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline">
                        {org.services.length} hizmet
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div>
                        <p className="font-medium">{formatCurrency(org.totalAmount)}</p>
                        {org.discountPercentage > 0 && (
                          <p className="text-sm text-gray-600">
                            %{org.discountPercentage} iskonto
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Select
                        value={org.status}
                        onValueChange={(newStatus) => handleStatusChange(org.id, newStatus)}
                        disabled={isUpdating}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue>
                            <Badge variant={getStatusBadgeVariant(org.status)}>
                              {org.status}
                            </Badge>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Teklif">Teklif</SelectItem>
                          <SelectItem value="Kesinleşti">Kesinleşti</SelectItem>
                          <SelectItem value="İptal">İptal</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex justify-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewOrganization(org.id)}
                          title="Görüntüle"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditOrganization(org.id)}
                          title="Düzenle"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteOrganization(org.id)}
                          title="Sil"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          disabled={isDeleting}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Modal'lar */}
      <OrganizationDetailModal
        organization={selectedOrganization}
        open={isDetailModalOpen}
        onOpenChange={setIsDetailModalOpen}
      />

      <OrganizationEditModal
        organization={selectedOrganization}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        onSave={handleEditSave}
        isLoading={isUpdating}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingOrganization} onOpenChange={() => setDeletingOrganization(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Organizasyonu Sil</AlertDialogTitle>
            <AlertDialogDescription>
              "{deletingOrganization?.customerName}" müşterisine ait organizasyonu silmek istediğinizden emin misiniz?
              Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Siliniyor..." : "Sil"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
}
