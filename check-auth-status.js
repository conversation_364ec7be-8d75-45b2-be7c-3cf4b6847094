// Script to check current auth status
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAuthStatus() {
  try {
    console.log('🔍 Checking auth status...');
    
    // Check current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session error:', sessionError);
      return;
    }
    
    if (!session) {
      console.log('❌ No active session found');
      return;
    }
    
    console.log('✅ Active session found for user:', session.user.id);
    console.log('📧 Email:', session.user.email);
    
    // Check if user has profile
    const { data: profile, error: profileError } = await supabase
      .from('eventflow_profiles')
      .select('*')
      .eq('id', session.user.id)
      .maybeSingle();
    
    if (profileError) {
      console.error('❌ Profile error:', profileError);
    } else if (profile) {
      console.log('✅ Profile found:', profile);
    } else {
      console.log('❌ No profile found for user');
    }
    
    // Check if user is admin
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', session.user.id)
      .eq('is_active', true)
      .maybeSingle();
    
    if (adminError) {
      console.error('❌ Admin check error:', adminError);
    } else if (adminUser) {
      console.log('✅ Admin user found:', adminUser);
    } else {
      console.log('❌ User is not an admin');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkAuthStatus();
