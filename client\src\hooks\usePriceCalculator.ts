import { useState, useEffect } from "react";
import type { Service } from "../types";

export function usePriceCalculator(services: Service[]) {
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
  const [discountPercentage, setDiscountPercentage] = useState<number>(0);
  const [totalPrice, setTotalPrice] = useState<number>(0);
  const [discountAmount, setDiscountAmount] = useState<number>(0);
  const [finalPrice, setFinalPrice] = useState<number>(0);

  const selectedServices = services.filter(service => 
    selectedServiceIds.includes(service.id)
  );

  useEffect(() => {
    const newTotalPrice = selectedServices.reduce((sum, service) => {
      return sum + service.basePrice;
    }, 0);

    const newDiscountAmount = (newTotalPrice * discountPercentage) / 100;
    const newFinalPrice = newTotalPrice - newDiscountAmount;

    setTotalPrice(newTotalPrice);
    setDiscountAmount(newDiscountAmount);
    setFinalPrice(newFinalPrice);
  }, [selectedServices, discountPercentage]);

  const toggleService = (serviceId: string) => {
    setSelectedServiceIds(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const clearSelection = () => {
    setSelectedServiceIds([]);
    setDiscountPercentage(0);
  };

  const isServiceSelected = (serviceId: string) => {
    return selectedServiceIds.includes(serviceId);
  };

  return {
    selectedServiceIds,
    selectedServices,
    discountPercentage,
    totalPrice,
    discountAmount,
    finalPrice,
    setDiscountPercentage,
    toggleService,
    clearSelection,
    isServiceSelected,
  };
}
