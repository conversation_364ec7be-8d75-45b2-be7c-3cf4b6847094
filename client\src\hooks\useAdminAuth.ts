import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import type { AdminUser } from '../lib/adminApi';

interface AdminAuthState {
  isAdmin: boolean;
  adminUser: AdminUser | null;
  loading: boolean;
  error: string | null;
  permissions: Record<string, any>;
  role: 'super_admin' | 'admin' | 'moderator' | null;
}

export const useAdminAuth = () => {
  const { user, loading: authLoading } = useAuth();
  const [adminState, setAdminState] = useState<AdminAuthState>({
    isAdmin: false,
    adminUser: null,
    loading: true,
    error: null,
    permissions: {},
    role: null
  });

  useEffect(() => {
    let mounted = true;

    const checkAdminStatus = async () => {
      if (authLoading) return;
      
      if (!user) {
        if (mounted) {
          setAdminState({
            isAdmin: false,
            adminUser: null,
            loading: false,
            error: null,
            permissions: {},
            role: null
          });
        }
        return;
      }

      try {
        console.log('🔍 Checking admin status for user:', user.id, 'Email:', user.email);

        // First check if admin user exists
        const { data: adminUser, error: adminError } = await supabase
          .from('admin_users')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .maybeSingle(); // Use maybeSingle instead of single

        console.log('🔍 Admin query result:', { adminUser, adminError });

        if (adminError) {
          console.error('❌ Admin query error:', adminError);
          if (mounted) {
            setAdminState({
              isAdmin: false,
              adminUser: null,
              loading: false,
              error: `Admin auth check failed: ${adminError.message}`,
              permissions: {},
              role: null
            });
          }
          return;
        }

        if (!adminUser) {
          // No admin record found - user is not an admin
          console.log('ℹ️ User is not an admin');
          if (mounted) {
            setAdminState({
              isAdmin: false,
              adminUser: null,
              loading: false,
              error: 'Admin yetkileriniz bulunamadı. Lütfen sistem yöneticisi ile iletişime geçin.',
              permissions: {},
              role: null
            });
          }
          return;
        }

        console.log('✅ Admin user found:', adminUser.role);

        // Get user profile separately (optional)
        const { data: profile } = await supabase
          .from('eventflow_profiles')
          .select('first_name, last_name, company_name')
          .eq('id', user.id)
          .maybeSingle();

        if (mounted) {
          setAdminState({
            isAdmin: true,
            adminUser: {
              ...adminUser,
              email: user.email,
              first_name: profile?.first_name || 'Admin',
              last_name: profile?.last_name || 'User',
              company_name: profile?.company_name || 'Admin Company'
            },
            loading: false,
            error: null,
            permissions: adminUser.permissions || {},
            role: adminUser.role
          });
        }

      } catch (error) {
        console.error('❌ Admin auth check failed:', error);
        if (mounted) {
          setAdminState({
            isAdmin: false,
            adminUser: null,
            loading: false,
            error: error instanceof Error ? error.message : 'Admin auth check failed',
            permissions: {},
            role: null
          });
        }
      }
    };

    checkAdminStatus();

    return () => {
      mounted = false;
    };
  }, [user, authLoading]);

  // Helper functions
  const hasPermission = (permission: string): boolean => {
    if (!adminState.isAdmin || !adminState.adminUser) return false;
    
    // Super admins have all permissions
    if (adminState.role === 'super_admin') return true;
    
    // Check specific permission
    return adminState.permissions[permission] === true;
  };

  const hasRole = (role: AdminUser['role']): boolean => {
    return adminState.role === role;
  };

  const hasAnyRole = (roles: AdminUser['role'][]): boolean => {
    return adminState.role ? roles.includes(adminState.role) : false;
  };

  const canManageUsers = (): boolean => {
    return hasRole('super_admin') || hasPermission('manage_users');
  };

  const canManageSettings = (): boolean => {
    return hasRole('super_admin') || hasPermission('manage_settings');
  };

  const canViewLogs = (): boolean => {
    return hasAnyRole(['super_admin', 'admin']) || hasPermission('view_logs');
  };

  const canViewStats = (): boolean => {
    return hasAnyRole(['super_admin', 'admin', 'moderator']) || hasPermission('view_stats');
  };

  return {
    ...adminState,
    // Helper functions
    hasPermission,
    hasRole,
    hasAnyRole,
    canManageUsers,
    canManageSettings,
    canViewLogs,
    canViewStats,
    // Computed properties
    isSuperAdmin: adminState.role === 'super_admin',
    isRegularAdmin: adminState.role === 'admin',
    isModerator: adminState.role === 'moderator',
    displayName: adminState.adminUser ? 
      `${adminState.adminUser.first_name || ''} ${adminState.adminUser.last_name || ''}`.trim() || 
      adminState.adminUser.email || 'Admin User' : 
      'Unknown'
  };
};

// Hook for admin-only components
export const useRequireAdmin = () => {
  const adminAuth = useAdminAuth();
  
  useEffect(() => {
    if (!adminAuth.loading && !adminAuth.isAdmin) {
      // Redirect to home or show error
      window.location.href = '/';
    }
  }, [adminAuth.loading, adminAuth.isAdmin]);

  return adminAuth;
};

// Hook for specific role requirements
export const useRequireRole = (requiredRole: AdminUser['role']) => {
  const adminAuth = useAdminAuth();
  
  useEffect(() => {
    if (!adminAuth.loading && (!adminAuth.isAdmin || !adminAuth.hasRole(requiredRole))) {
      // Redirect to admin dashboard or show error
      window.location.href = '/admin';
    }
  }, [adminAuth.loading, adminAuth.isAdmin, adminAuth.role, requiredRole]);

  return adminAuth;
};

// Hook for permission-based access
export const useRequirePermission = (permission: string) => {
  const adminAuth = useAdminAuth();
  
  useEffect(() => {
    if (!adminAuth.loading && (!adminAuth.isAdmin || !adminAuth.hasPermission(permission))) {
      // Redirect to admin dashboard or show error
      window.location.href = '/admin';
    }
  }, [adminAuth.loading, adminAuth.isAdmin, permission]);

  return adminAuth;
};
