using Microsoft.AspNetCore.Http;

namespace EventFlow.Infrastructure.Extensions
{
    public static class HttpContextExtensions
    {
        public static string? GetUserId(this HttpContext httpContext)
        {
            // JWT'den gelen user ID'yi al
            var userIdFromClaims = httpContext.User?.FindFirst("sub")?.Value;
            if (!string.IsNullOrEmpty(userIdFromClaims))
            {
                return userIdFromClaims;
            }

            // Context items'dan al (OnTokenValidated event'inde set edilmiş)
            if (httpContext.Items.TryGetValue("UserId", out var userId) && userId is string userIdString)
            {
                return userIdString;
            }

            return null;
        }

        public static bool IsAuthenticated(this HttpContext httpContext)
        {
            return !string.IsNullOrEmpty(httpContext.GetUserId());
        }
    }
}
