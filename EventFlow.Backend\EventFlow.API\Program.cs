using Microsoft.EntityFrameworkCore;
using EventFlow.Infrastructure.Data;
using EventFlow.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Database Configuration - Temporarily disabled due to DNS issues
// builder.Services.AddDbContext<EventFlowDbContext>(options =>
//     options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// HTTP Context Accessor
builder.Services.AddHttpContextAccessor();

// Supabase REST API Service
builder.Services.AddHttpClient<SupabaseService>();
builder.Services.AddScoped<SupabaseService>();

// JWT Authentication Configuration for Supabase
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false; // Development için
        options.SaveToken = true;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = "https://yqnhitvatsnrjdabsgzv.supabase.co/auth/v1",
            ValidAudience = "authenticated",
            IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
            {
                // Supabase JWT secret - Supabase'de bu genellikle anon key ile aynıdır
                var key = builder.Configuration["Supabase:JwtSecret"] ?? "your-jwt-secret-here";
                return new[] { new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key)) };
            },
            ClockSkew = TimeSpan.Zero
        };

        // Supabase JWT'leri için özel event handler
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = context =>
            {
                // JWT'den user ID'yi al ve context'e ekle
                var userId = context.Principal?.FindFirst("sub")?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    context.HttpContext.Items["UserId"] = userId;
                }
                return Task.CompletedTask;
            },
            OnAuthenticationFailed = context =>
            {
                Console.WriteLine($"Authentication failed: {context.Exception.Message}");
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddAuthorization();

// CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.WithOrigins(
                "http://localhost:5173",
                "http://localhost:3000",
                "http://localhost:5000",
                "https://organizasyon.netlify.app",
                "https://bespoke-zabaione-dd3853.netlify.app"
              )
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "Organizasyon Defteri API", Version = "v1" });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "EventFlow API v1");
        c.RoutePrefix = "swagger";
    });
}

// Enable CORS
app.UseCors();

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }));

app.Run();
