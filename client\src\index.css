@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-friendly Calendar Styles */
@media (max-width: 768px) {
  .rbc-calendar {
    font-size: 12px;
  }

  .rbc-header {
    padding: 4px 2px;
    font-size: 11px;
    font-weight: 600;
  }

  .rbc-date-cell {
    padding: 2px;
    font-size: 11px;
  }

  .rbc-event {
    padding: 1px 3px;
    font-size: 10px;
    border-radius: 3px;
  }

  .rbc-month-view {
    border: none;
  }

  .rbc-month-row {
    border-bottom: 1px solid #e5e7eb;
  }

  .rbc-day-bg {
    border-right: 1px solid #e5e7eb;
  }

  .rbc-today {
    background-color: #dbeafe;
  }

  .rbc-off-range-bg {
    background-color: #f9fafb;
  }

  .rbc-toolbar {
    margin-bottom: 10px;
  }

  .rbc-toolbar button {
    padding: 4px 8px;
    font-size: 12px;
  }
}

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  /* Fix for mobile viewport and scroll issues */
  html {
    height: 100%;
    /* iOS Safari viewport fix */
    height: -webkit-fill-available;
    /* Prevent overscroll bounce */
    overscroll-behavior: none;
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
    /* CSS custom properties for dynamic viewport */
    --vh: 1vh;
    --vw: 1vw;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    /* iOS Safari viewport fix */
    min-height: -webkit-fill-available;
    /* Prevent overscroll bounce */
    overscroll-behavior: none;
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Ensure proper height calculation */
    height: 100%;
  }
}

/* React Big Calendar Custom Styles */
.rbc-calendar {
  font-family: inherit;
}

.rbc-event {
  border-radius: 4px;
  padding: 2px 5px;
  font-size: 12px;
  font-weight: 500;
}

.rbc-event-label {
  font-size: 10px;
  font-weight: 400;
}

.rbc-toolbar {
  margin-bottom: 1rem;
}

.rbc-toolbar button {
  padding: 0.5rem 1rem;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.rbc-toolbar button:hover {
  background: hsl(var(--accent));
}

.rbc-toolbar button.rbc-active {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-color: hsl(var(--primary));
}

.rbc-month-view,
.rbc-time-view {
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  background: hsl(var(--background));
}

.rbc-month-header {
  background: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-header {
  padding: 0.75rem 0.5rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-date-cell {
  padding: 0.5rem;
  color: hsl(var(--muted-foreground));
}

.rbc-date-cell.rbc-now {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rbc-off-range {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

.rbc-today {
  background: hsl(var(--accent));
}

/* Modern utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Modern Calendar Styles */
.modern-calendar {
  /* Calendar animations */
  .calendar-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .calendar-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  /* Event hover effects */
  .event-card {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .event-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Date cell hover effects */
  .date-cell {
    transition: all 0.2s ease-in-out;
  }

  .date-cell:hover {
    transform: scale(1.02);
  }

  /* Smooth transitions for view changes */
  .view-transition {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Calendar Optimizations */
@media (max-width: 768px) {
  .modern-calendar {
    /* Improve touch targets */
    .date-cell {
      min-height: 60px;
      touch-action: manipulation;
    }

    /* Optimize event display */
    .event-card {
      font-size: 11px;
      padding: 2px 4px;
      margin-bottom: 1px;
    }

    /* Better spacing for mobile */
    .calendar-grid {
      gap: 2px;
    }

    /* Swipe indicators */
    .swipe-indicator {
      opacity: 0.6;
      transition: opacity 0.2s ease-in-out;
    }

    .swipe-indicator.active {
      opacity: 1;
    }
  }
}

/* Smooth transitions for all interactive elements */
@layer utilities {
  .transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Mobile Optimizations - Comprehensive mobile scroll and layout fixes */
@media (max-width: 768px) {
  /* Root element fixes for mobile */
  html {
    /* Prevent zoom on input focus */
    -webkit-text-size-adjust: 100%;
    /* Improve touch responsiveness */
    touch-action: manipulation;
  }

  body {
    /* Prevent overscroll bounce */
    overscroll-behavior-y: none;
    /* Improve momentum scrolling */
    -webkit-overflow-scrolling: touch;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Fix for iOS Safari address bar */
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  /* Force hardware acceleration and fix positioning for mobile */
  header[class*="sticky"] {
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50 !important;
    will-change: transform;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    /* Prevent touch callout */
    -webkit-touch-callout: none;
    /* Improve touch action */
    touch-action: manipulation;
  }

  /* Header visibility states */
  header.header-hidden {
    transform: translateY(-100%);
  }

  header.header-visible {
    transform: translateY(0);
  }

  /* Main content container fixes */
  .main-content-mobile-padding {
    padding-top: 80px; /* Approximate header height */
    /* Ensure proper scroll container */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* Prevent overscroll */
    overscroll-behavior: contain;
    /* Fix height calculation */
    min-height: calc(100vh - 80px);
    min-height: calc(-webkit-fill-available - 80px);
    /* Use dynamic viewport height when available */
    min-height: calc((var(--vh, 1vh) * 100) - 80px);
  }

  /* Layout container fixes */
  .flex.h-screen {
    height: 100vh;
    height: -webkit-fill-available;
    /* Use dynamic viewport height when available */
    height: calc(var(--vh, 1vh) * 100);
  }

  /* Scroll container optimizations */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  /* Card and component touch improvements */
  .bg-white, .bg-gradient-to-br {
    /* Improve touch responsiveness */
    touch-action: manipulation;
  }

  /* Button and interactive element improvements */
  button, .cursor-pointer, [role="button"] {
    /* Prevent touch delay */
    touch-action: manipulation;
    /* Prevent touch callout */
    -webkit-touch-callout: none;
    /* Prevent text selection */
    -webkit-user-select: none;
    user-select: none;
  }

  /* Input and form improvements */
  input, textarea, select {
    /* Prevent zoom on focus */
    font-size: 16px;
    /* Improve touch action */
    touch-action: manipulation;
  }
}
