import { createClient } from '@supabase/supabase-js'

// Support both VITE_ prefixed and non-prefixed environment variables (for Netlify compatibility)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || import.meta.env.SUPABASE_URL || 'https://yqnhitvatsnrjdabsgzv.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc'
const supabaseServiceKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.SUPABASE_SERVICE_ROLE_KEY

// Mobile-safe storage implementation
const createMobileSafeStorage = () => {
  // Check if we're in a mobile browser that might have localStorage issues
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  if (isMobile) {
    // For mobile, use a hybrid approach with fallback
    return {
      getItem: (key: string) => {
        try {
          return window.localStorage.getItem(key);
        } catch (error) {
          console.warn('localStorage getItem failed, using sessionStorage:', error);
          try {
            return window.sessionStorage.getItem(key);
          } catch (sessionError) {
            console.error('Both localStorage and sessionStorage failed:', sessionError);
            return null;
          }
        }
      },
      setItem: (key: string, value: string) => {
        try {
          window.localStorage.setItem(key, value);
        } catch (error) {
          console.warn('localStorage setItem failed, using sessionStorage:', error);
          try {
            window.sessionStorage.setItem(key, value);
          } catch (sessionError) {
            console.error('Both localStorage and sessionStorage failed:', sessionError);
          }
        }
      },
      removeItem: (key: string) => {
        try {
          window.localStorage.removeItem(key);
        } catch (error) {
          console.warn('localStorage removeItem failed, using sessionStorage:', error);
          try {
            window.sessionStorage.removeItem(key);
          } catch (sessionError) {
            console.error('Both localStorage and sessionStorage failed:', sessionError);
          }
        }
      }
    };
  }

  // For desktop, use localStorage directly
  return window.localStorage;
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    storage: createMobileSafeStorage(),
    storageKey: 'sb-yqnhitvatsnrjdabsgzv-auth-token',
    debug: import.meta.env.DEV,
    // Mobile-specific settings
    ...((/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) && {
      // Increase timeout for mobile networks
      timeout: 30000,
      // Retry failed requests
      retryAttempts: 3
    })
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'organizasyon-app',
      'Cache-Control': 'no-cache',
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // Add mobile-specific headers
      ...((/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) && {
        'X-Mobile-Client': 'true',
        'Accept-Encoding': 'gzip, deflate, br'
      })
    }
  }
})

// Admin client with service role key for admin operations
export const supabaseAdmin = supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  global: {
    headers: {
      'X-Client-Info': 'organizasyon-admin',
    }
  }
}) : null

// Debug environment variables and test connection
console.log('🔧 Supabase configuration:', {
  url: supabaseUrl,
  hasAnonKey: !!supabaseAnonKey,
  anonKeyLength: supabaseAnonKey?.length,
  hasServiceKey: !!supabaseServiceKey,
  isDev: import.meta.env.DEV,
  envVars: {
    VITE_SUPABASE_URL: !!import.meta.env.VITE_SUPABASE_URL,
    SUPABASE_URL: !!import.meta.env.SUPABASE_URL,
    VITE_SUPABASE_ANON_KEY: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
    SUPABASE_ANON_KEY: !!import.meta.env.SUPABASE_ANON_KEY
  }
})

// Test Supabase connection
supabase.auth.getSession().then(({ data, error }) => {
  if (error) {
    console.error('❌ Supabase connection error:', error);
  } else {
    console.log('✅ Supabase connection successful:', !!data.session);
  }
}).catch(err => {
  console.error('❌ Supabase connection failed:', err);
})

// Session storage helper functions
export const sessionHelpers = {
  // Check if session exists in storage
  hasStoredSession: () => {
    try {
      const stored = localStorage.getItem('sb-yqnhitvatsnrjdabsgzv-auth-token')
      return !!stored && stored !== 'null'
    } catch {
      return false
    }
  },

  // Clear session storage
  clearSession: () => {
    try {
      localStorage.removeItem('sb-yqnhitvatsnrjdabsgzv-auth-token')
      sessionStorage.removeItem('sb-yqnhitvatsnrjdabsgzv-auth-token')
    } catch (error) {
      console.warn('Failed to clear session storage:', error)
    }
  }
}

export type Database = {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          first_name: string
          last_name: string
          company_name: string
          phone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          first_name: string
          last_name: string
          company_name: string
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          company_name?: string
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      eventflow_customers: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          user_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      eventflow_services: {
        Row: {
          id: string
          name: string
          description: string | null
          base_price: number
          is_active: boolean | null
          user_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          base_price: number
          is_active?: boolean | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          base_price?: number
          is_active?: boolean | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      eventflow_organizations: {
        Row: {
          id: string
          customer_id: string
          event_date: string
          status: string | null
          discount_percentage: number | null
          total_amount: number
          notes: string | null
          user_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          event_date: string
          status?: string | null
          discount_percentage?: number | null
          total_amount: number
          notes?: string | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          event_date?: string
          status?: string | null
          discount_percentage?: number | null
          total_amount?: number
          notes?: string | null
          user_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
