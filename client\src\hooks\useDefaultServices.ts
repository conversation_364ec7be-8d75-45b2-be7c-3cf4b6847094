import { useEffect, useState } from 'react';
import { useServices } from './useServices';
import { useAuth } from '../contexts/AuthContext';
import { DEFAULT_SERVICES } from '../data/defaultServices';

export function useDefaultServices() {
  const { user, profile } = useAuth();
  const { services, createService, isLoading } = useServices();
  const [isAddingDefaults, setIsAddingDefaults] = useState(false);

  // Otomatik hizmet ekleme özelliği kaldırıldı
  // Artık sadece manuel olarak "Hızlı Hizmet Ekle" butonu ile çalışacak

  const addAllDefaultServices = async () => {
    setIsAddingDefaults(true);
    
    try {
      let successCount = 0;
      let errorCount = 0;

      for (const service of DEFAULT_SERVICES) {
        try {
          createService({
            name: service.name,
            description: service.description,
            basePrice: service.basePrice,
            isActive: true
          });
          successCount++;
          await new Promise(resolve => setTimeout(resolve, 150));
        } catch (error) {
          errorCount++;
          console.error('Error adding service:', service.name, error);
        }
      }

      // Success and error handling will be done at component level
    } catch (error) {
      console.error('Error adding all default services:', error);
      // Error handling will be done at component level
    } finally {
      setIsAddingDefaults(false);
    }
  };

  return {
    isAddingDefaults,
    addAllDefaultServices
  };
}
