import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Loader2, Mail, Lock, Eye, EyeOff, LogIn } from 'lucide-react'

interface LoginFormProps {
  onToggleMode: () => void
}

export const LoginForm: React.FC<LoginFormProps> = ({ onToggleMode }) => {
  const { signIn, emailConfirmationSent, resendConfirmation } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showEmailConfirmation, setShowEmailConfirmation] = useState(false)
  const [resendingEmail, setResendingEmail] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { error } = await signIn(email, password)

    if (error) {
      setError(error.message)

      // Check if it's an email confirmation error
      if (error.message.includes('Email adresinizi doğrulamanız gerekiyor')) {
        setShowEmailConfirmation(true)
      }
    }

    setLoading(false)
  }

  const handleResendConfirmation = async () => {
    if (!email) {
      setError('Lütfen email adresinizi girin')
      return
    }

    setResendingEmail(true)
    setError(null)

    try {
      const { error } = await resendConfirmation(email)
      if (error) {
        setError(error.message)
      } else {
        setError(null)
        // Show success message
        setShowEmailConfirmation(true)
      }
    } catch (error) {
      setError('Email gönderilirken bir hata oluştu')
    } finally {
      setResendingEmail(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="text-center pb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <LogIn className="w-8 h-8 text-white" />
        </div>
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Hoş Geldiniz
        </CardTitle>
        <CardDescription className="text-gray-600 text-base">
          Hesabınıza giriş yaparak organizasyon yönetimine başlayın
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive" className="border-red-200 bg-red-50">
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          {showEmailConfirmation && (
            <Alert className="border-blue-200 bg-blue-50">
              <Mail className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div className="space-y-3">
                  <p className="font-medium">Email Doğrulaması Gerekli</p>
                  <p className="text-sm">
                    Hesabınıza giriş yapabilmek için email adresinizi doğrulamanız gerekiyor.
                    Email kutunuzu kontrol edin ve doğrulama linkine tıklayın.
                  </p>
                  <div className="flex gap-2 pt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleResendConfirmation}
                      disabled={resendingEmail || !email}
                      className="text-blue-600 border-blue-300 hover:bg-blue-50"
                    >
                      {resendingEmail ? (
                        <>
                          <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                          Gönderiliyor...
                        </>
                      ) : (
                        'Tekrar Gönder'
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = '/email-confirmation'}
                      className="text-blue-600 border-blue-300 hover:bg-blue-50"
                    >
                      Doğrulama Sayfası
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="text-gray-700 font-medium">
              E-posta Adresi
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={loading}
                className="pl-11 h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl transition-all duration-200"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-gray-700 font-medium">
              Şifre
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={loading}
                className="pl-11 pr-11 h-12 border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl transition-all duration-200"
                placeholder="Şifrenizi girin"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                disabled={loading}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Giriş yapılıyor...
              </>
            ) : (
              <>
                <LogIn className="mr-2 h-5 w-5" />
                Giriş Yap
              </>
            )}
          </Button>

          <div className="text-center pt-4">
            <p className="text-gray-600 mb-2">Henüz hesabınız yok mu?</p>
            <Button
              type="button"
              variant="outline"
              onClick={onToggleMode}
              disabled={loading}
              className="border-2 border-gray-200 hover:border-blue-500 hover:text-blue-600 transition-all duration-200 rounded-xl"
            >
              Ücretsiz Hesap Oluşturun
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
