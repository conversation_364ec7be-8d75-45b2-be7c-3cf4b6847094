import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { api } from "../lib/api";
import { supabase } from "../lib/supabase";
import type { Service } from "../types";

export function useServices() {
  const queryClient = useQueryClient();

  const servicesQuery = useQuery({
    queryKey: ["services"],
    queryFn: api.services.getAll,
    staleTime: 10 * 1000, // 10 saniye fresh tut
    refetchOnWindowFocus: true,
  });

  // Realtime subscription for services
  useEffect(() => {
    const channel = supabase
      .channel('services-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'eventflow_services'
        },
        (payload) => {
          console.log('🔄 Services realtime update:', payload);
          // Invalidate queries to refetch data
          queryClient.invalidateQueries({ queryKey: ["services"] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  const createServiceMutation = useMutation({
    mutationFn: api.services.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  const updateServiceMutation = useMutation({
    mutationFn: ({ id, ...service }: { id: string } & Partial<Omit<Service, "id" | "createdAt">>) =>
      api.services.update(id, service),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  const deleteServiceMutation = useMutation({
    mutationFn: api.services.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  return {
    services: servicesQuery.data || [],
    isLoading: servicesQuery.isLoading,
    error: servicesQuery.error,
    createService: createServiceMutation.mutate,
    updateService: updateServiceMutation.mutate,
    deleteService: deleteServiceMutation.mutate,
    isCreating: createServiceMutation.isPending,
    isUpdating: updateServiceMutation.isPending,
    isDeleting: deleteServiceMutation.isPending,
  };
}
