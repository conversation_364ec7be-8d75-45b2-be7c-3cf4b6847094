import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { supabase } from "./supabase";

// API Base URL configuration
const getApiBaseUrl = () => {
  // In development, use the dev API URL or fallback to localhost
  if (import.meta.env.DEV) {
    return import.meta.env.VITE_DEV_API_BASE_URL || 'http://localhost:5153';
  }
  // In production, use Netlify Functions (relative URLs)
  return '';
};

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    console.error(`API Error: ${res.status} ${res.statusText}`, text);
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const apiBaseUrl = getApiBaseUrl();
  let backendUrl = url.startsWith('/api') ? `${apiBaseUrl}${url}` : url;

  // Add cache busting for GET requests in production
  if (method === 'GET' && !import.meta.env.DEV) {
    const separator = backendUrl.includes('?') ? '&' : '?';
    backendUrl += `${separator}_t=${Date.now()}`;
  }

  // Get auth token from Supabase
  const { data: { session } } = await supabase.auth.getSession();
  const authToken = session?.access_token;

  const res = await fetch(backendUrl, {
    method,
    headers: {
      ...(data ? { "Content-Type": "application/json" } : {}),
      ...(authToken ? { "Authorization": `Bearer ${authToken}` } : {}),
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    },
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const url = queryKey.join("/") as string;
    const apiBaseUrl = getApiBaseUrl();
    let backendUrl = url.startsWith('/api') ? `${apiBaseUrl}${url}` : url;

    // Add cache busting in production
    if (!import.meta.env.DEV) {
      const separator = backendUrl.includes('?') ? '&' : '?';
      backendUrl += `${separator}_t=${Date.now()}`;
    }

    // Get auth token from Supabase
    const { data: { session } } = await supabase.auth.getSession();
    const authToken = session?.access_token;

    const res = await fetch(backendUrl, {
      credentials: "include",
      headers: {
        ...(authToken ? { "Authorization": `Bearer ${authToken}` } : {}),
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: true, // Pencere odaklandığında yenile
      staleTime: 30 * 1000, // 30 saniye fresh tut
      cacheTime: 5 * 60 * 1000, // 5 dakika cache'te tut
      retry: 1, // 1 kez tekrar dene
      retryDelay: 1000, // 1 saniye bekle
    },
    mutations: {
      retry: false,
    },
  },
});
