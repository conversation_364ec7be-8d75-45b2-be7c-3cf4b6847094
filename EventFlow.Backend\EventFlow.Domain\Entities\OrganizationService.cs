using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EventFlow.Domain.Entities;

public class OrganizationService : BaseEntity
{
    [Required]
    public Guid OrganizationId { get; set; }
    
    [Required]
    public Guid ServiceId { get; set; }
    
    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; } = 1;
    
    [Required]
    [Range(0, double.MaxValue)]
    [Column(TypeName = "decimal(10,2)")]
    public decimal UnitPrice { get; set; }
    
    [NotMapped]
    public decimal TotalPrice => Quantity * UnitPrice;
    
    // Navigation properties
    [ForeignKey("OrganizationId")]
    public virtual Organization Organization { get; set; } = null!;
    
    [ForeignKey("ServiceId")]
    public virtual Service Service { get; set; } = null!;
}
