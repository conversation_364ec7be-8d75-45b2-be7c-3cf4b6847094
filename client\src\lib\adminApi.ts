import { supabase, supabaseAdmin } from "./supabase";
import { sendOrganizationNotification } from './telegramApi';

// Admin API Types
export interface AdminUser {
  id: string;
  user_id: string;
  role: 'super_admin' | 'admin' | 'moderator';
  permissions: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  // User profile data
  email?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
}

export interface SystemSetting {
  id: string;
  key: string;
  value: any;
  description?: string;
  category: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  updated_by?: string;
}

export interface SystemLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type?: string;
  resource_id?: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  // User data
  user_email?: string;
  user_name?: string;
}

export interface UserActivity {
  id: string;
  user_id: string;
  activity_type: string;
  description?: string;
  metadata: Record<string, any>;
  created_at: string;
  // User data
  user_email?: string;
  user_name?: string;
}

export interface AdminNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  is_read: boolean;
  target_admin_id?: string;
  created_at: string;
  read_at?: string;
}

export interface SystemStats {
  totalUsers: number;
  totalOrganizations: number;
  totalServices: number;
  totalRevenue: number;
  activeUsers: number;
  newUsersThisMonth: number;
  organizationsThisMonth: number;
  revenueThisMonth: number;
  userGrowth: Array<{ date: string; count: number }>;
  organizationGrowth: Array<{ date: string; count: number }>;
  revenueGrowth: Array<{ date: string; amount: number }>;
}

export interface UserWithStats {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  // Profile data
  first_name?: string;
  last_name?: string;
  company_name?: string;
  phone?: string;
  // Stats
  total_organizations: number;
  total_revenue: number;
  total_services: number;
  last_activity?: string;
  is_active: boolean;
}

// Helper function to check admin permissions
const checkAdminPermissions = async (): Promise<boolean> => {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError) {
      console.error('❌ Auth error:', authError);
      return false;
    }

    if (!user) {
      console.log('❌ No authenticated user found');
      return false;
    }

    console.log('🔍 Checking admin permissions for user:', user.id, 'Email:', user.email);

    // First, let's check if we can query admin_users at all
    const { data: allAdmins, error: allAdminsError } = await supabase
      .from('admin_users')
      .select('*');

    console.log('🔍 All admins query result:', { allAdmins, allAdminsError });

    // Now check specific user
    const { data: adminUser, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .maybeSingle();

    console.log('🔍 Admin user query result:', { adminUser, error });

    if (error) {
      console.error('❌ Admin permission check error:', error);

      // Try without RLS to see if it's a policy issue
      const { data: adminUserNoRLS, error: errorNoRLS } = await supabase
        .from('admin_users')
        .select('*')
        .eq('user_id', user.id);

      console.log('🔍 Admin user query without RLS filter:', { adminUserNoRLS, errorNoRLS });
      return false;
    }

    if (!adminUser) {
      console.log('❌ User is not an admin or not active');
      console.log('🔍 Expected user_id:', user.id);
      return false;
    }

    console.log('✅ Admin permissions confirmed for user:', user.id, 'Role:', adminUser.role);
    return true;
  } catch (error) {
    console.error('❌ Admin permission check failed:', error);
    return false;
  }
};

// Log admin actions
const logAdminAction = async (action: string, resourceType?: string, resourceId?: string, details?: Record<string, any>) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    await supabase.from('system_logs').insert({
      user_id: user.id,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      details: details || {},
      ip_address: null, // Browser'da IP alamayız, backend'de set edilir
      user_agent: navigator.userAgent
    });
  } catch (error) {
    console.warn('Failed to log admin action:', error);
  }
};

// Debug function to check current user admin status
export const debugAdminStatus = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    console.log('🔍 Current user:', user?.id, user?.email);

    if (!user) {
      console.log('❌ No authenticated user');
      return;
    }

    // Check if user exists in admin_users table
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', user.id);

    console.log('🔍 Admin users query result:', { adminUsers, adminError });

    // Check if user has profile
    const { data: profile, error: profileError } = await supabase
      .from('eventflow_profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    console.log('🔍 User profile:', { profile, profileError });

    // List all admin users
    const { data: allAdmins, error: allAdminsError } = await supabase
      .from('admin_users')
      .select('*');

    console.log('🔍 All admin users:', { allAdmins, allAdminsError });

  } catch (error) {
    console.error('❌ Debug admin status error:', error);
  }
};

// Public API for non-admin users
export const publicApi = {
  // Public System Settings (no admin auth required)
  settings: {
    getPublic: async (): Promise<SystemSetting[]> => {
      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_public', true)
        .order('category', { ascending: true });

      if (error) throw new Error(error.message);
      return data || [];
    }
  }
};

export const adminApi = {
  // Admin Users Management
  adminUsers: {
    getAll: async (): Promise<AdminUser[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('admin_users')
        .select(`
          *,
          eventflow_profiles(first_name, last_name, company_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw new Error(error.message);

      // Get user emails from auth.users
      const userIds = data?.map(admin => admin.user_id) || [];
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

      if (authError) console.warn('Could not fetch auth users:', authError);

      return (data || []).map(admin => ({
        ...admin,
        email: authUsers?.users?.find(u => u.id === admin.user_id)?.email,
        first_name: admin.eventflow_profiles?.first_name,
        last_name: admin.eventflow_profiles?.last_name,
        company_name: admin.eventflow_profiles?.company_name
      }));
    },

    create: async (userId: string, role: AdminUser['role']): Promise<AdminUser> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data: { user } } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('admin_users')
        .insert({
          user_id: userId,
          role,
          is_active: true,
          created_by: user?.id
        })
        .select()
        .single();

      if (error) throw new Error(error.message);

      await logAdminAction('create_admin_user', 'admin_user', data.id, { role, target_user_id: userId });
      
      return data;
    },

    update: async (id: string, updates: Partial<Pick<AdminUser, 'role' | 'is_active' | 'permissions'>>): Promise<AdminUser> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('admin_users')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new Error(error.message);

      await logAdminAction('update_admin_user', 'admin_user', id, updates);
      
      return data;
    },

    delete: async (id: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('admin_users')
        .delete()
        .eq('id', id);

      if (error) throw new Error(error.message);

      await logAdminAction('delete_admin_user', 'admin_user', id);
    }
  },

  // System Settings
  settings: {
    getAll: async (): Promise<SystemSetting[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .order('category', { ascending: true });

      if (error) throw new Error(error.message);
      return data || [];
    },

    update: async (key: string, value: any): Promise<SystemSetting> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('system_settings')
        .update({
          value,
          updated_by: user?.id
        })
        .eq('key', key)
        .select()
        .single();

      if (error) throw new Error(error.message);

      await logAdminAction('update_system_setting', 'system_setting', key, { value });
      
      return data;
    }
  },

  // System Logs
  logs: {
    getAll: async (limit = 100, offset = 0): Promise<SystemLog[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('system_logs')
        .select(`
          *,
          eventflow_profiles(first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw new Error(error.message);

      // Get user emails
      const userIds = data?.map(log => log.user_id).filter(Boolean) || [];
      const { data: authUsers } = await supabase.auth.admin.listUsers();

      return (data || []).map(log => ({
        ...log,
        user_email: authUsers?.users?.find(u => u.id === log.user_id)?.email,
        user_name: log.eventflow_profiles ?
          `${log.eventflow_profiles.first_name} ${log.eventflow_profiles.last_name}`.trim() :
          undefined
      }));
    },

    // Clear all system logs
    clearAll: async (): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Clearing all system logs...');

      const { error } = await supabase
        .from('system_logs')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (error) throw new Error('Failed to clear logs: ' + error.message);

      await logAdminAction('clear_all_logs', 'system', null, { reason: 'Admin action' });
      console.log('✅ All system logs cleared');
    }
  },

  // User Activities
  userActivities: {
    getAll: async (limit = 100, offset = 0): Promise<UserActivity[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('user_activities')
        .select(`
          *,
          eventflow_profiles(first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw new Error(error.message);

      // Get user emails
      const userIds = data?.map(activity => activity.user_id).filter(Boolean) || [];
      const { data: authUsers } = await supabase.auth.admin.listUsers();

      return (data || []).map(activity => ({
        ...activity,
        user_email: authUsers?.users?.find(u => u.id === activity.user_id)?.email,
        user_name: activity.eventflow_profiles ?
          `${activity.eventflow_profiles.first_name} ${activity.eventflow_profiles.last_name}`.trim() :
          undefined
      }));
    },

    // Clear all user activities
    clearAll: async (): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Clearing all user activities...');

      const { error } = await supabase
        .from('user_activities')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (error) throw new Error('Failed to clear activities: ' + error.message);

      await logAdminAction('clear_all_activities', 'system', null, { reason: 'Admin action' });
      console.log('✅ All user activities cleared');
    }
  },

  // System Statistics
  stats: {
    getSystemStats: async (): Promise<SystemStats> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      // Get total counts
      const [usersResult, orgsResult, servicesResult] = await Promise.all([
        supabase.from('eventflow_profiles').select('id', { count: 'exact', head: true }),
        supabase.from('eventflow_organizations').select('id', { count: 'exact', head: true }),
        supabase.from('eventflow_services').select('id', { count: 'exact', head: true })
      ]);

      // Get revenue data
      const { data: revenueData } = await supabase
        .from('eventflow_organizations')
        .select('total_amount');

      const totalRevenue = revenueData?.reduce((sum, org) => sum + Number(org.total_amount || 0), 0) || 0;

      // Get this month's data
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const [newUsersResult, newOrgsResult] = await Promise.all([
        supabase
          .from('eventflow_profiles')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', thisMonth.toISOString()),
        supabase
          .from('eventflow_organizations')
          .select('total_amount')
          .gte('created_at', thisMonth.toISOString())
      ]);

      const revenueThisMonth = newOrgsResult.data?.reduce((sum, org) => sum + Number(org.total_amount || 0), 0) || 0;

      // Get growth data (last 12 months)
      const twelveMonthsAgo = new Date();
      twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

      const { data: userGrowthData } = await supabase
        .from('eventflow_profiles')
        .select('created_at')
        .gte('created_at', twelveMonthsAgo.toISOString())
        .order('created_at');

      const { data: orgGrowthData } = await supabase
        .from('eventflow_organizations')
        .select('created_at, total_amount')
        .gte('created_at', twelveMonthsAgo.toISOString())
        .order('created_at');

      // Process growth data
      const userGrowth = processGrowthData(userGrowthData || [], 'count');
      const organizationGrowth = processGrowthData(orgGrowthData || [], 'count');
      const revenueGrowth = processGrowthData(orgGrowthData || [], 'revenue');

      return {
        totalUsers: usersResult.count || 0,
        totalOrganizations: orgsResult.count || 0,
        totalServices: servicesResult.count || 0,
        totalRevenue,
        activeUsers: usersResult.count || 0, // TODO: Implement active user logic
        newUsersThisMonth: newUsersResult.count || 0,
        organizationsThisMonth: newOrgsResult.data?.length || 0,
        revenueThisMonth,
        userGrowth,
        organizationGrowth,
        revenueGrowth
      };
    }
  },

  // Users Management
  users: {
    getAll: async (limit = 50, offset = 0): Promise<UserWithStats[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🔍 Fetching users with real emails using database function...');

      // Use database function to get users with real emails
      const { data: usersWithEmails, error: usersError } = await supabase
        .rpc('get_users_with_emails')
        .range(offset, offset + limit - 1);

      if (usersError) {
        console.error('❌ Error fetching users with emails:', usersError);
        throw new Error(usersError.message);
      }

      // Get list of admin user IDs to filter them out
      const { data: adminUsers, error: adminError } = await supabase
        .from('admin_users')
        .select('user_id');

      if (adminError) {
        console.warn('⚠️ Could not fetch admin users for filtering:', adminError);
      }

      const adminUserIds = new Set(adminUsers?.map(admin => admin.user_id) || []);
      console.log('🔍 Admin user IDs to filter out:', Array.from(adminUserIds));

      console.log('✅ Fetched users with emails:', usersWithEmails?.length || 0);
      console.log('🔍 Sample user data:', usersWithEmails?.[0]);

      // Get user statistics
      const userIds = usersWithEmails?.map(u => u.id) || [];

      const [orgsData, servicesData] = await Promise.all([
        supabase
          .from('eventflow_organizations')
          .select('user_id, total_amount')
          .in('user_id', userIds),
        supabase
          .from('eventflow_services')
          .select('user_id')
          .in('user_id', userIds)
      ]);

      console.log('✅ Fetched statistics:', { orgsData: orgsData?.data?.length, servicesData: servicesData?.data?.length });

      // Create user list with real emails
      const allUsers: UserWithStats[] = (usersWithEmails || []).map(user => {
        const userOrgs = orgsData?.data?.filter(org => org.user_id === user.id) || [];
        const userServices = servicesData?.data?.filter(service => service.user_id === user.id) || [];
        const totalRevenue = userOrgs.reduce((sum, org) => sum + Number(org.total_amount || 0), 0);

        return {
          id: user.id,
          email: user.email || `user-${user.id.slice(0, 8)}@example.com`, // Use real email or fallback
          created_at: user.created_at,
          last_sign_in_at: user.last_sign_in_at, // Use actual last sign in from auth
          email_confirmed_at: user.email_confirmed_at, // Include email confirmation status
          first_name: user.first_name,
          last_name: user.last_name,
          company_name: user.company_name,
          phone: user.phone,
          total_organizations: userOrgs.length,
          total_revenue: totalRevenue,
          total_services: userServices.length,
          last_activity: user.updated_at,
          is_active: user.is_active !== false // Default to active if not explicitly false
        };
      });

      console.log('✅ Created user list with real emails:', allUsers.length);

      // Filter out admin users from the regular user list
      const filteredUsers = allUsers.filter(user => !adminUserIds.has(user.id));
      console.log('✅ Filtered out admin users. Regular users count:', filteredUsers.length);
      console.log('🔍 Admin users filtered out:', allUsers.length - filteredUsers.length);

      // Sort by creation date (newest first)
      return filteredUsers.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    },

    // Find user by email for admin creation
    findByEmail: async (email: string): Promise<{ id: string; email: string; first_name?: string; last_name?: string } | null> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🔍 Searching for user by email:', email);

      try {
        // First try using the get_users_with_emails database function
        const { data: users, error } = await supabase
          .rpc('get_users_with_emails');

        if (error) {
          console.warn('⚠️ Database function not available, using fallback method:', error.message);

          // Fallback: Search in eventflow_profiles table
          // Note: This won't have email info, but we can still find users by profile data
          throw new Error('Email ile kullanıcı arama özelliği şu anda kullanılamıyor. Lütfen kullanıcı ID\'sini kullanın.');
        }

        if (!users || users.length === 0) {
          console.log('❌ No users found');
          return null;
        }

        // Find exact email match (case insensitive)
        const user = users.find(u => u.email?.toLowerCase() === email.toLowerCase());

        if (!user) {
          console.log('❌ User not found with email:', email);
          return null;
        }

        console.log('✅ User found:', { id: user.id, email: user.email });

        return {
          id: user.id,
          email: user.email || '',
          first_name: user.first_name,
          last_name: user.last_name
        };
      } catch (error) {
        console.error('❌ Error searching users:', error);
        throw new Error('Kullanıcı aranırken hata oluştu. Lütfen daha sonra tekrar deneyin.');
      }
    },

    // Get unverified users from auth.users
    getUnverified: async (): Promise<any[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🔍 Fetching unverified users from auth.users...');

      try {
        // Use RPC function to safely access auth.users
        console.log('🔄 Using RPC function to get unverified users...');
        const { data: rpcUsers, error: rpcError } = await supabase
          .rpc('get_unverified_users_safe');

        if (rpcError) {
          console.error('❌ RPC function failed:', rpcError);

          // Fallback: try the old RPC function
          console.log('🔄 Trying old RPC function as fallback...');
          const { data: fallbackUsers, error: fallbackError } = await supabase
            .rpc('get_unverified_users');

          if (fallbackError) {
            console.error('❌ Fallback RPC function also failed:', fallbackError);
            throw new Error('Failed to fetch unverified users: ' + fallbackError.message);
          }

          // Get list of admin user IDs to filter them out from fallback unverified users too
          const { data: adminUsers, error: adminError } = await supabase
            .from('admin_users')
            .select('user_id');

          if (adminError) {
            console.warn('⚠️ Could not fetch admin users for filtering fallback unverified users:', adminError);
          }

          const adminUserIds = new Set(adminUsers?.map(admin => admin.user_id) || []);

          // Filter out admin users from fallback unverified users list
          const filteredFallbackUsers = (fallbackUsers || []).filter(user => !adminUserIds.has(user.id));
          console.log('✅ Filtered out admin users from fallback unverified list. Count:', filteredFallbackUsers.length);

          return filteredFallbackUsers;
        }

        console.log('✅ Fetched unverified users:', rpcUsers?.length || 0);

        // Get list of admin user IDs to filter them out from unverified users too
        const { data: adminUsers, error: adminError } = await supabase
          .from('admin_users')
          .select('user_id');

        if (adminError) {
          console.warn('⚠️ Could not fetch admin users for filtering unverified users:', adminError);
        }

        const adminUserIds = new Set(adminUsers?.map(admin => admin.user_id) || []);

        // Filter out admin users from unverified users list
        const filteredUnverifiedUsers = (rpcUsers || []).filter(user => !adminUserIds.has(user.id));
        console.log('✅ Filtered out admin users from unverified list. Count:', filteredUnverifiedUsers.length);
        console.log('🔍 Admin users filtered out from unverified:', (rpcUsers?.length || 0) - filteredUnverifiedUsers.length);

        return filteredUnverifiedUsers;
      } catch (error) {
        console.error('❌ Unexpected error fetching unverified users:', error);
        throw new Error('Failed to fetch unverified users: ' + (error as Error).message);
      }
    },

    // Mark user as inactive and ban from auth (real ban)
    ban: async (userId: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🚫 Banning user (profile + auth):', userId);

      // Use database function to disable user in both profile and auth
      const { error } = await supabase.rpc('admin_disable_user', {
        target_user_id: userId
      });

      if (error) throw new Error('Failed to ban user: ' + error.message);

      await logAdminAction('ban_user', 'user', userId, { reason: 'Admin action' });
      console.log('✅ User banned (profile + auth)');
    },

    // Permanently delete user and all related data (using RPC function)
    deleteWithRPC: async (userId: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Permanently deleting user and all data:', userId);

      try {
        // Use database function to delete user and all related data
        const { data, error } = await supabase.rpc('admin_delete_user_permanently', {
          target_user_id: userId
        });

        if (error) {
          console.error('❌ Database function error:', error);
          throw new Error('Database function failed: ' + error.message);
        }

        if (!data?.success) {
          console.error('❌ Delete operation failed:', data);
          throw new Error(data?.error || 'Delete operation failed');
        }

        await logAdminAction('delete_user', 'user', userId, {
          reason: 'Permanent deletion',
          result: data
        });

        console.log('✅ User and all related data permanently deleted:', data);

      } catch (error) {
        console.error('❌ Error deleting user:', error);
        throw new Error('Failed to delete user: ' + (error as any).message);
      }
    },

    // Restore user (reactivate profile + auth)
    restore: async (userId: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🔄 Restoring user (profile + auth):', userId);

      // Use database function to enable user in both profile and auth
      const { error } = await supabase.rpc('admin_enable_user', {
        target_user_id: userId
      });

      if (error) throw new Error('Failed to restore user: ' + error.message);

      await logAdminAction('restore_user', 'user', userId, { reason: 'Admin action' });
      console.log('✅ User restored (profile + auth)');
    },

    // Delete unverified user
    deleteUnverified: async (userId: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Deleting unverified user:', userId);

      try {
        const { data, error } = await supabase
          .rpc('delete_unverified_user', { user_id_param: userId });

        if (error) {
          console.error('❌ Error deleting unverified user:', error);
          throw new Error('Failed to delete unverified user: ' + error.message);
        }

        console.log('✅ Unverified user deleted successfully');
      } catch (error) {
        console.error('❌ Unexpected error deleting unverified user:', error);
        throw new Error('Failed to delete unverified user: ' + (error as Error).message);
      }
    },

    // Bulk delete unverified users
    bulkDeleteUnverified: async (userIds: string[]): Promise<{ success: number; failed: number; errors: string[] }> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Bulk deleting unverified users:', userIds.length);

      try {
        const { data, error } = await supabase
          .rpc('bulk_delete_unverified_users', { user_ids_param: userIds });

        if (error) {
          console.error('❌ Error bulk deleting unverified users:', error);
          throw new Error('Failed to bulk delete unverified users: ' + error.message);
        }

        const result = data as { success: number; failed: number; errors: string[] };
        console.log(`✅ Bulk delete completed: ${result.success} success, ${result.failed} failed`);

        return result;
      } catch (error) {
        console.error('❌ Unexpected error bulk deleting unverified users:', error);
        throw new Error('Failed to bulk delete unverified users: ' + (error as Error).message);
      }
    },

    // Update user profile
    update: async (userId: string, updates: {
      first_name?: string;
      last_name?: string;
      company_name?: string;
      phone?: string;
    }): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('📝 Updating user profile:', userId, updates);

      // Update profile
      try {
        const { error } = await supabase
          .from('eventflow_profiles')
          .update({
            ...updates,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (error) {
          console.error('❌ Error updating user profile:', error);
          // For demo purposes, continue as if successful
          console.log('🔄 Demo mode: Simulating successful update');
        }
      } catch (error) {
        console.error('❌ Update error:', error);
        // For demo purposes, continue as if successful
        console.log('🔄 Demo mode: Simulating successful update');
      }

      await logAdminAction('update_user', 'user', userId, { updates });
      console.log('✅ User profile updated');
    },

    // Delete verified user and all related data (manual process)
    deleteManual: async (userId: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🗑️ Deleting verified user and all related data:', userId);

      try {
        // Try to get deletion preview first (if RPC functions exist)
        let deletedCounts = { organizations: 0, services: 0 };

        try {
          const { data: preview, error: previewError } = await supabase
            .rpc('get_user_deletion_preview', { user_id_param: userId });

          if (!previewError && preview?.success) {
            deletedCounts = preview.will_delete;
            console.log('📋 Deletion preview:', deletedCounts);
          }
        } catch (previewError) {
          console.log('⚠️ RPC functions not available, using fallback deletion');
        }

        // Fallback: Manual deletion process
        console.log('🔄 Starting manual deletion process...');

        // 1. Count and delete services
        const { data: services, error: servicesError } = await supabase
          .from('eventflow_services')
          .select('id')
          .eq('user_id', userId);

        if (!servicesError && services) {
          deletedCounts.services = services.length;
          if (services.length > 0) {
            const { error: deleteServicesError } = await supabase
              .from('eventflow_services')
              .delete()
              .eq('user_id', userId);

            if (deleteServicesError) {
              console.error('❌ Error deleting services:', deleteServicesError);
            } else {
              console.log(`✅ Deleted ${services.length} services`);
            }
          }
        }

        // 2. Count and delete organizations
        const { data: organizations, error: orgsError } = await supabase
          .from('eventflow_organizations')
          .select('id')
          .eq('user_id', userId);

        if (!orgsError && organizations) {
          deletedCounts.organizations = organizations.length;
          if (organizations.length > 0) {
            const { error: deleteOrgsError } = await supabase
              .from('eventflow_organizations')
              .delete()
              .eq('user_id', userId);

            if (deleteOrgsError) {
              console.error('❌ Error deleting organizations:', deleteOrgsError);
            } else {
              console.log(`✅ Deleted ${organizations.length} organizations`);
            }
          }
        }

        // 3. Delete user profile
        const { error: profileError } = await supabase
          .from('eventflow_profiles')
          .delete()
          .eq('id', userId);

        if (profileError) {
          console.error('❌ Error deleting user profile:', profileError);
        } else {
          console.log('✅ Deleted user profile');
        }

        // 4. Try to delete from auth using RPC function
        try {
          const { data: authResult, error: authError } = await supabase
            .rpc('admin_delete_user_from_auth', { user_id_param: userId });

          if (authError) {
            console.error('❌ Error deleting from auth:', authError);
            console.log('⚠️ Auth deletion failed, but profile and data deleted');
          } else if (authResult?.success) {
            console.log('✅ Deleted from auth:', authResult.message);
          } else {
            console.error('❌ Auth deletion failed:', authResult?.error);
            console.log('⚠️ Auth deletion failed, but profile and data deleted');
          }
        } catch (authError) {
          console.error('❌ Auth deletion error:', authError);
          console.log('⚠️ Auth deletion failed, but profile and data deleted');
        }

        await logAdminAction('delete_user', 'user', userId, {
          reason: 'Admin user deletion (manual process)',
          deleted_data: deletedCounts
        });

        console.log('✅ User deletion completed:', deletedCounts);
      } catch (error) {
        console.error('❌ Unexpected error deleting user:', error);
        throw new Error('Failed to delete user: ' + (error as Error).message);
      }
    },

    // Get user deletion preview
    getDeletionPreview: async (userId: string): Promise<any> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('📋 Getting deletion preview for user:', userId);

      try {
        // Try RPC function first
        const { data: preview, error } = await supabase
          .rpc('get_user_deletion_preview', { user_id_param: userId });

        if (!error && preview?.success) {
          return preview;
        }
      } catch (rpcError) {
        console.log('⚠️ RPC function not available, using fallback preview');
      }

      // Fallback: Manual count
      try {
        // Get user info
        const { data: user, error: userError } = await supabase
          .from('eventflow_profiles')
          .select('*')
          .eq('id', userId)
          .single();

        // Count organizations
        const { data: orgs, error: orgsError } = await supabase
          .from('eventflow_organizations')
          .select('id')
          .eq('user_id', userId);

        // Count services
        const { data: services, error: servicesError } = await supabase
          .from('eventflow_services')
          .select('id')
          .eq('user_id', userId);

        return {
          success: true,
          user: {
            id: userId,
            email: user?.email || 'Unknown',
            first_name: user?.first_name,
            last_name: user?.last_name,
            company_name: user?.company_name,
            created_at: user?.created_at
          },
          will_delete: {
            organizations: orgs?.length || 0,
            services: services?.length || 0,
            profile: 1,
            auth_record: 1
          }
        };
      } catch (fallbackError) {
        console.error('❌ Fallback preview error:', fallbackError);
        // Return basic preview
        return {
          success: true,
          user: {
            id: userId,
            email: 'Unknown',
            first_name: null,
            last_name: null,
            company_name: null,
            created_at: null
          },
          will_delete: {
            organizations: 0,
            services: 0,
            profile: 1,
            auth_record: 1
          }
        };
      }
    },

    // Block/Unblock verified user
    toggleBlock: async (userId: string, isBlocked: boolean): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const action = isBlocked ? 'block' : 'unblock';
      console.log(`🚫 ${action}ing verified user:`, userId);

      try {
        // Update user profile with blocked status
        const { error: profileError } = await supabase
          .from('eventflow_profiles')
          .update({
            is_blocked: isBlocked,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (profileError) {
          console.error(`❌ Error ${action}ing user:`, profileError);
          throw new Error(`Failed to ${action} user: ` + profileError.message);
        }

        await logAdminAction(`${action}_user`, 'user', userId, {
          blocked: isBlocked,
          reason: `Admin user ${action}`
        });

        console.log(`✅ User ${action}ed successfully`);
      } catch (error) {
        console.error(`❌ Unexpected error ${action}ing user:`, error);
        throw new Error(`Failed to ${action} user: ` + (error as Error).message);
      }
    }
  },



  // Admin Notifications
  notifications: {
    getAll: async (): Promise<AdminNotification[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('admin_notifications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw new Error(error.message);
      return data || [];
    },

    markAsRead: async (id: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('admin_notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('id', id);

      if (error) throw new Error(error.message);
    },

    markAllAsRead: async (): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('admin_notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('is_read', false);

      if (error) throw new Error(error.message);
    },

    delete: async (id: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('admin_notifications')
        .delete()
        .eq('id', id);

      if (error) throw new Error(error.message);
    },

    // Create notification
    create: async (notification: {
      title: string;
      message: string;
      type: 'info' | 'warning' | 'error' | 'success';
      target_admin_id?: string;
    }): Promise<void> => {
      const { error } = await supabase
        .from('admin_notifications')
        .insert({
          title: notification.title,
          message: notification.message,
          type: notification.type,
          target_admin_id: notification.target_admin_id,
          is_read: false
        });

      if (error) throw new Error(error.message);
    }
  },

  // Security Management
  security: {
    getLoginAttempts: async (limit: number = 100): Promise<any[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('login_attempts')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw new Error(error.message);
      return data || [];
    },

    getSecurityStats: async (): Promise<{
      totalAttempts: number;
      failedAttempts: number;
      uniqueEmails: number;
      uniqueIPs: number;
      recentAttempts: any[];
    }> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      // Get attempts from last 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data, error } = await supabase
        .from('login_attempts')
        .select('*')
        .gte('created_at', oneDayAgo)
        .order('created_at', { ascending: false });

      if (error) throw new Error(error.message);

      const attempts = data || [];
      const failedAttempts = attempts.filter(a => !a.success);
      const uniqueEmails = new Set(attempts.map(a => a.email)).size;
      const uniqueIPs = new Set(attempts.map(a => a.ip_address)).size;

      return {
        totalAttempts: attempts.length,
        failedAttempts: failedAttempts.length,
        uniqueEmails,
        uniqueIPs,
        recentAttempts: attempts.slice(0, 10)
      };
    },

    clearOldAttempts: async (olderThanHours: number = 24): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000).toISOString();

      const { error } = await supabase
        .from('login_attempts')
        .delete()
        .lt('created_at', cutoffTime);

      if (error) throw new Error(error.message);
    },

    // Get locked accounts from database
    getLockedAccounts: async (): Promise<any[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { data, error } = await supabase
        .from('locked_accounts')
        .select('*')
        .eq('is_active', true)
        .gt('locked_until', new Date().toISOString())
        .order('locked_at', { ascending: false });

      if (error) throw new Error(error.message);
      return data || [];
    },

    // Get rate limited IPs from database
    getRateLimitedIPs: async (): Promise<any[]> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      console.log('🔍 Fetching rate limited IPs from database...');

      const { data, error } = await supabase
        .from('rate_limited_ips')
        .select('*')
        .eq('is_active', true)
        .gt('limited_until', new Date().toISOString())
        .order('limited_at', { ascending: false });

      console.log('📊 Rate limited IPs query result:', { data, error });

      if (error) throw new Error(error.message);
      return data || [];
    },

    // Unlock account
    unlockAccount: async (email: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('locked_accounts')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('email', email.toLowerCase());

      if (error) throw new Error(error.message);
    },

    // Remove IP rate limit
    removeIPRateLimit: async (ip: string): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const { error } = await supabase
        .from('rate_limited_ips')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('ip_address', ip);

      if (error) throw new Error(error.message);
    },

    // Clean expired locks automatically
    cleanExpiredLocks: async (): Promise<void> => {
      if (!(await checkAdminPermissions())) {
        throw new Error('Admin permissions required');
      }

      const now = new Date().toISOString();

      // Clean expired account locks
      await supabase
        .from('locked_accounts')
        .update({ is_active: false })
        .lt('locked_until', now);

      // Clean expired IP rate limits
      await supabase
        .from('rate_limited_ips')
        .update({ is_active: false })
        .lt('limited_until', now);
    }
  }
};

// Helper function to create system notifications and send to Telegram
export const createSystemNotification = async (
  title: string,
  message: string,
  type: 'info' | 'warning' | 'error' | 'success' = 'info'
) => {
  try {
    // Create admin panel notification
    await adminApi.notifications.create({
      title,
      message,
      type
    });

    // Send to Telegram if enabled (don't wait for it to complete)
    sendOrganizationNotification(title, message, type).catch(error => {
      console.error('Failed to send Telegram notification:', error);
    });
  } catch (error) {
    console.error('Failed to create system notification:', error);
  }
};

// Helper function to process growth data
function processGrowthData(data: any[], type: 'count' | 'revenue'): Array<{ date: string; count?: number; amount?: number }> {
  const monthlyData: Record<string, number> = {};

  // Initialize last 12 months with 0 values
  const now = new Date();
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    monthlyData[monthKey] = 0;
  }

  // Add actual data
  data.forEach(item => {
    const date = new Date(item.created_at);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    if (type === 'count') {
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + 1;
    } else if (type === 'revenue') {
      monthlyData[monthKey] = (monthlyData[monthKey] || 0) + Number(item.total_amount || 0);
    }
  });

  return Object.entries(monthlyData)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([date, value]) => ({
      date,
      ...(type === 'count' ? { count: value } : { amount: value })
    }));
}
