import { supabase } from "./supabase";
import type { Database } from "./supabase";
import { ActivityLogger } from './activityLogger';

// Supabase table types
type ServiceRow = Database['public']['Tables']['eventflow_services']['Row'];
type ServiceInsert = Database['public']['Tables']['eventflow_services']['Insert'];
type CustomerRow = Database['public']['Tables']['eventflow_customers']['Row'];
type CustomerInsert = Database['public']['Tables']['eventflow_customers']['Insert'];
type OrganizationRow = Database['public']['Tables']['eventflow_organizations']['Row'];
type OrganizationInsert = Database['public']['Tables']['eventflow_organizations']['Insert'];

// Frontend types (keeping compatibility)
export interface Service {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  createdAt: Date;
}

export interface Organization {
  id: string;
  customerId: string;
  eventDate: string;
  startTime: string;
  endTime: string;
  totalPrice: string;
  discountPercentage: string;
  finalPrice: string;
  status: string;
  createdAt: Date;
}

export interface OrganizationServiceDto {
  serviceId: string;
  serviceName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface OrganizationDetailsDto {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  eventDate: string;
  status: string;
  discountPercentage: number;
  totalAmount: number;
  notes?: string;
  createdAt: Date;
  services?: OrganizationServiceDto[];
}

export interface CreateOrganizationDto {
  customer: {
    fullName: string;
    email?: string;
    phoneNumber: string;
  };
  eventDate: string;
  status: string;
  discountPercentage: number;
  totalAmount: number;
  notes?: string;
  services: Array<{
    serviceId: string;
    quantity: number;
    unitPrice: number;
  }>;
}

// Helper function to convert Supabase service to frontend format
const convertService = (service: ServiceRow): Service => ({
  id: service.id,
  name: service.name,
  description: service.description || '',
  basePrice: Number(service.base_price),
  isActive: service.is_active || true,
  createdAt: service.created_at,
  updatedAt: service.updated_at
});

export const supabaseApi = {
  // Services
  services: {
    getAll: async (): Promise<Service[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      console.log('🔍 Fetching services for user:', user.id);

      const { data, error } = await supabase
        .from('eventflow_services')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Services fetch error:', error);
        throw new Error(error.message);
      }

      console.log('✅ Fetched', data?.length || 0, 'services for user:', user.id);
      return (data || []).map(convertService);
    },

    create: async (service: Omit<Service, "id" | "createdAt" | "updatedAt">): Promise<Service> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      console.log('🔐 Creating service for user:', user.id);

      const { data, error } = await supabase
        .from('eventflow_services')
        .insert({
          name: service.name,
          description: service.description,
          base_price: service.basePrice,
          is_active: service.isActive,
          user_id: user.id
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Service creation error:', error);
        throw new Error(error.message);
      }

      console.log('✅ Service created successfully for user:', user.id);

      // Log user activity
      ActivityLogger.createService(service.name);

      return convertService(data);
    },

    update: async (id: string, service: Partial<Omit<Service, "id" | "createdAt">>): Promise<Service> => {
      const updateData: any = {};
      if (service.name !== undefined) updateData.name = service.name;
      if (service.description !== undefined) updateData.description = service.description;
      if (service.basePrice !== undefined) updateData.base_price = service.basePrice;
      if (service.isActive !== undefined) updateData.is_active = service.isActive;
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('eventflow_services')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw new Error(error.message);

      // Log user activity
      ActivityLogger.updateService(id, updateData);

      return convertService(data);
    },

    delete: async (id: string): Promise<void> => {
      const { error } = await supabase
        .from('eventflow_services')
        .delete()
        .eq('id', id);

      if (error) throw new Error(error.message);

      // Log user activity
      ActivityLogger.deleteService(id);
    }
  },

  // Customers
  customers: {
    getAll: async (): Promise<Customer[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('eventflow_customers')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw new Error(error.message);
      return (data || []).map(customer => ({
        id: customer.id,
        fullName: customer.name,
        phoneNumber: customer.phone || '',
        email: customer.email || '',
        createdAt: new Date(customer.created_at)
      }));
    },

    create: async (customer: Omit<Customer, "id" | "createdAt">): Promise<Customer> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Email alanı boş ise null olarak gönder
      const emailValue = customer.email?.trim() === '' ? null : customer.email;

      try {
        const { data, error } = await supabase
          .from('eventflow_customers')
          .insert({
            name: customer.fullName,
            email: emailValue,
            phone: customer.phoneNumber,
            user_id: user.id
          })
          .select()
          .single();

        if (error) {
          // Hata mesajını daha anlaşılır hale getir
          if (error.message.includes('chk_email_format')) {
            throw new Error('Geçerli bir e-posta adresi giriniz veya boş bırakınız');
          }
          throw new Error(error.message);
        }

        return {
          id: data.id,
          fullName: data.name,
          phoneNumber: data.phone || '',
          email: data.email || '',
          createdAt: new Date(data.created_at)
        };
      } catch (error: any) {
        console.error('Customer creation error:', error);
        throw error;
      }
    }
  },

  // Organizations
  organizations: {
    getAll: async (): Promise<OrganizationDetailsDto[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get organizations with customer data
      const { data: orgsData, error: orgsError } = await supabase
        .from('eventflow_organizations')
        .select(`
          *,
          eventflow_customers!inner(name, email, phone)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (orgsError) throw new Error(orgsError.message);

      // Get organization services with service details
      const { data: servicesData, error: servicesError } = await supabase
        .from('eventflow_organization_services')
        .select(`
          *,
          eventflow_services!inner(name)
        `)
        .eq('user_id', user.id);

      if (servicesError) throw new Error(servicesError.message);

      // Map organizations with their services
      return (orgsData || []).map(org => {
        const orgServices = (servicesData || [])
          .filter(service => service.organization_id === org.id)
          .map(service => ({
            serviceId: service.service_id,
            serviceName: service.eventflow_services?.name || 'Bilinmeyen Hizmet',
            quantity: service.quantity,
            unitPrice: Number(service.unit_price),
            totalPrice: service.quantity * Number(service.unit_price)
          }));

        return {
          id: org.id,
          customerId: org.customer_id,
          customerName: org.eventflow_customers?.name || '',
          customerEmail: org.eventflow_customers?.email || '',
          customerPhone: org.eventflow_customers?.phone || '',
          eventDate: org.event_date,
          status: org.status || 'Teklif',
          discountPercentage: Number(org.discount_percentage || 0),
          totalAmount: Number(org.total_amount),
          notes: org.notes || '',
          createdAt: new Date(org.created_at),
          services: orgServices
        };
      });
    },

    create: async (organization: CreateOrganizationDto): Promise<OrganizationDetailsDto> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      try {
        // Email alanı boş ise null olarak gönder
        const emailValue = organization.customer.email?.trim() === '' ? null : organization.customer.email;

        // First check if customer already exists (only if email is provided)
        let customerData;
        if (emailValue) {
          const { data: existingCustomer } = await supabase
            .from('eventflow_customers')
            .select('*')
            .eq('email', emailValue)
            .eq('user_id', user.id)
            .single();

          if (existingCustomer) {
            console.log('✅ Using existing customer:', existingCustomer.id);
            customerData = existingCustomer;
          }
        }

        if (!customerData) {
          // Create new customer
          const { data: newCustomer, error: customerError } = await supabase
            .from('eventflow_customers')
            .insert({
              name: organization.customer.fullName,
              email: emailValue,
              phone: organization.customer.phoneNumber,
              user_id: user.id
            })
            .select()
            .single();

          if (customerError) {
            if (customerError.code === '23505') {
              // Duplicate key error - try to get existing customer
              const { data: duplicateCustomer } = await supabase
                .from('eventflow_customers')
                .select('*')
                .eq('email', emailValue)
                .eq('user_id', user.id)
                .single();

              if (duplicateCustomer) {
                customerData = duplicateCustomer;
              } else {
                throw new Error('Bu email adresi zaten kullanılıyor');
              }
            } else if (customerError.message.includes('chk_email_format')) {
              throw new Error('Geçerli bir e-posta adresi giriniz veya boş bırakınız');
            } else {
              throw new Error(customerError.message);
            }
          } else {
            customerData = newCustomer;
          }
        }

        // Then create organization
        const { data: orgData, error: orgError } = await supabase
          .from('eventflow_organizations')
          .insert({
            customer_id: customerData.id,
            event_date: organization.eventDate,
            status: organization.status,
            discount_percentage: organization.discountPercentage,
            total_amount: organization.totalAmount,
            notes: organization.notes || '',
            user_id: user.id
          })
          .select()
          .single();

        if (orgError) throw new Error(orgError.message);

        // Create organization services and get service names
        let servicesWithNames: OrganizationServiceDto[] = [];
        if (organization.services && organization.services.length > 0) {
          const { error: servicesError } = await supabase
            .from('eventflow_organization_services')
            .insert(
              organization.services.map(service => ({
                organization_id: orgData.id,
                service_id: service.serviceId,
                quantity: service.quantity,
                unit_price: service.unitPrice,
                user_id: user.id
              }))
            );

          if (servicesError) throw new Error(servicesError.message);

          // Get service names for the response
          const serviceIds = organization.services.map(s => s.serviceId);
          const { data: serviceDetails, error: serviceDetailsError } = await supabase
            .from('eventflow_services')
            .select('id, name')
            .in('id', serviceIds)
            .eq('user_id', user.id);

          if (serviceDetailsError) throw new Error(serviceDetailsError.message);

          servicesWithNames = organization.services.map(service => {
            const serviceDetail = serviceDetails?.find(s => s.id === service.serviceId);
            return {
              serviceId: service.serviceId,
              serviceName: serviceDetail?.name || 'Bilinmeyen Hizmet',
              quantity: service.quantity,
              unitPrice: service.unitPrice,
              totalPrice: service.quantity * service.unitPrice
            };
          });
        }

        const result = {
          id: orgData.id,
          customerId: customerData.id,
          customerName: customerData.name,
          customerEmail: customerData.email || '',
          customerPhone: customerData.phone || '',
          eventDate: orgData.event_date,
          status: orgData.status || 'Teklif',
          discountPercentage: Number(orgData.discount_percentage || 0),
          totalAmount: Number(orgData.total_amount),
          notes: orgData.notes || '',
          createdAt: new Date(orgData.created_at),
          services: servicesWithNames
        };

        // Log user activity
        ActivityLogger.createOrganization(customerData.name);

        return result;
      } catch (error) {
        console.error('Organization creation error:', error);
        throw error;
      }
    },

    update: async (id: string, updateData: any): Promise<OrganizationDetailsDto> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Update organization
      const { data: orgData, error: orgError } = await supabase
        .from('eventflow_organizations')
        .update({
          event_date: updateData.eventDate,
          status: updateData.status,
          discount_percentage: updateData.discountPercentage,
          notes: updateData.notes || '',
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (orgError) throw new Error(orgError.message);

      // Update customer if provided
      if (updateData.customerName || updateData.customerEmail || updateData.customerPhone) {
        // Email alanı boş ise null olarak gönder
        const emailValue = updateData.customerEmail?.trim() === '' ? null : updateData.customerEmail;

        const { error: customerError } = await supabase
          .from('eventflow_customers')
          .update({
            name: updateData.customerName,
            email: emailValue,
            phone: updateData.customerPhone,
            updated_at: new Date().toISOString()
          })
          .eq('id', orgData.customer_id)
          .eq('user_id', user.id);

        if (customerError) {
          if (customerError.message.includes('chk_email_format')) {
            throw new Error('Geçerli bir e-posta adresi giriniz veya boş bırakınız');
          }
          throw new Error(customerError.message);
        }
      }

      // Get updated organization with customer data
      const { data: updatedOrg, error: fetchError } = await supabase
        .from('eventflow_organizations')
        .select(`
          *,
          eventflow_customers!inner(name, email, phone)
        `)
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (fetchError) throw new Error(fetchError.message);

      const result = {
        id: updatedOrg.id,
        customerId: updatedOrg.customer_id,
        customerName: updatedOrg.eventflow_customers.name,
        customerEmail: updatedOrg.eventflow_customers.email || '',
        customerPhone: updatedOrg.eventflow_customers.phone || '',
        eventDate: updatedOrg.event_date,
        status: updatedOrg.status || 'Teklif',
        discountPercentage: Number(updatedOrg.discount_percentage || 0),
        totalAmount: Number(updatedOrg.total_amount),
        notes: updatedOrg.notes || '',
        createdAt: new Date(updatedOrg.created_at),
        updatedAt: new Date(updatedOrg.updated_at),
        services: []
      };

      // Log user activity
      ActivityLogger.updateOrganization(id, updateData);

      return result;
    },

    updateStatus: async (id: string, status: string): Promise<void> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('eventflow_organizations')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw new Error(error.message);
    },

    delete: async (id: string): Promise<void> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // First delete organization services
      const { error: servicesError } = await supabase
        .from('eventflow_organization_services')
        .delete()
        .eq('organization_id', id)
        .eq('user_id', user.id);

      if (servicesError) throw new Error(servicesError.message);

      // Then delete organization
      const { error: orgError } = await supabase
        .from('eventflow_organizations')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (orgError) throw new Error(orgError.message);

      // Log user activity
      ActivityLogger.deleteOrganization(id);
    }
  },

  // Calendar
  calendar: {
    getEvents: async (): Promise<CalendarEventDto[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('eventflow_organizations')
        .select(`
          *,
          eventflow_customers!inner(name)
        `)
        .eq('user_id', user.id)
        .order('event_date', { ascending: true });

      if (error) throw new Error(error.message);

      return (data || []).map(org => ({
        id: org.id,
        title: `${org.eventflow_customers.name} - ${org.status}`,
        start: new Date(org.event_date),
        end: new Date(new Date(org.event_date).getTime() + 3 * 60 * 60 * 1000), // 3 saat sonra
        customerName: org.eventflow_customers.name,
        status: org.status || 'Teklif',
        totalAmount: Number(org.total_amount || 0)
      }));
    }
  }
};
