import { useFormContext } from "react-hook-form";
import { motion } from "framer-motion";
import { CreditC<PERSON>, Percent, FileText, Calculator, TrendingDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { useServices } from "../../../hooks/useServices";
import { cn } from "@/lib/utils";

export function PricingStep() {
  const { control, formState: { errors }, watch, setValue } = useFormContext();
  const { services } = useServices();
  
  const selectedServices = watch("services.selectedServices") || [];
  const discountPercentage = watch("pricing.discountPercentage") || 0;
  const notes = watch("pricing.notes") || "";

  const calculatePricing = () => {
    if (!services) return { subtotal: 0, discount: 0, total: 0 };
    
    const subtotal = selectedServices.reduce((total: number, serviceId: string) => {
      const service = services.find(s => s.id === serviceId);
      return total + (service?.basePrice || 0);
    }, 0);
    
    const discount = (subtotal * discountPercentage) / 100;
    const total = subtotal - discount;
    
    return { subtotal, discount, total };
  };

  const { subtotal, discount, total } = calculatePricing();

  const handleDiscountChange = (value: number[]) => {
    setValue("pricing.discountPercentage", value[0]);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <CreditCard className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900">Fiyatlandırma</h3>
        <p className="text-sm sm:text-base text-gray-600 px-2">İndirim oranını belirleyin ve notlarınızı ekleyin</p>
      </div>

      {/* Selected Services Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4">
        <h4 className="text-xs sm:text-sm font-medium text-gray-900 mb-2 sm:mb-3">Seçilen Hizmetler</h4>
        <div className="space-y-2">
          {selectedServices.map((serviceId: string) => {
            const service = services?.find(s => s.id === serviceId);
            if (!service) return null;
            
            return (
              <div key={serviceId} className="flex items-center justify-between text-sm">
                <span className="text-gray-700">{service.name}</span>
                <span className="font-medium text-gray-900">
                  ₺{service.basePrice.toLocaleString('tr-TR')}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Discount Section */}
      <div className="space-y-4">
        <FormField
          control={control}
          name="pricing.discountPercentage"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center space-x-2">
                <Percent className="w-4 h-4 text-gray-500" />
                <span>İndirim Oranı</span>
                <Badge variant="secondary">%{discountPercentage}</Badge>
              </FormLabel>
              <FormControl>
                <div className="space-y-4">
                  <Slider
                    value={[discountPercentage]}
                    onValueChange={handleDiscountChange}
                    max={100}
                    min={0}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>%0</span>
                    <span>%25</span>
                    <span>%50</span>
                    <span>%75</span>
                    <span>%100</span>
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Quick Discount Buttons */}
        <div className="flex flex-wrap gap-2">
          {[0, 5, 10, 15, 20, 25].map((percentage) => (
            <button
              key={percentage}
              type="button"
              onClick={() => setValue("pricing.discountPercentage", percentage)}
              className={cn(
                "px-3 py-1 text-xs rounded-full border transition-all duration-200",
                discountPercentage === percentage
                  ? "bg-orange-500 text-white border-orange-500"
                  : "bg-white text-gray-700 border-gray-300 hover:border-orange-300 hover:bg-orange-50"
              )}
            >
              %{percentage}
            </button>
          ))}
        </div>
      </div>

      {/* Pricing Breakdown */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-orange-50 border border-orange-200 rounded-lg p-4"
      >
        <div className="flex items-center space-x-2 mb-3">
          <Calculator className="w-5 h-5 text-orange-600" />
          <h4 className="text-sm font-medium text-orange-900">Fiyat Hesabı</h4>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-orange-800">Ara Toplam</span>
            <span className="font-medium text-orange-900">
              ₺{subtotal.toLocaleString('tr-TR')}
            </span>
          </div>
          
          {discountPercentage > 0 && (
            <div className="flex items-center justify-between text-green-700">
              <div className="flex items-center space-x-1">
                <TrendingDown className="w-4 h-4" />
                <span>İndirim (%{discountPercentage})</span>
              </div>
              <span className="font-medium">
                -₺{discount.toLocaleString('tr-TR')}
              </span>
            </div>
          )}
          
          <div className="border-t border-orange-200 pt-2">
            <div className="flex items-center justify-between font-semibold text-orange-900">
              <span>Toplam Tutar</span>
              <span className="text-lg">
                ₺{total.toLocaleString('tr-TR')}
              </span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Notes Section */}
      <FormField
        control={control}
        name="pricing.notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center space-x-2">
              <FileText className="w-4 h-4 text-gray-500" />
              <span>Notlar</span>
              <span className="text-gray-400 text-sm">(opsiyonel)</span>
            </FormLabel>
            <FormControl>
              <Textarea
                {...field}
                placeholder="Organizasyon hakkında özel notlarınızı buraya yazabilirsiniz..."
                className={cn(
                  "min-h-[100px] resize-none transition-all duration-200",
                  "focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                )}
              />
            </FormControl>
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>Müşteri ile paylaşılacak özel talepler veya notlar</span>
              <span>{notes.length}/500</span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Final Summary */}
      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">✓</span>
          </div>
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-orange-900">Özet</h4>
            <ul className="text-sm text-orange-800 space-y-1">
              <li>• {selectedServices.length} hizmet seçildi</li>
              <li>• %{discountPercentage} indirim uygulandı</li>
              <li>• Toplam tutar: ₺{total.toLocaleString('tr-TR')}</li>
              {notes && <li>• Özel notlar eklendi</li>}
            </ul>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
          <span>Adım 4/4 - Fiyatlandırma</span>
        </div>
      </div>
    </div>
  );
}
