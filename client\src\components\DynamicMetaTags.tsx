import { Helmet } from 'react-helmet-async';
import { usePublicSettings } from '@/hooks/usePublicSettings';

export function DynamicMetaTags() {
  const { settings, isLoading } = usePublicSettings();

  // Don't render anything while loading to avoid flashing default values
  if (isLoading) {
    return null;
  }

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{settings.app_name}</title>
      <meta name="description" content={settings.site_description} />
      <meta name="keywords" content={settings.site_keywords} />

      {/* Open Graph / Facebook */}
      <meta property="og:title" content={settings.app_name} />
      <meta property="og:description" content={settings.site_description} />
      <meta property="og:type" content="website" />

      {/* Twitter */}
      <meta property="twitter:title" content={settings.app_name} />
      <meta property="twitter:description" content={settings.site_description} />
      <meta property="twitter:card" content="summary_large_image" />
    </Helmet>
  );
}
