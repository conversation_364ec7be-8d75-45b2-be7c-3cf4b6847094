import { useState, useEffect, useCallback, useRef } from 'react';

interface UseScrollHeaderOptions {
  threshold?: number;
  hideOnScrollDown?: boolean;
}

export function useScrollHeader(options: UseScrollHeaderOptions = {}) {
  const { threshold = 10, hideOnScrollDown = true } = options;
  
  const [isVisible, setIsVisible] = useState(true);
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const lastScrollY = useRef(0);
  const ticking = useRef(false);

  const updateScrollDirection = useCallback(() => {
    const scrollY = window.scrollY;
    
    if (Math.abs(scrollY - lastScrollY.current) < threshold) {
      ticking.current = false;
      return;
    }

    const direction = scrollY > lastScrollY.current ? 'down' : 'up';
    setScrollDirection(direction);
    
    if (hideOnScrollDown) {
      // Show header when scrolling up or at the top
      if (direction === 'up' || scrollY < threshold) {
        setIsVisible(true);
      } 
      // Hide header when scrolling down and not at the top
      else if (direction === 'down' && scrollY > threshold) {
        setIsVisible(false);
      }
    }

    lastScrollY.current = scrollY;
    ticking.current = false;
  }, [threshold, hideOnScrollDown]);

  const requestTick = useCallback(() => {
    if (!ticking.current) {
      requestAnimationFrame(updateScrollDirection);
      ticking.current = true;
    }
  }, [updateScrollDirection]);

  useEffect(() => {
    const handleScroll = () => {
      requestTick();
    };

    // Optimized scroll event listener for mobile
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const throttledScroll = () => {
      clearTimeout(timeoutId);
      if (rafId) {
        cancelAnimationFrame(rafId);
      }

      rafId = requestAnimationFrame(() => {
        timeoutId = setTimeout(handleScroll, 5); // Reduced delay for better responsiveness
      });
    };

    // Add event listeners with optimized options
    const scrollOptions = { passive: true, capture: false };
    window.addEventListener('scroll', throttledScroll, scrollOptions);
    window.addEventListener('touchmove', throttledScroll, scrollOptions);
    window.addEventListener('touchstart', throttledScroll, scrollOptions);
    window.addEventListener('touchend', throttledScroll, scrollOptions);

    // Handle orientation and viewport changes
    const handleViewportChange = () => {
      // Delay to allow viewport to settle
      setTimeout(() => {
        lastScrollY.current = window.scrollY;
        setIsVisible(true);
        // Force a scroll update after viewport change
        requestTick();
      }, 150);
    };

    window.addEventListener('orientationchange', handleViewportChange);
    window.addEventListener('resize', handleViewportChange);

    // Handle iOS Safari viewport changes
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        handleViewportChange();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      window.removeEventListener('touchmove', throttledScroll);
      window.removeEventListener('touchstart', throttledScroll);
      window.removeEventListener('touchend', throttledScroll);
      window.removeEventListener('orientationchange', handleViewportChange);
      window.removeEventListener('resize', handleViewportChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearTimeout(timeoutId);
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [requestTick]);

  // Reset visibility when component mounts or page loads
  useEffect(() => {
    setIsVisible(true);
    lastScrollY.current = window.scrollY;
  }, []);

  return {
    isVisible,
    scrollDirection,
    scrollY: lastScrollY.current
  };
}
