import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Menu,
  Bell,
  Settings,
  User,
  LogOut,
  Home,
  Shield
} from 'lucide-react';
import { Link } from 'wouter';
import { useAuth } from '../../contexts/AuthContext';
import type { AdminUser } from '../../lib/adminApi';

interface AdminHeaderProps {
  title: string;
  subtitle?: string;
  onMenuClick: () => void;
  adminUser: AdminUser | null;
  role: 'super_admin' | 'admin' | 'moderator' | null;
}

export const AdminHeader: React.FC<AdminHeaderProps> = ({
  title,
  subtitle,
  onMenuClick,
  adminUser,
  role
}) => {
  const { signOut } = useAuth();

  // Fetch notifications to get unread count
  const { data: notifications = [] } = useQuery({
    queryKey: ['admin', 'notifications'],
    queryFn: adminApi.notifications.getAll,
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: false
  });

  const unreadCount = notifications.filter(n => !n.is_read).length;

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getRoleDisplayName = (role: string | null): string => {
    switch (role) {
      case 'super_admin':
        return 'Süper Admin';
      case 'admin':
        return 'Admin';
      case 'moderator':
        return 'Moderatör';
      default:
        return 'Bilinmeyen';
    }
  };

  const getRoleBadgeVariant = (role: string | null) => {
    switch (role) {
      case 'super_admin':
        return 'default';
      case 'admin':
        return 'secondary';
      case 'moderator':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <header className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 shadow-sm">
      <div className="flex items-center justify-between h-16 px-4 sm:px-6">
        {/* Left side - Menu button and title */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={onMenuClick}
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <div>
            <h1 className="text-xl font-semibold text-slate-900 dark:text-white">
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {subtitle}
              </p>
            )}
          </div>
        </div>

        {/* Right side - Actions and user menu */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <Link href="/admin/notifications">
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </Button>
          </Link>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 h-10">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-sm">
                    {adminUser?.first_name?.charAt(0)?.toUpperCase() || 
                     adminUser?.email?.charAt(0)?.toUpperCase() || 'A'}
                  </span>
                </div>
                <div className="hidden sm:block text-left">
                  <p className="text-sm font-medium text-slate-900 dark:text-white">
                    {adminUser?.first_name && adminUser?.last_name 
                      ? `${adminUser.first_name} ${adminUser.last_name}`
                      : adminUser?.email || 'Admin User'
                    }
                  </p>
                  <Badge 
                    variant={getRoleBadgeVariant(role)}
                    className="text-xs"
                  >
                    {getRoleDisplayName(role)}
                  </Badge>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {adminUser?.first_name && adminUser?.last_name 
                      ? `${adminUser.first_name} ${adminUser.last_name}`
                      : 'Admin User'
                    }
                  </p>
                  <p className="text-xs text-slate-600 dark:text-slate-400">
                    {adminUser?.email}
                  </p>
                  <Badge 
                    variant={getRoleBadgeVariant(role)}
                    className="text-xs w-fit"
                  >
                    <Shield className="w-3 h-3 mr-1" />
                    {getRoleDisplayName(role)}
                  </Badge>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>Profil</span>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuItem asChild>
                <Link href="/admin/settings" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Ayarlar</span>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem asChild>
                <Link href="/" className="flex items-center">
                  <Home className="mr-2 h-4 w-4" />
                  <span>Ana Uygulamaya Dön</span>
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={handleSignOut}
                className="text-red-600 focus:text-red-600 focus:bg-red-50"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Çıkış Yap</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};
