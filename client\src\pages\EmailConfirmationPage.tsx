import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '../contexts/AuthContext';
import { Mail, CheckCircle, Clock, RefreshCw } from 'lucide-react';
import { Link } from 'wouter';

export default function EmailConfirmationPage() {
  const { resendConfirmation, emailConfirmationSent } = useAuth();
  const [email, setEmail] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [resendError, setResendError] = useState('');

  const handleResendConfirmation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setIsResending(true);
    setResendError('');
    setResendSuccess(false);

    try {
      const { error } = await resendConfirmation(email);
      
      if (error) {
        setResendError(error.message || 'Email gönderilirken bir hata oluştu');
      } else {
        setResendSuccess(true);
        setEmail('');
      }
    } catch (error) {
      setResendError('Beklenmeyen bir hata oluştu');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Main Confirmation Card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center space-y-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
              <Mail className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900">
                Email Adresinizi Doğrulayın
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Hesabınızı aktif hale getirmek için email doğrulaması gerekiyor
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900 mb-1">
                    Doğrulama emaili gönderildi!
                  </p>
                  <p className="text-blue-700">
                    Email kutunuzu kontrol edin ve doğrulama linkine tıklayın.
                    Link sizi <strong>gezdim.net</strong> adresine yönlendirecek.
                    Spam klasörünü de kontrol etmeyi unutmayın.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-amber-900 mb-1">
                    Doğrulama linki 24 saat geçerlidir
                  </p>
                  <p className="text-amber-700">
                    Link süresi dolmuşsa aşağıdaki formdan yeni bir doğrulama emaili talep edebilirsiniz.
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center">
              <Link href="/auth">
                <Button variant="outline" className="w-full">
                  Giriş Sayfasına Dön
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Resend Confirmation Card */}
        <Card className="shadow-lg border-0">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-center">
              Email Almadınız mı?
            </CardTitle>
          </CardHeader>
          <CardContent>
            {resendSuccess ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="font-medium text-green-900 mb-1">
                  Doğrulama emaili tekrar gönderildi!
                </p>
                <p className="text-sm text-green-700">
                  Lütfen email kutunuzu kontrol edin.
                </p>
              </div>
            ) : (
              <form onSubmit={handleResendConfirmation} className="space-y-4">
                <div>
                  <Label htmlFor="resend-email">Email Adresiniz</Label>
                  <Input
                    id="resend-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="mt-1"
                  />
                </div>

                {resendError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <p className="text-sm text-red-700">{resendError}</p>
                  </div>
                )}

                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isResending || !email.trim()}
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4 mr-2" />
                      Doğrulama Emaili Tekrar Gönder
                    </>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Help Text */}
        <div className="text-center text-sm text-gray-600">
          <p>
            Sorun yaşıyorsanız{' '}
            <Link href="/contact" className="text-blue-600 hover:text-blue-700 underline">
              destek ekibi ile iletişime geçin
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
