// Mock API for development/testing when backend is not available
import type { 
  Service, 
  OrganizationDetailsDto, 
  CalendarEventDto, 
  CreateOrganizationDto 
} from "../types";

// Mock data
const mockServices: Service[] = [
  {
    id: "1",
    name: "Dü<PERSON>ün Organizasyonu",
    description: "Unutulmaz düğün organizasyonları",
    price: 15000,
    duration: 480,
    category: "Düğün",
    isActive: true,
    createdAt: new Date().toISOString()
  },
  {
    id: "2", 
    name: "<PERSON><PERSON>sal <PERSON>tkinlik",
    description: "Profesyonel kurumsal etkinlik organizasyonu",
    price: 8000,
    duration: 240,
    category: "Kurumsal",
    isActive: true,
    createdAt: new Date().toISOString()
  }
];

const mockOrganizations: OrganizationDetailsDto[] = [
  {
    id: "1",
    name: "Ahmet & Ayşe Düğünü",
    description: "Romantik bahçe düğünü",
    eventDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    location: "<PERSON><PERSON><PERSON>",
    budget: 25000,
    guestCount: 150,
    status: "<PERSON>lama",
    contactPerson: "Ahmet Yılmaz",
    contactPhone: "0532 123 45 67",
    contactEmail: "<EMAIL>",
    notes: "Çiçek süslemeleri önemli",
    createdAt: new Date().toISOString(),
    services: []
  }
];

const mockEvents: CalendarEventDto[] = [
  {
    id: "1",
    title: "Ahmet & Ayşe Düğünü",
    start: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000),
    organizationId: "1"
  }
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockApi = {
  // Services
  services: {
    getAll: async (): Promise<Service[]> => {
      await delay(500);
      return [...mockServices];
    },
    create: async (service: Omit<Service, "id" | "createdAt">): Promise<Service> => {
      await delay(500);
      const newService: Service = {
        ...service,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      mockServices.push(newService);
      return newService;
    },
    update: async (id: string, service: Partial<Omit<Service, "id" | "createdAt">>): Promise<Service> => {
      await delay(500);
      const index = mockServices.findIndex(s => s.id === id);
      if (index === -1) throw new Error("Service not found");
      
      mockServices[index] = { ...mockServices[index], ...service };
      return mockServices[index];
    },
    delete: async (id: string): Promise<void> => {
      await delay(500);
      const index = mockServices.findIndex(s => s.id === id);
      if (index === -1) throw new Error("Service not found");
      mockServices.splice(index, 1);
    }
  },

  // Organizations
  organizations: {
    getAll: async (): Promise<OrganizationDetailsDto[]> => {
      await delay(500);
      return [...mockOrganizations];
    },
    getById: async (id: string): Promise<OrganizationDetailsDto> => {
      await delay(500);
      const org = mockOrganizations.find(o => o.id === id);
      if (!org) throw new Error("Organization not found");
      return org;
    },
    create: async (organization: CreateOrganizationDto): Promise<OrganizationDetailsDto> => {
      await delay(500);
      const newOrg: OrganizationDetailsDto = {
        ...organization,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        services: []
      };
      mockOrganizations.push(newOrg);
      return newOrg;
    },
    update: async (id: string, organization: any): Promise<OrganizationDetailsDto> => {
      await delay(500);
      const index = mockOrganizations.findIndex(o => o.id === id);
      if (index === -1) throw new Error("Organization not found");
      
      mockOrganizations[index] = { ...mockOrganizations[index], ...organization };
      return mockOrganizations[index];
    },
    updateStatus: async (id: string, status: string): Promise<void> => {
      await delay(500);
      const index = mockOrganizations.findIndex(o => o.id === id);
      if (index === -1) throw new Error("Organization not found");
      mockOrganizations[index].status = status;
    },
    delete: async (id: string): Promise<void> => {
      await delay(500);
      const index = mockOrganizations.findIndex(o => o.id === id);
      if (index === -1) throw new Error("Organization not found");
      mockOrganizations.splice(index, 1);
    }
  },

  // Calendar
  calendar: {
    getEvents: async (): Promise<CalendarEventDto[]> => {
      await delay(500);
      return [...mockEvents];
    }
  }
};
