using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EventFlow.Infrastructure.Services;
using EventFlow.Shared.DTOs;

namespace EventFlow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ServicesController : ControllerBase
{
    private readonly SupabaseService _supabaseService;
    private readonly ILogger<ServicesController> _logger;

    public ServicesController(SupabaseService supabaseService, ILogger<ServicesController> logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Get all services
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<ServiceDto>>> GetServices()
    {
        try
        {
            var services = await _supabaseService.GetServicesAsync();
            return Ok(services);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving services");
            return StatusCode(500, new { message = "Hizmetler yüklenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Get service by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ServiceDto>> GetService(Guid id)
    {
        try
        {
            var service = await _supabaseService.GetServiceByIdAsync(id);
            if (service == null)
            {
                return NotFound(new { message = "Hizmet bulunamadı" });
            }

            return Ok(service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service {ServiceId}", id);
            return StatusCode(500, new { message = "Hizmet yüklenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Create a new service
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ServiceDto>> CreateService(CreateServiceDto createServiceDto)
    {
        try
        {
            var service = await _supabaseService.CreateServiceAsync(createServiceDto);
            return CreatedAtAction(nameof(GetService), new { id = service.Id }, service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating service");
            return StatusCode(500, new { message = "Hizmet oluşturulurken hata oluştu" });
        }
    }

    /// <summary>
    /// Update an existing service
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ServiceDto>> UpdateService(Guid id, UpdateServiceDto updateServiceDto)
    {
        try
        {
            var service = await _supabaseService.UpdateServiceAsync(id, updateServiceDto);
            if (service == null)
            {
                return NotFound(new { message = "Hizmet bulunamadı" });
            }

            return Ok(service);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating service {ServiceId}", id);
            return StatusCode(500, new { message = "Hizmet güncellenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Delete a service
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteService(Guid id)
    {
        try
        {
            var success = await _supabaseService.DeleteServiceAsync(id);
            if (!success)
            {
                return NotFound(new { message = "Hizmet bulunamadı" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting service {ServiceId}", id);
            return StatusCode(500, new { message = "Hizmet silinirken hata oluştu" });
        }
    }
}
