import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import {
  Home,
  Settings,
  PlusCircle,
  Calendar,
  List,
  X,
  Wrench,
  Shield
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { useAuth } from "../../contexts/AuthContext";
import { useAdminAuth } from "../../hooks/useAdminAuth";

const navigation = [
  { name: "Ana Sayfa", href: "/", icon: Home },
  { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/services", icon: Settings },
  { name: "Yeni Organizasyon", href: "/new-organization", icon: PlusCircle },
  { name: "<PERSON><PERSON><PERSON>", href: "/calendar", icon: Calendar },
  { name: "Organizasyonlar", href: "/organizations", icon: List },
  { name: "<PERSON><PERSON><PERSON>", href: "/settings", icon: Wrench },
];

interface MobileSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function MobileSidebar({ open, onOpenChange }: MobileSidebarProps) {
  const [location] = useLocation();
  const { profile } = useAuth();
  const adminAuth = useAdminAuth();

  const handleLinkClick = () => {
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="left" className="w-80 p-0">
        <SheetHeader className="p-6 border-b">
          <SheetTitle className="text-left">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl font-bold text-gray-900">Organizasyon Defteri</h1>
                <p className="text-sm text-gray-500 mt-1">
                  {profile && profile.firstName && profile.lastName
                    ? `${profile.firstName} ${profile.lastName}`
                    : 'Organizasyon Yönetimi'}
                </p>
                {profile?.companyName && (
                  <p className="text-xs text-gray-400 mt-1">
                    {profile.companyName}
                  </p>
                )}
              </div>
            </div>
          </SheetTitle>
        </SheetHeader>
        
        <nav className="flex-1 p-4 space-y-2">
          {navigation.map((item) => {
            const isActive = location === item.href;
            return (
              <Link key={item.name} href={item.href} onClick={handleLinkClick}>
                <div
                  className={cn(
                    "flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-200",
                    isActive
                      ? "text-white bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg shadow-blue-500/25"
                      : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                  )}
                >
                  <item.icon className="w-5 h-5 mr-3" />
                  {item.name}
                </div>
              </Link>
            );
          })}

          {/* Admin Panel Link - Only show for admin users */}
          {adminAuth.isAdmin && (
            <Link href="/admin">
              <div
                className={cn(
                  "flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-200 cursor-pointer border-2 border-dashed mt-4",
                  location.startsWith('/admin')
                    ? "text-white bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25 border-purple-300"
                    : "text-purple-700 border-purple-200 hover:bg-purple-50 hover:text-purple-900 hover:border-purple-300"
                )}
              >
                <Shield className="w-5 h-5 mr-3" />
                Admin Panel
              </div>
            </Link>
          )}
        </nav>
      </SheetContent>
    </Sheet>
  );
}