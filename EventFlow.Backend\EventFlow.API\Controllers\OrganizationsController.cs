using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EventFlow.Infrastructure.Services;
using EventFlow.Shared.DTOs;

namespace EventFlow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class OrganizationsController : ControllerBase
{
    private readonly SupabaseService _supabaseService;
    private readonly ILogger<OrganizationsController> _logger;

    public OrganizationsController(SupabaseService supabaseService, ILogger<OrganizationsController> logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Get all organizations
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<OrganizationDto>>> GetOrganizations()
    {
        try
        {
            var organizations = await _supabaseService.GetOrganizationsAsync();
            return Ok(organizations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organizations");
            return StatusCode(500, new { message = "Organizasyonlar yüklenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Get organization by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<OrganizationDto>> GetOrganization(Guid id)
    {
        try
        {
            // Mock implementation
            var organization = new OrganizationDto
            {
                Id = id,
                CustomerId = Guid.NewGuid(),
                CustomerName = "Test Müşteri",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+90 ************",
                EventDate = DateTime.Now.AddDays(30),
                Status = "Teklif",
                DiscountPercentage = 0,
                TotalAmount = 5000,
                Notes = "Test organizasyon",
                CreatedAt = DateTime.Now.AddDays(-5),
                UpdatedAt = DateTime.Now,
                Services = new List<OrganizationServiceDto>()
            };

            return Ok(organization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organization {OrganizationId}", id);
            return StatusCode(500, new { message = "Organizasyon yüklenirken hata oluştu" });
        }
    }



    /// <summary>
    /// Create a new organization
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<OrganizationDto>> CreateOrganization(CreateOrganizationDto createOrganizationDto)
    {
        try
        {
            var organization = await _supabaseService.CreateOrganizationAsync(createOrganizationDto);
            return CreatedAtAction(nameof(GetOrganization), new { id = organization.Id }, organization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating organization");
            return StatusCode(500, new { message = "Organizasyon oluşturulurken hata oluştu" });
        }
    }

    /// <summary>
    /// Update organization
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<OrganizationDto>> UpdateOrganization(Guid id, UpdateOrganizationDto updateOrganizationDto)
    {
        try
        {
            var organization = await _supabaseService.UpdateOrganizationAsync(id, updateOrganizationDto);
            return Ok(organization);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating organization {OrganizationId}", id);
            return StatusCode(500, new { message = "Organizasyon güncellenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Update organization status
    /// </summary>
    [HttpPatch("{id}/status")]
    public async Task<ActionResult> UpdateOrganizationStatus(Guid id, [FromBody] UpdateStatusRequest request)
    {
        try
        {
            await _supabaseService.UpdateOrganizationStatusAsync(id, request.Status);
            return Ok(new { message = "Organizasyon durumu güncellendi" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating organization status {OrganizationId}", id);
            return StatusCode(500, new { message = "Organizasyon durumu güncellenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Delete organization
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteOrganization(Guid id)
    {
        try
        {
            await _supabaseService.DeleteOrganizationAsync(id);
            return Ok(new { message = "Organizasyon başarıyla silindi" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting organization {OrganizationId}", id);
            return StatusCode(500, new { message = "Organizasyon silinirken hata oluştu" });
        }
    }

    /// <summary>
    /// Get calendar events
    /// </summary>
    [HttpGet("calendar")]
    public async Task<ActionResult<IEnumerable<CalendarEventDto>>> GetCalendarEvents()
    {
        try
        {
            var events = await _supabaseService.GetCalendarEventsAsync();
            return Ok(events);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving calendar events");
            return StatusCode(500, new { message = "Takvim etkinlikleri yüklenirken hata oluştu" });
        }
    }
}

public class UpdateStatusRequest
{
    public string Status { get; set; } = string.Empty;
}
