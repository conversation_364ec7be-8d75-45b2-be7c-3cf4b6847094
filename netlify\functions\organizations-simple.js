// Simple organizations function without Supabase for testing
// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

// JWT decode function for extracting user ID from token
function decodeJWT(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('JWT decode error:', error);
    return null;
  }
}

// Extract user ID from Authorization header
function getUserIdFromAuth(event) {
  const authHeader = event.headers.authorization || event.headers.Authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = decodeJWT(token);
  return payload?.sub || null;
}

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod === 'GET') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      // Fetch organizations with customer info for the authenticated user
      const orgResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations?select=*,eventflow_customers(name,email,phone)&user_id=eq.${userId}&order=created_at.desc`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!orgResponse.ok) {
        throw new Error(`Supabase error: ${orgResponse.status}`);
      }

      const supabaseOrganizations = await orgResponse.json();

      // Fetch organization services for the authenticated user
      const servicesResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organization_services?select=*,eventflow_services(id,name,base_price)&user_id=eq.${userId}`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      const organizationServices = servicesResponse.ok ? await servicesResponse.json() : [];

      // Convert Supabase format to frontend format
      const organizations = supabaseOrganizations.map(org => {
        // Find services for this organization
        const orgServices = organizationServices
          .filter(os => os.organization_id === org.id)
          .map(os => ({
            serviceId: os.service_id,
            serviceName: os.eventflow_services?.name || 'Bilinmeyen Hizmet',
            quantity: os.quantity,
            unitPrice: parseFloat(os.unit_price),
            totalPrice: os.quantity * parseFloat(os.unit_price)
          }));

        return {
          id: org.id,
          customerId: org.customer_id,
          customerName: org.eventflow_customers?.name || 'Bilinmeyen Müşteri',
          customerEmail: org.eventflow_customers?.email,
          customerPhone: org.eventflow_customers?.phone,
          eventDate: org.event_date,
          status: org.status,
          discountPercentage: parseFloat(org.discount_percentage || 0),
          totalAmount: parseFloat(org.total_amount),
          notes: org.notes,
          createdAt: org.created_at,
          updatedAt: org.updated_at,
          services: orgServices
        };
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(organizations),
      };
    } catch (error) {
      console.error('Error fetching organizations:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Organizasyonlar yüklenirken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'POST') {
    try {
      // Get user ID from auth token
      const userId = getUserIdFromAuth(event);
      if (!userId) {
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ message: 'Unauthorized: User not authenticated' }),
        };
      }

      const requestData = JSON.parse(event.body);

      // First, create or get customer for the authenticated user
      let customerId;

      // Email alanı boş ise null olarak gönder
      const emailValue = requestData.customer.email?.trim() === '' ? null : requestData.customer.email;

      console.log('DEBUG: Original email:', requestData.customer.email);
      console.log('DEBUG: Processed email value:', emailValue);

      // Check if customer exists by email for this user (only if email is provided)
      let existingCustomers = [];

      if (emailValue) {
        console.log('DEBUG: Checking for existing customer with email:', emailValue);
        const customerCheckResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_customers?select=id&email=eq.${encodeURIComponent(emailValue)}&user_id=eq.${userId}`, {
          headers: {
            'apikey': SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json'
          }
        });

        existingCustomers = await customerCheckResponse.json();
        console.log('DEBUG: Existing customers found:', existingCustomers.length);
      } else {
        console.log('DEBUG: No email provided, skipping customer check');
      }

      if (existingCustomers.length > 0) {
        customerId = existingCustomers[0].id;
      } else {
        // Email değeri zaten yukarıda kontrol edildi

        // Create new customer with user_id
        const customerData = {
          name: requestData.customer.fullName,
          phone: requestData.customer.phoneNumber,
          user_id: userId
        };

        // Email değeri varsa ekle
        if (emailValue) {
          customerData.email = emailValue;
          console.log('DEBUG: Adding email to customer data:', emailValue);
        } else {
          console.log('DEBUG: No email added to customer data');
        }

        console.log('DEBUG: Customer data to be sent:', JSON.stringify(customerData));

        const customerResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_customers`, {
          method: 'POST',
          headers: {
            'apikey': SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
          },
          body: JSON.stringify(customerData)
        });

        if (!customerResponse.ok) {
          const errorData = await customerResponse.text();
          console.error('DEBUG: Customer creation error:', errorData);
          console.error('DEBUG: Response status:', customerResponse.status);

          if (errorData.includes('chk_email_format')) {
            console.error('DEBUG: Email format constraint violation detected');
            throw new Error('Geçerli bir e-posta adresi giriniz veya boş bırakınız');
          }

          throw new Error(`Customer creation failed: ${customerResponse.status} - ${errorData}`);
        } else {
          console.log('DEBUG: Customer created successfully');
        }

        const newCustomer = await customerResponse.json();
        customerId = newCustomer[0].id;
      }

      // First calculate total amount from services
      let totalAmount = 0;
      if (requestData.serviceIds && requestData.serviceIds.length > 0) {
        for (const serviceId of requestData.serviceIds) {
          // Get service price for the authenticated user
          const serviceResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services?select=base_price&id=eq.${serviceId}&user_id=eq.${userId}`, {
            headers: {
              'apikey': SUPABASE_ANON_KEY,
              'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json'
            }
          });

          if (serviceResponse.ok) {
            const services = await serviceResponse.json();
            if (services.length > 0) {
              totalAmount += services[0].base_price;
            }
          }
        }
      }

      // Calculate final amount with discount
      const discountAmount = totalAmount * (requestData.discountPercentage || 0) / 100;
      const finalAmount = totalAmount - discountAmount;

      // Create organization with calculated total amount
      const eventDateTime = new Date(`${requestData.eventDate}T${requestData.startTime}:00`);

      const organizationResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations`, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: JSON.stringify({
          customer_id: customerId,
          event_date: eventDateTime.toISOString(),
          status: 'Teklif',
          discount_percentage: requestData.discountPercentage || 0,
          total_amount: finalAmount, // Use calculated final amount
          notes: requestData.notes || '',
          user_id: userId
        })
      });

      if (!organizationResponse.ok) {
        throw new Error(`Organization creation failed: ${organizationResponse.status}`);
      }

      const newOrganization = await organizationResponse.json();
      const organizationId = newOrganization[0].id;

      // Add services to organization if provided
      if (requestData.serviceIds && requestData.serviceIds.length > 0) {
        for (const serviceId of requestData.serviceIds) {
          // Get service price for the authenticated user
          const serviceResponse = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_services?select=base_price&id=eq.${serviceId}&user_id=eq.${userId}`, {
            headers: {
              'apikey': SUPABASE_ANON_KEY,
              'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json'
            }
          });

          const serviceData = await serviceResponse.json();
          const unitPrice = parseFloat(serviceData[0]?.base_price || 0);

          // Add service to organization with user_id
          await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organization_services`, {
            method: 'POST',
            headers: {
              'apikey': SUPABASE_ANON_KEY,
              'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              organization_id: organizationId,
              service_id: serviceId,
              quantity: 1,
              unit_price: unitPrice,
              user_id: userId
            })
          });
        }
      }

      return {
        statusCode: 201,
        headers,
        body: JSON.stringify({
          id: organizationId,
          message: 'Organizasyon başarıyla oluşturuldu'
        }),
      };
    } catch (error) {
      console.error('Error creating organization:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Organizasyon oluşturulurken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'PATCH') {
    try {
      // Get organization ID from URL path
      const pathParts = (event.rawUrl || event.path || '').split('/');
      const organizationId = pathParts[pathParts.length - 2]; // ID is before /status

      // Check if this is a status update request
      if (pathParts[pathParts.length - 1] === 'status') {
        const requestData = JSON.parse(event.body);

        // Update only status in Supabase
        const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations?id=eq.${organizationId}`, {
          method: 'PATCH',
          headers: {
            'apikey': SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: requestData.status,
            updated_at: new Date().toISOString()
          })
        });

        if (!response.ok) {
          throw new Error(`Supabase error: ${response.status}`);
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ message: 'Organizasyon durumu güncellendi' }),
        };
      }
    } catch (error) {
      console.error('Error updating organization status:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Organizasyon durumu güncellenirken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'PUT') {
    try {
      const requestData = JSON.parse(event.body);
      // Get organization ID from URL path
      const pathParts = (event.rawUrl || event.path || '').split('/');
      const organizationId = pathParts[pathParts.length - 1];

      // Update organization in Supabase
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations?id=eq.${organizationId}`, {
        method: 'PATCH',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        },
        body: JSON.stringify({
          event_date: requestData.eventDate,
          status: requestData.status,
          discount_percentage: requestData.discountPercentage,
          total_amount: requestData.totalAmount,
          notes: requestData.notes,
          updated_at: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      const updatedOrganization = await response.json();

      if (updatedOrganization.length === 0) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ message: 'Organizasyon bulunamadı' }),
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ message: 'Organizasyon başarıyla güncellendi' }),
      };
    } catch (error) {
      console.error('Error updating organization:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Organizasyon güncellenirken hata oluştu' }),
      };
    }
  }

  if (event.httpMethod === 'DELETE') {
    try {
      // Get organization ID from URL path
      const pathParts = (event.rawUrl || event.path || '').split('/');
      const organizationId = pathParts[pathParts.length - 1];

      // First delete related organization services
      await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organization_services?organization_id=eq.${organizationId}`, {
        method: 'DELETE',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Then delete the organization
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations?id=eq.${organizationId}`, {
        method: 'DELETE',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ message: 'Organizasyon başarıyla silindi' }),
      };
    } catch (error) {
      console.error('Error deleting organization:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Organizasyon silinirken hata oluştu' }),
      };
    }
  }

  return {
    statusCode: 405,
    headers,
    body: JSON.stringify({ message: 'Method not allowed' }),
  };
};
