import React, { useState } from 'react'
import { LoginForm } from '../components/auth/LoginForm'
import { RegisterForm } from '../components/auth/RegisterForm'
import { Calendar, Users, Sparkles, ArrowRight } from 'lucide-react'

export const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true)

  const toggleMode = () => {
    setIsLogin(!isLogin)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="flex min-h-screen">
        {/* Left Side - Branding & Features */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-2/5 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 p-12 flex-col justify-between relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full"></div>
            <div className="absolute top-40 right-32 w-24 h-24 bg-white rounded-full"></div>
            <div className="absolute bottom-32 left-32 w-40 h-40 bg-white rounded-full"></div>
            <div className="absolute bottom-20 right-20 w-28 h-28 bg-white rounded-full"></div>
          </div>

          <div className="relative z-10">
            {/* Logo & Title */}
            <div className="mb-12">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mr-4">
                  <Calendar className="w-7 h-7 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-white">
                    Organizasyon Defteri
                  </h1>
                  <p className="text-blue-100 text-lg">
                    Profesyonel Etkinlik Yönetimi
                  </p>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Calendar className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Etkinlik Yönetimi
                  </h3>
                  <p className="text-blue-100">
                    Düğün, nişan, doğum günü ve kurumsal etkinliklerinizi kolayca organize edin
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Müşteri Takibi
                  </h3>
                  <p className="text-blue-100">
                    Müşteri bilgilerini güvenle saklayın ve organizasyon geçmişini takip edin
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Hizmet Kataloğu
                  </h3>
                  <p className="text-blue-100">
                    Geniş hizmet yelpazesi ile müşterilerinize en iyi deneyimi sunun
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Quote */}
          <div className="relative z-10">
            <blockquote className="text-white/90 text-lg italic">
              "Hayalinizdeki organizasyonu gerçeğe dönüştürün"
            </blockquote>
            <div className="flex items-center mt-4">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-3">
                <ArrowRight className="w-4 h-4 text-white" />
              </div>
              <span className="text-blue-100 font-medium">Organizasyon Defteri Ekibi</span>
            </div>
          </div>
        </div>

        {/* Right Side - Auth Forms */}
        <div className="flex-1 flex items-center justify-center p-6 sm:p-12">
          <div className="w-full max-w-md space-y-8">
            {/* Mobile Logo */}
            <div className="lg:hidden text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                  <Calendar className="w-8 h-8 text-white" />
                </div>
                <div className="text-left">
                  <h1 className="text-2xl font-bold text-gray-900">
                    Organizasyon Defteri
                  </h1>
                  <p className="text-gray-600">
                    Profesyonel Etkinlik Yönetimi
                  </p>
                </div>
              </div>
            </div>

            {isLogin ? (
              <LoginForm onToggleMode={toggleMode} />
            ) : (
              <RegisterForm onToggleMode={toggleMode} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
