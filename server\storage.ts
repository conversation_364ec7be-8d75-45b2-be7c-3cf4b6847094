import { 
  services, 
  customers, 
  organizations, 
  organizationServices,
  type Service, 
  type InsertService,
  type Customer,
  type InsertCustomer,
  type Organization,
  type InsertOrganization,
  type CreateOrganization,
  type OrganizationDetailsDto,
  type CalendarEventDto
} from "@shared/schema";

export interface IStorage {
  // Services
  getServices(): Promise<Service[]>;
  getService(id: string): Promise<Service | undefined>;
  createService(service: InsertService): Promise<Service>;
  updateService(id: string, service: Partial<InsertService>): Promise<Service | undefined>;
  deleteService(id: string): Promise<boolean>;

  // Customers
  getCustomers(): Promise<Customer[]>;
  getCustomer(id: string): Promise<Customer | undefined>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;

  // Organizations
  getOrganizations(): Promise<OrganizationDetailsDto[]>;
  getOrganization(id: string): Promise<OrganizationDetailsDto | undefined>;
  createOrganization(organization: CreateOrganization): Promise<OrganizationDetailsDto>;
  updateOrganizationStatus(id: string, status: string): Promise<boolean>;

  // Calendar
  getCalendarEvents(): Promise<CalendarEventDto[]>;
}

export class MemStorage implements IStorage {
  private services: Map<string, Service> = new Map();
  private customers: Map<string, Customer> = new Map();
  private organizations: Map<string, Organization> = new Map();
  private organizationServices: Map<string, { organizationId: string; serviceId: string }> = new Map();

  constructor() {
    this.seedDefaultServices();
  }

  private seedDefaultServices() {
    const defaultServices: InsertService[] = [
      {
        name: "Kamera Çekimi",
        description: "Profesyonel etkinlik kameraman hizmeti",
        price: "2500.00",
        isActive: true
      },
      {
        name: "Fotoğraf Çekimi",
        description: "Etkinlik fotoğrafçısı hizmeti",
        price: "1800.00",
        isActive: true
      },
      {
        name: "Drone Çekimi",
        description: "Havadan çekim hizmeti",
        price: "3200.00",
        isActive: true
      },
      {
        name: "Dış Çekim",
        description: "Açık hava çekim hizmeti",
        price: "2000.00",
        isActive: true
      },
      {
        name: "Orkestra",
        description: "Canlı müzik orkestra hizmeti",
        price: "8500.00",
        isActive: true
      },
      {
        name: "Bando Ekibi",
        description: "Bando müzik ekibi hizmeti",
        price: "5000.00",
        isActive: true
      },
      {
        name: "Davul Zurna Ekibi",
        description: "Geleneksel davul zurna ekibi",
        price: "3500.00",
        isActive: true
      },
      {
        name: "Ses Sistemi",
        description: "Profesyonel ses sistemi kiralama",
        price: "1500.00",
        isActive: true
      },
      {
        name: "Kadın/Erkek DJ",
        description: "Profesyonel DJ hizmeti",
        price: "2000.00",
        isActive: true
      },
      {
        name: "Sandalye Masa Çadır",
        description: "Oturma düzeni ve çadır hizmeti",
        price: "3000.00",
        isActive: true
      },
      {
        name: "Çay Kazanı",
        description: "Çay ikram hizmeti",
        price: "500.00",
        isActive: true
      },
      {
        name: "Host/Hostes/Garson",
        description: "Etkinlik personeli hizmeti",
        price: "1000.00",
        isActive: true
      },
      {
        name: "Zincir Balon Alan Süsleme",
        description: "Dekoratif süsleme hizmeti",
        price: "800.00",
        isActive: true
      },
      {
        name: "Palyaço/Animatör",
        description: "Çocuk etkinlikleri animatörü",
        price: "1200.00",
        isActive: true
      },
      {
        name: "Patlamış Mısır",
        description: "Patlamış mısır ikram hizmeti",
        price: "300.00",
        isActive: true
      },
      {
        name: "Pamuk Şeker",
        description: "Pamuk şeker ikram hizmeti",
        price: "400.00",
        isActive: true
      },
      {
        name: "Davetiye",
        description: "Özel davetiye tasarım ve baskı",
        price: "600.00",
        isActive: true
      },
      {
        name: "Projektör",
        description: "Projektör kiralama hizmeti",
        price: "750.00",
        isActive: true
      },
      {
        name: "Sarkıt Lamba",
        description: "Dekoratif aydınlatma hizmeti",
        price: "900.00",
        isActive: true
      },
      {
        name: "Sis/Konfeti Makinesi",
        description: "Efekt makinesi hizmeti",
        price: "800.00",
        isActive: true
      },
      {
        name: "Volkan/Meşale Tabancası",
        description: "Efekt tabancası hizmeti",
        price: "1100.00",
        isActive: true
      },
      {
        name: "Kuru Buz Sis Show",
        description: "Kuru buz efekt gösterisi",
        price: "1500.00",
        isActive: true
      },
      {
        name: "Kına Tahtı",
        description: "Kına gecesi taht hizmeti",
        price: "2500.00",
        isActive: true
      },
      {
        name: "Söz Nişan Düğün Tag",
        description: "Özel etiket tasarım hizmeti",
        price: "400.00",
        isActive: true
      },
      {
        name: "Giriş Yolu",
        description: "Giriş yolu dekorasyon hizmeti",
        price: "2000.00",
        isActive: true
      },
      {
        name: "Gelin Damat Masası",
        description: "Özel masa dekorasyon hizmeti",
        price: "1800.00",
        isActive: true
      },
      {
        name: "Araç Kiralama",
        description: "Özel araç kiralama hizmeti",
        price: "3500.00",
        isActive: true
      },
      {
        name: "Extralar",
        description: "Ek hizmetler",
        price: "500.00",
        isActive: true
      },
      {
        name: "Hediyelikler",
        description: "Hediyelik eşya hizmeti",
        price: "700.00",
        isActive: true
      },
      {
        name: "İkramlıklar",
        description: "İkram hizmetleri",
        price: "600.00",
        isActive: true
      }
    ];

    defaultServices.forEach(service => {
      const id = this.generateId();
      this.services.set(id, {
        id,
        ...service,
        isActive: service.isActive ?? true,
        createdAt: new Date()
      });
    });
  }

  private generateId(): string {
    return crypto.randomUUID();
  }

  // Services
  async getServices(): Promise<Service[]> {
    return Array.from(this.services.values()).filter(service => service.isActive);
  }

  async getService(id: string): Promise<Service | undefined> {
    return this.services.get(id);
  }

  async createService(service: InsertService): Promise<Service> {
    const id = this.generateId();
    const newService: Service = {
      id,
      ...service,
      isActive: service.isActive ?? true,
      createdAt: new Date()
    };
    this.services.set(id, newService);
    return newService;
  }

  async updateService(id: string, service: Partial<InsertService>): Promise<Service | undefined> {
    const existingService = this.services.get(id);
    if (!existingService) return undefined;

    const updatedService = { ...existingService, ...service };
    this.services.set(id, updatedService);
    return updatedService;
  }

  async deleteService(id: string): Promise<boolean> {
    return this.services.delete(id);
  }

  // Customers
  async getCustomers(): Promise<Customer[]> {
    return Array.from(this.customers.values());
  }

  async getCustomer(id: string): Promise<Customer | undefined> {
    return this.customers.get(id);
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    const id = this.generateId();
    const newCustomer: Customer = {
      id,
      ...customer,
      createdAt: new Date()
    };
    this.customers.set(id, newCustomer);
    return newCustomer;
  }

  // Organizations
  async getOrganizations(): Promise<OrganizationDetailsDto[]> {
    const organizationList = Array.from(this.organizations.values());
    const result: OrganizationDetailsDto[] = [];

    for (const org of organizationList) {
      const customer = this.customers.get(org.customerId);
      if (!customer) continue;

      const serviceIds = Array.from(this.organizationServices.values())
        .filter(os => os.organizationId === org.id)
        .map(os => os.serviceId);
      
      const services = serviceIds
        .map(id => this.services.get(id))
        .filter(Boolean) as Service[];

      result.push({
        id: org.id,
        customer,
        services,
        eventDate: org.eventDate,
        startTime: org.startTime,
        endTime: org.endTime,
        totalPrice: parseFloat(org.totalPrice || "0"),
        discountPercentage: parseFloat(org.discountPercentage || "0"),
        finalPrice: parseFloat(org.finalPrice || "0"),
        status: org.status,
        createdAt: org.createdAt || new Date()
      });
    }

    return result;
  }

  async getOrganization(id: string): Promise<OrganizationDetailsDto | undefined> {
    const org = this.organizations.get(id);
    if (!org) return undefined;

    const customer = this.customers.get(org.customerId);
    if (!customer) return undefined;

    const serviceIds = Array.from(this.organizationServices.values())
      .filter(os => os.organizationId === org.id)
      .map(os => os.serviceId);
    
    const services = serviceIds
      .map(id => this.services.get(id))
      .filter(Boolean) as Service[];

    return {
      id: org.id,
      customer,
      services,
      eventDate: org.eventDate,
      startTime: org.startTime,
      endTime: org.endTime,
      totalPrice: parseFloat(org.totalPrice || "0"),
      discountPercentage: parseFloat(org.discountPercentage || "0"),
      finalPrice: parseFloat(org.finalPrice || "0"),
      status: org.status,
      createdAt: org.createdAt || new Date()
    };
  }

  async createOrganization(organizationData: CreateOrganization): Promise<OrganizationDetailsDto> {
    // Create customer first
    const customer = await this.createCustomer(organizationData.customer);
    
    // Calculate prices
    const selectedServices = organizationData.serviceIds
      .map(id => this.services.get(id))
      .filter(Boolean) as Service[];
    
    const totalPrice = selectedServices.reduce((sum, service) => sum + parseFloat(service.price), 0);
    const discountAmount = (totalPrice * organizationData.discountPercentage) / 100;
    const finalPrice = totalPrice - discountAmount;

    // Create organization
    const orgId = this.generateId();
    const organization: Organization = {
      id: orgId,
      customerId: customer.id,
      eventDate: organizationData.eventDate,
      startTime: organizationData.startTime,
      endTime: organizationData.endTime,
      totalPrice: totalPrice.toFixed(2),
      discountPercentage: organizationData.discountPercentage.toFixed(2),
      finalPrice: finalPrice.toFixed(2),
      status: "Teklif",
      createdAt: new Date()
    };

    this.organizations.set(orgId, organization);

    // Create organization-service relationships
    organizationData.serviceIds.forEach(serviceId => {
      const relationId = this.generateId();
      this.organizationServices.set(relationId, {
        organizationId: orgId,
        serviceId
      });
    });

    return {
      id: orgId,
      customer,
      services: selectedServices,
      eventDate: organizationData.eventDate,
      startTime: organizationData.startTime,
      endTime: organizationData.endTime,
      totalPrice,
      discountPercentage: organizationData.discountPercentage,
      finalPrice,
      status: "Teklif",
      createdAt: organization.createdAt!
    };
  }

  async updateOrganizationStatus(id: string, status: string): Promise<boolean> {
    const org = this.organizations.get(id);
    if (!org) return false;

    this.organizations.set(id, { ...org, status });
    return true;
  }

  // Calendar
  async getCalendarEvents(): Promise<CalendarEventDto[]> {
    const organizations = await this.getOrganizations();
    
    return organizations.map(org => {
      const [year, month, day] = org.eventDate.split('-').map(Number);
      const [startHour, startMinute] = org.startTime.split(':').map(Number);
      const [endHour, endMinute] = org.endTime.split(':').map(Number);

      const start = new Date(year, month - 1, day, startHour, startMinute);
      const end = new Date(year, month - 1, day, endHour, endMinute);

      return {
        id: org.id,
        title: `${org.customer.fullName} - ${org.services.length} Hizmet`,
        start,
        end,
        customerName: org.customer.fullName,
        status: org.status
      };
    });
  }
}

export const storage = new MemStorage();
