import { useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, Check, User, Calendar, Package, CreditCard, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { CustomerStep } from "./steps/CustomerStep";
import { EventStep } from "./steps/EventStep";
import { ServicesStep } from "./steps/ServicesStep";
import { PricingStep } from "./steps/PricingStep";

// Form schemas for each step
const customerSchema = z.object({
  fullName: z.string().min(2, "Ad soyad en az 2 karakter olmalıdır"),
  phoneNumber: z.string().min(10, "Geçerli bir telefon numarası giriniz"),
  email: z.string().email("Geçerli bir e-posta adresi giriniz").optional().or(z.literal("")),
});

const eventSchema = z.object({
  eventDate: z.string().min(1, "Etkinlik tarihi gereklidir"),
  startTime: z.string().min(1, "Başlangıç saati gereklidir"),
  endTime: z.string().min(1, "Bitiş saati gereklidir"),
});

const servicesSchema = z.object({
  selectedServices: z.array(z.string()).min(1, "En az bir hizmet seçmelisiniz"),
});

const pricingSchema = z.object({
  discountPercentage: z.number().min(0).max(100),
  notes: z.string().optional(),
});

// Combined schema
const organizationSchema = z.object({
  customer: customerSchema,
  event: eventSchema,
  services: servicesSchema,
  pricing: pricingSchema,
});

export type OrganizationFormData = z.infer<typeof organizationSchema>;

interface FormStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  schema: z.ZodSchema<any>;
}

const steps: FormStep[] = [
  {
    id: "customer",
    title: "Müşteri Bilgileri",
    description: "Müşteri iletişim bilgilerini girin",
    icon: User,
    schema: customerSchema,
  },
  {
    id: "event",
    title: "Etkinlik Detayları",
    description: "Tarih ve saat bilgilerini belirleyin",
    icon: Calendar,
    schema: eventSchema,
  },
  {
    id: "services",
    title: "Hizmet Seçimi",
    description: "Sunulacak hizmetleri seçin",
    icon: Package,
    schema: servicesSchema,
  },
  {
    id: "pricing",
    title: "Fiyatlandırma",
    description: "İndirim ve notları ekleyin",
    icon: CreditCard,
    schema: pricingSchema,
  },
];

interface ModernMultiStepFormProps {
  onSubmit: (data: OrganizationFormData) => void;
  isSubmitting?: boolean;
  defaultValues?: Partial<OrganizationFormData>;
}

export function ModernMultiStepForm({ 
  onSubmit, 
  isSubmitting = false,
  defaultValues 
}: ModernMultiStepFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');

  const methods = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    mode: "onChange",
    defaultValues: defaultValues || {
      customer: { fullName: "", phoneNumber: "", email: "" },
      event: { eventDate: "", startTime: "", endTime: "" },
      services: { selectedServices: [] },
      pricing: { discountPercentage: 0, notes: "" },
    },
  });

  const { trigger, getValues, formState: { errors } } = methods;

  // Calculate progress
  const progress = ((currentStep + 1) / steps.length) * 100;

  // Validate current step
  const validateCurrentStep = async () => {
    const stepId = steps[currentStep].id;
    const isValid = await trigger(stepId as any);
    
    if (isValid) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
    }
    
    return isValid;
  };

  // Navigation functions
  const goToNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < steps.length - 1) {
      setDirection('forward');
      setCurrentStep(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentStep > 0) {
      setDirection('backward');
      setCurrentStep(prev => prev - 1);
    }
  };

  const goToStep = async (stepIndex: number) => {
    if (stepIndex < currentStep || completedSteps.has(stepIndex)) {
      setDirection(stepIndex > currentStep ? 'forward' : 'backward');
      setCurrentStep(stepIndex);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    const isValid = await validateCurrentStep();
    if (isValid) {
      const formData = getValues();
      onSubmit(formData);
    }
  };

  // Animation variants
  const slideVariants = {
    enter: (direction: string) => ({
      x: direction === 'forward' ? 300 : -300,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction: string) => ({
      x: direction === 'forward' ? -300 : 300,
      opacity: 0,
    }),
  };

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <FormProvider {...methods}>
      <div className="w-full max-w-full sm:max-w-2xl lg:max-w-4xl mx-auto space-y-2 sm:space-y-4 lg:space-y-6">
        {/* Header */}
        <div className="text-center space-y-0.5 sm:space-y-1 lg:space-y-2 px-0.5 sm:px-1">
          <h1 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 leading-tight">Yeni Organizasyon Oluştur</h1>
          <p className="text-[10px] sm:text-[11px] lg:text-xs text-gray-600 leading-tight">Adım adım organizasyon bilgilerini tamamlayın</p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-0.5 sm:space-y-1 px-0.5 sm:px-1">
          <div className="flex justify-between items-center">
            <span className="text-[9px] sm:text-[10px] font-medium text-gray-700">
              Adım {currentStep + 1} / {steps.length}
            </span>
            <span className="text-[9px] sm:text-[10px] text-gray-500">
              %{Math.round(progress)} tamamlandı
            </span>
          </div>
          <Progress value={progress} className="h-0.5 sm:h-1" />
        </div>

        {/* Step Navigation */}
        <div className="flex justify-center px-0 sm:px-1">
          <div className="flex items-center space-x-0.5 sm:space-x-1 overflow-x-auto pb-1 sm:pb-2 w-full justify-start sm:justify-center">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.has(index);
              const isCurrent = index === currentStep;
              const isAccessible = index <= currentStep || completedSteps.has(index);

              return (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => goToStep(index)}
                    disabled={!isAccessible}
                    className={cn(
                      "flex flex-col items-center space-y-0 sm:space-y-0.5 p-0.5 sm:p-1 lg:p-1.5 rounded transition-all duration-200",
                      "min-w-[45px] sm:min-w-[50px] lg:min-w-[60px] text-center flex-shrink-0",
                      isCurrent && "bg-blue-50 border-2 border-blue-500",
                      isCompleted && !isCurrent && "bg-green-50 border-2 border-green-500",
                      !isAccessible && "opacity-50 cursor-not-allowed",
                      isAccessible && !isCurrent && !isCompleted && "hover:bg-gray-50 border-2 border-transparent"
                    )}
                  >
                    <div className={cn(
                      "w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 rounded-full flex items-center justify-center transition-colors",
                      isCurrent && "bg-blue-500 text-white",
                      isCompleted && !isCurrent && "bg-green-500 text-white",
                      !isCurrent && !isCompleted && "bg-gray-200 text-gray-600"
                    )}>
                      {isCompleted && !isCurrent ? (
                        <Check className="w-2 h-2 sm:w-2.5 sm:h-2.5 lg:w-3 lg:h-3" />
                      ) : (
                        <step.icon className="w-2 h-2 sm:w-2.5 sm:h-2.5 lg:w-3 lg:h-3" />
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className={cn(
                        "text-[8px] sm:text-[9px] lg:text-[10px] font-medium leading-none",
                        isCurrent && "text-blue-700",
                        isCompleted && !isCurrent && "text-green-700",
                        !isCurrent && !isCompleted && "text-gray-600"
                      )}>
                        {step.title}
                      </p>
                    </div>
                  </button>
                  {index < steps.length - 1 && (
                    <div className="w-8 h-px bg-gray-300 mx-2" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Step Content */}
        <Card className="min-h-[280px] sm:min-h-[320px] lg:min-h-[360px] mx-0 border-0 shadow-lg">
          <CardContent className="p-1.5 sm:p-2 lg:p-3">
            <div className="mb-1.5 sm:mb-2 lg:mb-3">
              <div className="flex items-center space-x-1 sm:space-x-1.5 mb-0.5 sm:mb-1">
                <currentStepData.icon className="w-3.5 h-3.5 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-blue-600" />
                <h2 className="text-sm sm:text-base lg:text-lg font-semibold text-gray-900 leading-tight">
                  {currentStepData.title}
                </h2>
              </div>
              <p className="text-[10px] sm:text-xs lg:text-sm text-gray-600 leading-tight">{currentStepData.description}</p>
            </div>

            <AnimatePresence mode="wait" custom={direction}>
              <motion.div
                key={currentStep}
                custom={direction}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{
                  x: { type: "spring", stiffness: 300, damping: 30 },
                  opacity: { duration: 0.2 },
                }}
              >
                {/* Step content */}
                <div className="space-y-6">
                  {currentStep === 0 && <CustomerStep />}
                  {currentStep === 1 && <EventStep />}
                  {currentStep === 2 && <ServicesStep />}
                  {currentStep === 3 && <PricingStep />}
                </div>
              </motion.div>
            </AnimatePresence>
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between items-center px-0">
          <Button
            type="button"
            variant="outline"
            onClick={goToPrevious}
            disabled={isFirstStep}
            size="sm"
            className="flex items-center space-x-1 text-xs sm:text-sm"
          >
            <ChevronLeft className="w-3 h-3 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Önceki</span>
            <span className="sm:hidden">Geri</span>
          </Button>

          <div className="flex items-center space-x-2">
            {completedSteps.size > 0 && (
              <Badge variant="secondary" className="text-xs sm:text-sm">
                {completedSteps.size} / {steps.length}
              </Badge>
            )}
          </div>

          {isLastStep ? (
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={isSubmitting}
              size="sm"
              className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-xs sm:text-sm"
            >
              {isSubmitting ? (
                <>
                  <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span className="hidden sm:inline">Oluşturuluyor...</span>
                  <span className="sm:hidden">Oluşturuluyor</span>
                </>
              ) : (
                <>
                  <Check className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Organizasyon Oluştur</span>
                  <span className="sm:hidden">Oluştur</span>
                </>
              )}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={goToNext}
              size="sm"
              className="flex items-center space-x-1 text-xs sm:text-sm"
            >
              <span className="hidden sm:inline">Sonraki</span>
              <span className="sm:hidden">İleri</span>
              <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
            </Button>
          )}
        </div>
      </div>
    </FormProvider>
  );
}
