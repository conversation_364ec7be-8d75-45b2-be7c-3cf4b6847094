import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, formatDate } from "../../utils/formatters";
import { OrganizationDetailsDto } from "../../types";
import { Calendar, User, Phone, Mail, MapPin, FileText, DollarSign } from "lucide-react";

interface OrganizationDetailModalProps {
  organization: OrganizationDetailsDto | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function OrganizationDetailModal({
  organization,
  open,
  onOpenChange,
}: OrganizationDetailModalProps) {
  if (!organization) return null;

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Teklif":
        return "secondary";
      case "Kesinleşti":
        return "default";
      case "İptal":
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Organizasyon Detayları
          </DialogTitle>
          <DialogDescription>
            {organization.customerName} - {formatDate(organization.eventDate)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Durum ve Temel Bilgiler */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-sm text-gray-500 mb-2">DURUM</h3>
              <Badge variant={getStatusBadgeVariant(organization.status)}>
                {organization.status}
              </Badge>
            </div>
            <div>
              <h3 className="font-semibold text-sm text-gray-500 mb-2">ETKİNLİK TARİHİ</h3>
              <p className="font-medium">{formatDate(organization.eventDate)}</p>
            </div>
          </div>

          <Separator />

          {/* Müşteri Bilgileri */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <User className="w-5 h-5" />
              Müşteri Bilgileri
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Ad Soyad</p>
                  <p className="font-medium">{organization.customerName}</p>
                </div>
              </div>
              {organization.customerEmail && (
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">E-posta</p>
                    <p className="font-medium">{organization.customerEmail}</p>
                  </div>
                </div>
              )}
              {organization.customerPhone && (
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Telefon</p>
                    <p className="font-medium">{organization.customerPhone}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Hizmetler */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Hizmetler
            </h3>
            {organization.services && organization.services.length > 0 ? (
              <div className="space-y-3">
                {organization.services.map((service, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{service.serviceName}</p>
                      <p className="text-sm text-gray-500">
                        {service.quantity} adet × {formatCurrency(service.unitPrice)}
                      </p>
                    </div>
                    <p className="font-semibold">{formatCurrency(service.totalPrice)}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">Henüz hizmet eklenmemiş</p>
            )}
          </div>

          <Separator />

          {/* Fiyat Bilgileri */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Fiyat Bilgileri
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Ara Toplam:</span>
                <span>{formatCurrency(organization.totalAmount)}</span>
              </div>
              {organization.discountPercentage > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>İskonto (%{organization.discountPercentage}):</span>
                  <span>-{formatCurrency(organization.totalAmount * organization.discountPercentage / 100)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>Toplam Tutar:</span>
                <span>{formatCurrency(organization.totalAmount * (1 - organization.discountPercentage / 100))}</span>
              </div>
            </div>
          </div>

          {/* Notlar */}
          {organization.notes && (
            <>
              <Separator />
              <div>
                <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Notlar
                </h3>
                <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">{organization.notes}</p>
              </div>
            </>
          )}

          {/* Oluşturma Bilgileri */}
          <Separator />
          <div className="text-sm text-gray-500">
            <p>Oluşturulma: {formatDate(organization.createdAt)}</p>
            <p>Son Güncelleme: {formatDate(organization.updatedAt)}</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
