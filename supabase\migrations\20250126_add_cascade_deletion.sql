-- Migration: Add cascade deletion for user data
-- When a user is deleted, all their related data should be deleted too

-- First, let's check current foreign key constraints
-- and add CASCADE DELETE where needed

-- 1. Organizations table - delete organizations when user is deleted
ALTER TABLE organizations 
DROP CONSTRAINT IF EXISTS organizations_user_id_fkey;

ALTER TABLE organizations 
ADD CONSTRAINT organizations_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- 2. Services table - delete services when user is deleted
ALTER TABLE services 
DROP CONSTRAINT IF EXISTS services_user_id_fkey;

ALTER TABLE services 
ADD CONSTRAINT services_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- 3. Services table - delete services when organization is deleted
ALTER TABLE services 
DROP CONSTRAINT IF EXISTS services_organization_id_fkey;

ALTER TABLE services 
ADD CONSTRAINT services_organization_id_fkey 
FOREIGN KEY (organization_id) REFERENCES organizations(id) 
ON DELETE CASCADE;

-- 4. EventFlow profiles table - delete profile when user is deleted
ALTER TABLE eventflow_profiles 
DROP CONSTRAINT IF EXISTS eventflow_profiles_id_fkey;

ALTER TABLE eventflow_profiles 
ADD CONSTRAINT eventflow_profiles_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- 5. Admin users table - delete admin record when user is deleted
ALTER TABLE admin_users 
DROP CONSTRAINT IF EXISTS admin_users_user_id_fkey;

ALTER TABLE admin_users 
ADD CONSTRAINT admin_users_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- 6. Admin logs table - keep logs but set user_id to NULL when user is deleted
ALTER TABLE admin_logs 
DROP CONSTRAINT IF EXISTS admin_logs_user_id_fkey;

ALTER TABLE admin_logs 
ADD CONSTRAINT admin_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) 
ON DELETE SET NULL;

-- Create a function to handle user deletion with proper cleanup
CREATE OR REPLACE FUNCTION delete_user_and_data(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_orgs_count INTEGER := 0;
    deleted_services_count INTEGER := 0;
    result JSON;
BEGIN
    -- Check if user exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = target_user_id) THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User not found'
        );
    END IF;

    -- Count organizations and services before deletion
    SELECT COUNT(*) INTO deleted_orgs_count 
    FROM organizations 
    WHERE user_id = target_user_id;

    SELECT COUNT(*) INTO deleted_services_count 
    FROM services 
    WHERE user_id = target_user_id;

    -- Delete user from auth.users (this will cascade to all related tables)
    DELETE FROM auth.users WHERE id = target_user_id;

    -- Return summary
    result := json_build_object(
        'success', true,
        'user_id', target_user_id,
        'deleted_organizations', deleted_orgs_count,
        'deleted_services', deleted_services_count,
        'message', 'User and all related data deleted successfully'
    );

    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$;

-- Grant execute permission to authenticated users (admin only)
GRANT EXECUTE ON FUNCTION delete_user_and_data(UUID) TO authenticated;

-- Create RLS policy for the function (only admins can execute)
CREATE POLICY "Only admins can delete users" ON admin_users
FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE user_id = auth.uid() 
        AND role IN ('super_admin', 'admin')
    )
);

-- Create a function to get user deletion preview
CREATE OR REPLACE FUNCTION get_user_deletion_preview(target_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_info RECORD;
    orgs_count INTEGER := 0;
    services_count INTEGER := 0;
    result JSON;
BEGIN
    -- Get user info
    SELECT u.email, u.created_at, p.first_name, p.last_name, p.company_name
    INTO user_info
    FROM auth.users u
    LEFT JOIN eventflow_profiles p ON u.id = p.id
    WHERE u.id = target_user_id;

    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User not found'
        );
    END IF;

    -- Count related data
    SELECT COUNT(*) INTO orgs_count 
    FROM organizations 
    WHERE user_id = target_user_id;

    SELECT COUNT(*) INTO services_count 
    FROM services 
    WHERE user_id = target_user_id;

    -- Return preview
    result := json_build_object(
        'success', true,
        'user', json_build_object(
            'id', target_user_id,
            'email', user_info.email,
            'first_name', user_info.first_name,
            'last_name', user_info.last_name,
            'company_name', user_info.company_name,
            'created_at', user_info.created_at
        ),
        'will_delete', json_build_object(
            'organizations', orgs_count,
            'services', services_count,
            'profile', 1,
            'auth_record', 1
        )
    );

    RETURN result;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_user_deletion_preview(UUID) TO authenticated;

-- Additional fixes for mobile authentication issues
-- 7. User activities table - ensure it exists and has proper constraints
CREATE TABLE IF NOT EXISTS user_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    activity_type TEXT NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Drop existing constraint if it exists
ALTER TABLE user_activities
DROP CONSTRAINT IF EXISTS user_activities_user_id_fkey;

-- Add foreign key constraint that references eventflow_profiles instead of auth.users
-- This prevents the 409 error when user_activities tries to reference a user that doesn't have a profile
ALTER TABLE user_activities
ADD CONSTRAINT user_activities_user_id_fkey
FOREIGN KEY (user_id) REFERENCES eventflow_profiles(id)
ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON user_activities(created_at);

-- Ensure eventflow_profiles table has proper structure
ALTER TABLE eventflow_profiles
ADD COLUMN IF NOT EXISTS first_name TEXT,
ADD COLUMN IF NOT EXISTS last_name TEXT,
ADD COLUMN IF NOT EXISTS company_name TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create indexes for eventflow_profiles
CREATE INDEX IF NOT EXISTS idx_eventflow_profiles_email ON eventflow_profiles(id);

-- Enable RLS on user_activities if not already enabled
ALTER TABLE user_activities ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for user_activities
DROP POLICY IF EXISTS "Users can only access their own activities" ON user_activities;
CREATE POLICY "Users can only access their own activities" ON user_activities
FOR ALL USING (user_id = auth.uid());

-- Create RLS policy for eventflow_profiles
DROP POLICY IF EXISTS "Users can only access their own profile" ON eventflow_profiles;
CREATE POLICY "Users can only access their own profile" ON eventflow_profiles
FOR ALL USING (id = auth.uid());
