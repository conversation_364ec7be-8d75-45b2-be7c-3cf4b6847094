import { useFormContext } from "react-hook-form";
import { motion } from "framer-motion";
import { Calendar, Clock, AlertCircle, CalendarDays } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { cn } from "@/lib/utils";

export function EventStep() {
  const { control, formState: { errors }, watch } = useFormContext();

  const eventDate = watch("event.eventDate");
  const startTime = watch("event.startTime");
  const endTime = watch("event.endTime");

  const inputVariants = {
    focus: { scale: 1.02, transition: { duration: 0.2 } },
    blur: { scale: 1, transition: { duration: 0.2 } }
  };

  // Calculate duration if both times are provided
  const calculateDuration = () => {
    if (startTime && endTime) {
      const start = new Date(`2000-01-01T${startTime}`);
      const end = new Date(`2000-01-01T${endTime}`);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (diffMs > 0) {
        return `${diffHours} saat ${diffMinutes} dakika`;
      }
    }
    return null;
  };

  const duration = calculateDuration();

  // Get minimum date (today)
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <Calendar className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-gray-900">Etkinlik Detayları</h3>
        <p className="text-sm sm:text-base text-gray-600 px-2">Etkinliğinizin tarih ve saat bilgilerini belirleyin</p>
      </div>

      {/* Form Fields */}
      <div className="space-y-6">
        {/* Event Date */}
        <FormField
          control={control}
          name="event.eventDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center space-x-2">
                <CalendarDays className="w-4 h-4 text-gray-500" />
                <span>Etkinlik Tarihi</span>
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <motion.div
                  variants={inputVariants}
                  whileFocus="focus"
                  initial="blur"
                >
                  <Input
                    {...field}
                    type="date"
                    min={today}
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      "focus:ring-2 focus:ring-green-500 focus:border-green-500",
                      errors.event?.eventDate && "border-red-500 focus:ring-red-500"
                    )}
                  />
                </motion.div>
              </FormControl>
              <FormMessage className="flex items-center space-x-1">
                {errors.event?.eventDate && (
                  <AlertCircle className="w-4 h-4 text-red-500" />
                )}
              </FormMessage>
            </FormItem>
          )}
        />

        {/* Time Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Start Time */}
          <FormField
            control={control}
            name="event.startTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>Başlangıç Saati</span>
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <motion.div
                    variants={inputVariants}
                    whileFocus="focus"
                    initial="blur"
                  >
                    <Input
                      {...field}
                      type="time"
                      className={cn(
                        "h-12 text-base transition-all duration-200",
                        "focus:ring-2 focus:ring-green-500 focus:border-green-500",
                        errors.event?.startTime && "border-red-500 focus:ring-red-500"
                      )}
                    />
                  </motion.div>
                </FormControl>
                <FormMessage className="flex items-center space-x-1">
                  {errors.event?.startTime && (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                </FormMessage>
              </FormItem>
            )}
          />

          {/* End Time */}
          <FormField
            control={control}
            name="event.endTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>Bitiş Saati</span>
                  <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <motion.div
                    variants={inputVariants}
                    whileFocus="focus"
                    initial="blur"
                  >
                    <Input
                      {...field}
                      type="time"
                      className={cn(
                        "h-12 text-base transition-all duration-200",
                        "focus:ring-2 focus:ring-green-500 focus:border-green-500",
                        errors.event?.endTime && "border-red-500 focus:ring-red-500"
                      )}
                    />
                  </motion.div>
                </FormControl>
                <FormMessage className="flex items-center space-x-1">
                  {errors.event?.endTime && (
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  )}
                </FormMessage>
              </FormItem>
            )}
          />
        </div>

        {/* Duration Display */}
        {duration && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 border border-green-200 rounded-lg p-4"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <Clock className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-green-900">Etkinlik Süresi</h4>
                <p className="text-green-800 font-semibold">{duration}</p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Event Summary */}
        {eventDate && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-50 border border-gray-200 rounded-lg p-4"
          >
            <h4 className="text-sm font-medium text-gray-900 mb-2">Etkinlik Özeti</h4>
            <div className="space-y-2 text-sm text-gray-700">
              <div className="flex items-center space-x-2">
                <CalendarDays className="w-4 h-4 text-gray-500" />
                <span>
                  {new Date(eventDate).toLocaleDateString('tr-TR', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
              {startTime && endTime && (
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>{startTime} - {endTime}</span>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>

      {/* Tips */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-white text-xs font-bold">i</span>
          </div>
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-green-900">İpuçları</h4>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• Etkinlik tarihi bugünden sonra olmalıdır</li>
              <li>• Bitiş saati başlangıç saatinden sonra olmalıdır</li>
              <li>• Hazırlık ve temizlik için ekstra süre hesaplayın</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Adım 2/4 - Etkinlik Detayları</span>
        </div>
      </div>
    </div>
  );
}
