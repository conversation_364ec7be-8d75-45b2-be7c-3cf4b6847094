import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import {
  Users,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Mail,
  Calendar,
  Building,
  Activity,
  Shield
} from 'lucide-react';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { formatCurrency } from '../../utils/formatters';
import { AdminUser } from '../../types';
import { UserDetailModal } from '../../components/admin/modals/UserDetailModal';
import { UserEditModal } from '../../components/admin/modals/UserEditModal';
import { UserDeleteModal } from '../../components/admin/modals/UserDeleteModal';
import { UserBlockModal } from '../../components/admin/modals/UserBlockModal';

export default function AdminUsersPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  
  // Verified users modal states
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [showUserDetailModal, setShowUserDetailModal] = useState(false);
  const [showUserEditModal, setShowUserEditModal] = useState(false);
  const [showUserDeleteModal, setShowUserDeleteModal] = useState(false);
  const [showUserBlockModal, setShowUserBlockModal] = useState(false);
  const [isUserActionLoading, setIsUserActionLoading] = useState(false);

  // Unverified users states
  const [selectedUnverifiedUsers, setSelectedUnverifiedUsers] = useState<string[]>([]);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);
  const [selectedSingleUser, setSelectedSingleUser] = useState<any>(null);
  const [showSingleDeleteDialog, setShowSingleDeleteDialog] = useState(false);
  
  const pageSize = 20;

  // Fetch users with statistics
  const { data: users, isLoading, error, refetch } = useQuery({
    queryKey: ['admin', 'users', currentPage, pageSize],
    queryFn: () => adminApi.users.getAll(pageSize, currentPage * pageSize),
    refetchInterval: 30000
  });

  // Fetch unverified users
  const { data: unverifiedUsers, isLoading: isLoadingUnverified, refetch: refetchUnverified, error: unverifiedError } = useQuery({
    queryKey: ['admin', 'unverified-users'],
    queryFn: () => {
      console.log('🔍 useQuery calling getUnverified...');
      return adminApi.users.getUnverified();
    },
    refetchInterval: 60000, // Refresh every minute
    retry: 3,
    onError: (error) => {
      console.error('❌ useQuery error for unverified users:', error);
    },
    onSuccess: (data) => {
      console.log('✅ useQuery success for unverified users:', data);
    }
  });

  // Filter users based on search term
  const filteredUsers = users?.filter(user => 
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.company_name?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Filter unverified users
  const filteredUnverifiedUsers = unverifiedUsers?.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Unverified users functions
  const handleSelectUnverifiedUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUnverifiedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUnverifiedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleSelectAllUnverified = (checked: boolean) => {
    if (checked) {
      setSelectedUnverifiedUsers(filteredUnverifiedUsers?.map(u => u.id) || []);
    } else {
      setSelectedUnverifiedUsers([]);
    }
  };

  // Verified users handlers
  const handleViewUser = (user: any) => {
    console.log('👁️ Viewing user:', user);
    console.log('👁️ User email_confirmed_at:', user.email_confirmed_at);
    setSelectedUser(user);
    setShowUserDetailModal(true);
  };

  const handleEditUser = (user: AdminUser) => {
    console.log('✏️ Editing user:', user);
    setSelectedUser(user);
    setShowUserEditModal(true);
  };

  const handleBlockUser = (user: AdminUser) => {
    console.log('🚫 Blocking/Unblocking user:', user);
    setSelectedUser(user);
    setShowUserBlockModal(true);
  };

  const handleDeleteUser = (user: AdminUser) => {
    console.log('🗑️ Deleting verified user:', user);
    setSelectedUser(user);
    setShowUserDeleteModal(true);
  };

  // Modal action handlers
  const handleUserEdit = async (data: any) => {
    if (!selectedUser) return;

    setIsUserActionLoading(true);
    try {
      await adminApi.users.update(selectedUser.id, data);

      toast({
        title: "✅ Kullanıcı güncellendi",
        description: `${data.first_name} ${data.last_name} bilgileri başarıyla güncellendi.`,
        variant: "success",
      });

      setShowUserEditModal(false);
      setSelectedUser(null);
      refetch(); // Refresh user list
    } catch (error) {
      toast({
        title: "❌ Güncelleme Hatası",
        description: 'Kullanıcı güncellenirken hata oluştu: ' + (error as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsUserActionLoading(false);
    }
  };

  const handleUserDelete = async () => {
    if (!selectedUser) return;

    setIsUserActionLoading(true);
    try {
      await adminApi.users.deleteManual(selectedUser.id);

      toast({
        title: "✅ Kullanıcı silindi",
        description: `${selectedUser.email} kullanıcısı başarıyla silindi.`,
        variant: "success",
      });

      setShowUserDeleteModal(false);
      setSelectedUser(null);

      // Force refresh both queries
      await refetch(); // Refresh verified users
      await refetchUnverified(); // Refresh unverified users

      // Also invalidate all user-related queries
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['admin', 'unverified-users'] });
    } catch (error) {
      toast({
        title: "❌ Silme Hatası",
        description: 'Kullanıcı silinirken hata oluştu: ' + (error as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsUserActionLoading(false);
    }
  };

  const handleUserBlock = async () => {
    if (!selectedUser) return;

    setIsUserActionLoading(true);
    try {
      const newBlockedState = !selectedUser.is_blocked;
      const action = newBlockedState ? 'engellendi' : 'engeli kaldırıldı';

      await adminApi.users.toggleBlock(selectedUser.id, newBlockedState);

      toast({
        title: `✅ Kullanıcı ${action}`,
        description: `${selectedUser.email} kullanıcısı başarıyla ${action}.`,
        variant: "success",
      });

      setShowUserBlockModal(false);
      setSelectedUser(null);
      refetch(); // Refresh user list
    } catch (error) {
      toast({
        title: "❌ İşlem Hatası",
        description: 'İşlem gerçekleştirilirken hata oluştu: ' + (error as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsUserActionLoading(false);
    }
  };

  // Unverified users handlers
  const handleDeleteUnverifiedUser = async (user: any) => {
    setSelectedSingleUser(user);
    setShowSingleDeleteDialog(true);
  };

  const confirmDeleteSingleUser = async () => {
    if (!selectedSingleUser) return;

    try {
      await adminApi.users.deleteUnverified(selectedSingleUser.id);
      refetchUnverified();
      setShowSingleDeleteDialog(false);
      setSelectedSingleUser(null);
      toast({
        title: "✅ Doğrulanmamış hesap silindi",
        description: `${selectedSingleUser.email} hesabı başarıyla silindi.`,
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "❌ Silme Hatası",
        description: 'Hesap silinirken hata oluştu: ' + (error as Error).message,
        variant: "destructive",
      });
    }
  };

  const handleBulkDeleteUnverified = async () => {
    if (selectedUnverifiedUsers.length === 0) return;

    setIsBulkDeleting(true);
    try {
      const result = await adminApi.users.bulkDeleteUnverified(selectedUnverifiedUsers);
      refetchUnverified();
      setSelectedUnverifiedUsers([]);
      setShowBulkDeleteDialog(false);
      
      toast({
        title: "✅ Toplu silme tamamlandı",
        description: `${result.success} hesap silindi, ${result.failed} hesap silinemedi.`,
        variant: result.failed > 0 ? "destructive" : "success",
      });
    } catch (error) {
      toast({
        title: "❌ Toplu silme hatası",
        description: 'Hesaplar silinirken hata oluştu: ' + (error as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsBulkDeleting(false);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Az önce';
    if (diffInHours < 24) return `${diffInHours} saat önce`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} gün önce`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} hafta önce`;
  };

  const getStatusBadge = (user: any) => {
    if (!user.is_active) {
      return <Badge variant="secondary">Pasif</Badge>;
    }
    if (user.email_confirmed_at) {
      return <Badge variant="default">Doğrulanmış</Badge>;
    }
    return <Badge variant="secondary">Doğrulanmamış</Badge>;
  };

  console.log('🔍 Debug - Users data:', users);
  console.log('🔍 Debug - Unverified users data:', unverifiedUsers);
  console.log('🔍 Debug - Unverified users loading:', isLoadingUnverified);
  console.log('🔍 Debug - Unverified users error:', unverifiedError);
  console.log('🔍 Debug - Filtered users:', filteredUsers);
  console.log('🔍 Debug - Filtered unverified users:', filteredUnverifiedUsers);

  return (
    <AdminLayout
      title="Kullanıcı Yönetimi"
      subtitle="Tüm kullanıcıları görüntüle ve yönet"
    >
      <div className="space-y-6">
        <Tabs defaultValue="verified" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="verified" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Doğrulanmış Kullanıcılar ({filteredUsers.length})
            </TabsTrigger>
            <TabsTrigger value="unverified" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Doğrulanmamış Hesaplar ({filteredUnverifiedUsers.length})
            </TabsTrigger>
          </TabsList>

          {/* Verified Users Tab */}
          <TabsContent value="verified" className="space-y-6">
            {/* Header Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Kullanıcı ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Yenile
                </Button>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Toplam Kullanıcı</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{users?.length || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Kayıtlı kullanıcı sayısı
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Aktif Kullanıcı</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {users?.filter(u => u.is_active).length || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Aktif hesap sayısı
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Doğrulanmamış</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{unverifiedUsers?.length || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Email doğrulaması bekleyen
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Users Table */}
            <Card>
              <CardHeader>
                <CardTitle>Doğrulanmış Kullanıcılar</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  </div>
                ) : filteredUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Kullanıcı bulunamadı</p>
                    <p className="text-sm text-gray-400 mt-2">Arama kriterlerinizi değiştirin</p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Kullanıcı</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Şirket</TableHead>
                        <TableHead>Durum</TableHead>
                        <TableHead>Kayıt Tarihi</TableHead>
                        <TableHead>İşlemler</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-blue-600">
                                  {user.first_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium">
                                  {user.first_name && user.last_name
                                    ? `${user.first_name} ${user.last_name}`
                                    : user.email.split('@')[0]
                                  }
                                </div>
                                <div className="text-sm text-gray-500">ID: {user.id.slice(0, 8)}...</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span>{user.email}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span>{user.company_name || '-'}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(user)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-sm">
                                {new Date(user.created_at).toLocaleDateString('tr-TR')}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewUser(user)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  Detayları Görüntüle
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleEditUser(user)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Düzenle
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleBlockUser(user)} className="text-orange-600">
                                  <Shield className="mr-2 h-4 w-4" />
                                  {user.is_blocked ? 'Engeli Kaldır' : 'Engelle'}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleDeleteUser(user)} className="text-red-600">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Sil
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Unverified Users Tab */}
          <TabsContent value="unverified" className="space-y-6">
            {/* Header Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <div className="flex flex-1 items-center space-x-2">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Email ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={() => refetchUnverified()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Yenile
                </Button>
                {selectedUnverifiedUsers.length > 0 && (
                  <Button
                    variant="destructive"
                    onClick={() => setShowBulkDeleteDialog(true)}
                    disabled={isBulkDeleting}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Seçilenleri Sil ({selectedUnverifiedUsers.length})
                  </Button>
                )}
              </div>
            </div>

            {/* Unverified Users Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Doğrulanmamış Hesaplar
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Email doğrulaması yapmayan kullanıcılar. Bu hesaplar sisteme giriş yapamaz.
                </p>
              </CardHeader>
              <CardContent>
                {isLoadingUnverified ? (
                  <div className="flex items-center justify-center h-32">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  </div>
                ) : filteredUnverifiedUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <p className="text-gray-500">Doğrulanmamış hesap bulunmuyor</p>
                    <p className="text-sm text-gray-400 mt-2">Tüm kullanıcılar email adreslerini doğrulamış</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Select All */}
                    <div className="flex items-center space-x-2 pb-2 border-b">
                      <Checkbox
                        checked={selectedUnverifiedUsers.length === filteredUnverifiedUsers.length}
                        onCheckedChange={handleSelectAllUnverified}
                      />
                      <span className="text-sm font-medium">
                        Tümünü Seç ({filteredUnverifiedUsers.length} hesap)
                      </span>
                    </div>

                    {/* Users List */}
                    <div className="space-y-2">
                      {filteredUnverifiedUsers.map((user) => (
                        <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedUnverifiedUsers.includes(user.id)}
                              onCheckedChange={(checked) => handleSelectUnverifiedUser(user.id, checked as boolean)}
                            />
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span className="font-medium">{user.email}</span>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <div className="flex items-center text-sm text-gray-500">
                                <Calendar className="h-3 w-3 mr-1" />
                                {formatTimeAgo(user.created_at)}
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                Doğrulanmamış
                              </Badge>
                            </div>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteUnverifiedUser(user)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Hesabı Sil
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Info Box */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-700 mb-2">
                        <AlertCircle className="h-4 w-4" />
                        <span className="font-medium">Bilgi</span>
                      </div>
                      <p className="text-sm text-blue-600">
                        Bu hesaplar email doğrulaması yapmadığı için sisteme giriş yapamaz.
                        Uzun süre doğrulanmayan hesapları temizleyebilirsiniz.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Single Delete Dialog */}
        <AlertDialog open={showSingleDeleteDialog} onOpenChange={setShowSingleDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Hesabı Sil</AlertDialogTitle>
              <AlertDialogDescription>
                <strong>{selectedSingleUser?.email}</strong> hesabını kalıcı olarak silmek istediğinizden emin misiniz?
                Bu işlem geri alınamaz.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDeleteSingleUser} className="bg-red-600 hover:bg-red-700">
                Evet, Sil
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Bulk Delete Dialog */}
        <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Toplu Hesap Silme</AlertDialogTitle>
              <AlertDialogDescription>
                Seçilen <strong>{selectedUnverifiedUsers.length}</strong> doğrulanmamış hesabı kalıcı olarak silmek istediğinizden emin misiniz?
                Bu işlem geri alınamaz.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleBulkDeleteUnverified}
                className="bg-red-600 hover:bg-red-700"
                disabled={isBulkDeleting}
              >
                {isBulkDeleting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Siliniyor...
                  </>
                ) : (
                  'Evet, Hepsini Sil'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* User Management Modals */}
        <UserDetailModal
          user={selectedUser}
          open={showUserDetailModal}
          onOpenChange={setShowUserDetailModal}
        />

        <UserEditModal
          user={selectedUser}
          open={showUserEditModal}
          onOpenChange={setShowUserEditModal}
          onSave={handleUserEdit}
          isLoading={isUserActionLoading}
        />

        <UserDeleteModal
          user={selectedUser}
          open={showUserDeleteModal}
          onOpenChange={setShowUserDeleteModal}
          onConfirm={handleUserDelete}
          isLoading={isUserActionLoading}
        />

        <UserBlockModal
          user={selectedUser}
          open={showUserBlockModal}
          onOpenChange={setShowUserBlockModal}
          onConfirm={handleUserBlock}
          isLoading={isUserActionLoading}
        />
      </div>
    </AdminLayout>
  );
}
