import React from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import { useQuery } from '@tanstack/react-query';
import { adminApi, debugAdminStatus } from '../../lib/adminApi';
import {
  Users,
  Calendar,
  Settings,
  TrendingUp,
  Activity,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  BarChart3,
  FileText
} from 'lucide-react';
import { Link } from 'wouter';
import { formatCurrency } from '../../utils/formatters';

export default function AdminDashboard() {
  const adminAuth = useAdminAuth();

  // Fetch system statistics
  const { data: stats, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['admin', 'stats'],
    queryFn: adminApi.stats.getSystemStats,
    enabled: adminAuth.isAdmin,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch recent system logs
  const { data: recentLogs, isLoading: logsLoading } = useQuery({
    queryKey: ['admin', 'logs', 'recent'],
    queryFn: () => adminApi.logs.getAll(5, 0),
    enabled: adminAuth.canViewLogs(),
    refetchInterval: 60000 // Refresh every minute
  });

  // Fetch recent user activities
  const { data: recentActivities, isLoading: activitiesLoading } = useQuery({
    queryKey: ['admin', 'activities', 'recent'],
    queryFn: () => adminApi.userActivities.getAll(5, 0),
    enabled: adminAuth.isAdmin,
    refetchInterval: 60000
  });

  const quickActions = [
    {
      title: 'Kullanıcı Yönetimi',
      description: 'Kullanıcıları görüntüle ve yönet',
      href: '/admin/users',
      icon: Users,
      color: 'bg-blue-500',
      visible: adminAuth.canManageUsers()
    },
    {
      title: 'Sistem Ayarları',
      description: 'Sistem konfigürasyonunu düzenle',
      href: '/admin/settings',
      icon: Settings,
      color: 'bg-green-500',
      visible: adminAuth.canManageSettings()
    },
    {
      title: 'Sistem Logları',
      description: 'Sistem aktivitelerini incele',
      href: '/admin/logs',
      icon: FileText,
      color: 'bg-orange-500',
      visible: adminAuth.canViewLogs()
    },
    {
      title: 'Raporlar',
      description: 'Detaylı raporları görüntüle',
      href: '/admin/reports',
      icon: BarChart3,
      color: 'bg-purple-500',
      visible: adminAuth.canViewStats()
    }
  ].filter(action => action.visible);

  return (
    <AdminLayout
      title="Admin Dashboard"
      subtitle="Sistem genel durumu ve hızlı erişim"
    >
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-6 text-white shadow-xl">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">
                Hoş Geldiniz, {adminAuth.displayName}! 👋
              </h1>
              <Button
                variant="outline"
                size="sm"
                onClick={debugAdminStatus}
                className="mt-2 text-blue-600 border-white hover:bg-white/10"
              >
                🔍 Debug Admin Status
              </Button>
              <p className="text-blue-100">
                EventFlow Admin Panel - Sistem yönetimi ve izleme
              </p>
            </div>
            <div className="hidden sm:block">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                <Shield className="w-3 h-3 mr-1" />
                {adminAuth.role === 'super_admin' ? 'Süper Admin' : 
                 adminAuth.role === 'admin' ? 'Admin' : 'Moderatör'}
              </Badge>
            </div>
          </div>
        </div>

        {/* System Statistics */}
        {statsLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : statsError ? (
          <Card className="border-red-200">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
              <p className="text-red-600">İstatistikler yüklenirken hata oluştu</p>
            </CardContent>
          </Card>
        ) : stats ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-gradient-to-br from-white to-blue-50 border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Toplam Kullanıcı</CardTitle>
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">{stats.totalUsers}</div>
                <p className="text-sm text-gray-600 mt-1">
                  Bu ay +{stats.newUsersThisMonth} yeni
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-white to-green-50 border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Toplam Organizasyon</CardTitle>
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">{stats.totalOrganizations}</div>
                <p className="text-sm text-gray-600 mt-1">
                  Bu ay +{stats.organizationsThisMonth} yeni
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-white to-purple-50 border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Toplam Gelir</CardTitle>
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(stats.totalRevenue)}
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Bu ay {formatCurrency(stats.revenueThisMonth)}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-white to-orange-50 border-0 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Aktif Kullanıcı</CardTitle>
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Activity className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">{stats.activeUsers}</div>
                <p className="text-sm text-gray-600 mt-1">
                  Son 30 gün içinde
                </p>
              </CardContent>
            </Card>
          </div>
        ) : null}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Hızlı Eylemler
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {quickActions.map((action) => (
                <Link key={action.href} href={action.href}>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start h-auto p-4 hover:bg-gray-50"
                  >
                    <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center mr-3`}>
                      <action.icon className="w-5 h-5 text-white" />
                    </div>
                    <div className="text-left">
                      <p className="font-medium">{action.title}</p>
                      <p className="text-sm text-gray-600">{action.description}</p>
                    </div>
                  </Button>
                </Link>
              ))}
            </CardContent>
          </Card>

          {/* Recent System Logs */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Son Sistem Aktiviteleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              {logsLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded-lg animate-pulse" />
                  ))}
                </div>
              ) : recentLogs && recentLogs.length > 0 ? (
                <div className="space-y-3">
                  {recentLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <div>
                          <p className="font-medium text-sm">{log.action}</p>
                          <p className="text-xs text-gray-600">
                            {log.user_name || log.user_email || 'Sistem'} • {new Date(log.created_at).toLocaleString('tr-TR')}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {log.resource_type || 'sistem'}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Henüz sistem aktivitesi bulunmuyor</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
