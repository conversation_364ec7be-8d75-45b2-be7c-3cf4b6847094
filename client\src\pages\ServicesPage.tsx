import { useState } from "react";
import { Layout } from "../components/layout/Layout";
import { ServiceForm } from "../components/forms/ServiceForm";
import { useServices } from "../hooks/useServices";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Search, Wand2, Package, CheckCircle, TrendingUp, AlertTriangle } from "lucide-react";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import { formatCurrency } from "../utils/formatters";
import { DEFAULT_SERVICES } from "../data/defaultServices";
import { useToast } from "../hooks/use-toast";
import type { Service } from "../types";

export default function ServicesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [deletingService, setDeletingService] = useState<Service | null>(null);
  const [isDefaultServicesOpen, setIsDefaultServicesOpen] = useState(false);
  const [isBulkDeleteOpen, setIsBulkDeleteOpen] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);
  const [selectedDefaultServices, setSelectedDefaultServices] = useState<string[]>([]);
  const { toast } = useToast();



  const { 
    services, 
    isLoading, 
    error, 
    createService, 
    updateService, 
    deleteService,
    isCreating,
    isUpdating,
    isDeleting
  } = useServices();

  const filteredServices = services?.filter(service =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleCreate = (data: any) => {
    createService(data, {
      onSuccess: () => {
        toast({
          title: "✅ Başarılı",
          description: "Hizmet başarıyla oluşturuldu",
          variant: "success",
        });
        setIsFormOpen(false);
      },
      onError: (error: any) => {
        toast({
          title: "❌ Hata",
          description: error?.message || "Hizmet oluşturulurken bir hata oluştu",
          variant: "destructive",
        });
      },
    });
  };

  const handleUpdate = (data: any) => {
    if (editingService) {
      updateService({ id: editingService.id, ...data }, {
        onSuccess: () => {
          toast({
            title: "✅ Başarılı",
            description: "Hizmet başarıyla güncellendi",
            variant: "success",
          });
          setEditingService(null);
          setIsFormOpen(false);
        },
        onError: (error: any) => {
          toast({
            title: "❌ Hata",
            description: error?.message || "Hizmet güncellenirken bir hata oluştu",
            variant: "destructive",
          });
        },
      });
    }
  };

  const handleDelete = () => {
    if (deletingService) {
      deleteService(deletingService.id, {
        onSuccess: () => {
          toast({
            title: "✅ Başarılı",
            description: "Hizmet başarıyla silindi",
            variant: "success",
          });
          setDeletingService(null);
        },
        onError: (error: any) => {
          toast({
            title: "❌ Hata",
            description: error?.message || "Hizmet silinirken bir hata oluştu",
            variant: "destructive",
          });
        },
      });
    }
  };

  const openEditForm = (service: Service) => {
    setEditingService(service);
    setIsFormOpen(true);
  };

  const openCreateForm = () => {
    setEditingService(null);
    setIsFormOpen(true);
  };

  const handleBulkDelete = async () => {
    if (!services || services.length === 0) return;

    setIsBulkDeleting(true);
    let deletedCount = 0;
    let errorCount = 0;

    // Tüm hizmetleri sırayla sil
    for (const service of services) {
      try {
        await new Promise((resolve, reject) => {
          deleteService(service.id, {
            onSuccess: () => {
              deletedCount++;
              resolve(true);
            },
            onError: (error: any) => {
              errorCount++;
              reject(error);
            },
          });
        });
      } catch (error) {
        console.error(`Hizmet silme hatası: ${service.name}`, error);
      }
    }

    setIsBulkDeleting(false);
    setIsBulkDeleteOpen(false);

    // Sonuç bildirimi
    if (errorCount === 0) {
      toast({
        title: "✅ Başarılı",
        description: `${deletedCount} hizmet başarıyla silindi`,
        variant: "success",
      });
    } else if (deletedCount > 0) {
      toast({
        title: "⚠️ Kısmi Başarı",
        description: `${deletedCount} hizmet silindi, ${errorCount} hizmette hata oluştu`,
        variant: "default",
      });
    } else {
      toast({
        title: "❌ Hata",
        description: "Hiçbir hizmet silinemedi",
        variant: "destructive",
      });
    }
  };

  // Varsayılan hizmetler için fonksiyonlar
  const toggleDefaultService = (serviceName: string) => {
    setSelectedDefaultServices(prev =>
      prev.includes(serviceName)
        ? prev.filter(name => name !== serviceName)
        : [...prev, serviceName]
    );
  };

  const selectAllServices = () => {
    const allServiceNames = DEFAULT_SERVICES.map(s => s.name);
    setSelectedDefaultServices(allServiceNames);
  };

  const deselectAllServices = () => {
    setSelectedDefaultServices([]);
  };

  const addSelectedDefaultServices = async () => {
    const servicesToAdd = DEFAULT_SERVICES.filter(service =>
      selectedDefaultServices.includes(service.name)
    );

    let successCount = 0;
    let errorCount = 0;

    for (const service of servicesToAdd) {
      try {
        createService({
          name: service.name,
          description: service.description,
          basePrice: service.basePrice,
          isActive: true
        }, {
          onSuccess: () => {
            successCount++;
          },
          onError: () => {
            errorCount++;
          }
        });
        // Kısa bir delay ekleyelim
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        errorCount++;
        console.error('Error adding service:', service.name, error);
      }
    }

    // Biraz bekleyelim ki tüm işlemler tamamlansın
    await new Promise(resolve => setTimeout(resolve, 500));

    if (successCount > 0) {
      toast({
        title: "✅ Başarılı",
        description: `${successCount} hizmet başarıyla eklendi`,
        variant: "success",
      });
    }

    if (errorCount > 0) {
      toast({
        title: "⚠️ Uyarı",
        description: `${errorCount} hizmet eklenirken hata oluştu`,
        variant: "destructive",
      });
    }

    setSelectedDefaultServices([]);
    setIsDefaultServicesOpen(false);
  };

  if (isLoading) {
    return (
      <Layout title="Hizmetler" subtitle="Hizmet yönetimi ve fiyatlandırma">
        <div className="flex justify-center items-center h-96">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title="Hizmetler" subtitle="Hizmet yönetimi ve fiyatlandırma">
        <div className="flex justify-center items-center h-96">
          <p className="text-red-500">Hizmetler yüklenirken hata oluştu</p>
        </div>
      </Layout>
    );
  }

  return (
    <>
      <Layout title="Hizmetler" subtitle="Hizmet yönetimi ve fiyatlandırma">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8 space-y-6 sm:space-y-8">
          {/* Header Section */}
          <div className="text-center space-y-3 sm:space-y-4">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg mb-2 sm:mb-4">
              <Package className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 px-2">Hizmet Yönetimi</h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto px-4">
              Organizasyonlarınız için sunduğunuz hizmetleri yönetin, fiyatlandırın ve düzenleyin
            </p>
            {/* Force rebuild comment */}
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Toplam Hizmet</p>
                  <p className="text-3xl font-bold text-gray-900">{services?.length || 0}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Package className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Aktif Hizmet</p>
                  <p className="text-3xl font-bold text-green-600">{services?.filter(s => s.isActive).length || 0}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ortalama Fiyat</p>
                  <p className="text-3xl font-bold text-purple-600">
                    {formatCurrency((services?.reduce((sum, s) => sum + s.basePrice, 0) || 0) / (services?.length || 1))}
                  </p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Actions Bar */}
          <div className="bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-lg border-0">
            <div className="flex flex-col gap-4 sm:gap-4">
              <div className="relative w-full">
                <Search className="absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5" />
                <Input
                  placeholder="Hizmet ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 sm:pl-12 h-10 sm:h-12 border-2 border-gray-200 focus:border-blue-500 rounded-lg sm:rounded-xl transition-all duration-200 text-base sm:text-lg"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full">
                <Button
                  variant="outline"
                  size="lg"
                  className="flex-1 lg:flex-none h-12 border-2 border-green-500 text-green-700 hover:bg-green-50 hover:border-green-600 rounded-xl transition-all duration-200"
                  onClick={() => setIsDefaultServicesOpen(true)}
                >
                  <Wand2 className="w-5 h-5 mr-2" />
                  Hızlı Hizmet Ekle
                </Button>

                {services && services.length > 0 && (
                  <Button
                    variant="outline"
                    size="lg"
                    className="flex-1 lg:flex-none h-12 border-2 border-red-500 text-red-700 hover:bg-red-50 hover:border-red-600 rounded-xl transition-all duration-200"
                    onClick={() => setIsBulkDeleteOpen(true)}
                    disabled={isBulkDeleting}
                  >
                    <Trash2 className="w-5 h-5 mr-2" />
                    {isBulkDeleting ? "Siliniyor..." : "Tümünü Temizle"}
                  </Button>
                )}

                <Button
                  size="lg"
                  onClick={openCreateForm}
                  className="flex-1 lg:flex-none h-12 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl rounded-xl transition-all duration-200"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Yeni Hizmet
                </Button>
              </div>
            </div>
          </div>

        {/* Services Grid */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <LoadingSpinner size="lg" />
            <div className="text-gray-600 mt-6 text-center">
              <p className="text-xl font-medium">Hizmetler yükleniyor...</p>
              <p className="text-sm mt-2">Lütfen bekleyiniz</p>
            </div>
          </div>
        ) : filteredServices.length === 0 ? (
          <div className="bg-white rounded-2xl p-12 shadow-lg border-0 text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Package className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              {searchTerm ? "Arama sonucu bulunamadı" : "Henüz hizmet bulunmuyor"}
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              {searchTerm
                ? "Arama kriterinize uygun hizmet bulunamadı. Farklı anahtar kelimeler deneyin."
                : "Hızlı başlamak için varsayılan hizmetleri ekleyebilir veya kendi hizmetinizi oluşturabilirsiniz"
              }
            </p>
            {!searchTerm && (
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={() => setIsDefaultServicesOpen(true)}
                  variant="outline"
                  size="lg"
                  className="border-2 border-green-500 text-green-700 hover:bg-green-50 rounded-xl"
                >
                  <Wand2 className="w-5 h-5 mr-2" />
                  Hızlı Hizmet Ekle
                </Button>
                <Button
                  onClick={openCreateForm}
                  size="lg"
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 rounded-xl"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Yeni Hizmet
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <div
                key={service.id}
                className="bg-white rounded-2xl p-6 shadow-lg border-0 hover:shadow-xl transition-all duration-300 group"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {service.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      {service.description}
                    </p>
                  </div>
                  <Badge
                    variant={service.isActive ? "default" : "secondary"}
                    className={`ml-3 ${service.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'}`}
                  >
                    {service.isActive ? "Aktif" : "Pasif"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(service.basePrice)}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditForm(service)}
                      className="h-9 w-9 p-0 hover:bg-blue-50 hover:text-blue-600 rounded-lg"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setDeletingService(service)}
                      disabled={isDeleting}
                      className="h-9 w-9 p-0 hover:bg-red-50 hover:text-red-600 rounded-lg"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        </div>
      </div>
    </Layout>

    {/* Varsayılan Hizmetler Dialog */}
        <Dialog open={isDefaultServicesOpen} onOpenChange={setIsDefaultServicesOpen}>
          <DialogContent className="max-w-4xl w-[95vw] h-[90vh] sm:h-[85vh] flex flex-col p-0">
            <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
              <DialogTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Hızlı Hizmet Ekleme
              </DialogTitle>
            </DialogHeader>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-hidden flex flex-col px-6">
              <div className="space-y-4 py-4">
                {/* Toplu Seçim Butonları */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={selectAllServices}
                  >
                    Tümünü Seç ({DEFAULT_SERVICES.length})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={deselectAllServices}
                  >
                    Seçimi Temizle
                  </Button>
                  {selectedDefaultServices.length > 0 && (
                    <Badge variant="secondary">
                      {selectedDefaultServices.length} hizmet seçildi
                    </Badge>
                  )}
                </div>
              </div>

              {/* Hizmetler Listesi - Scrollable */}
              <div className="flex-1 overflow-y-auto border rounded-lg mb-4">
                <div className="grid gap-2 p-4">
                  {DEFAULT_SERVICES.map((service) => (
                    <div
                      key={service.name}
                      className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <Checkbox
                        checked={selectedDefaultServices.includes(service.name)}
                        onCheckedChange={() => toggleDefaultService(service.name)}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">{service.name}</h4>
                          <div className="flex items-center gap-2">
                            <span className="font-bold text-green-600 text-xs sm:text-sm">
                              {formatCurrency(service.basePrice)}
                              {service.unit && <span className="text-xs text-gray-500">/{service.unit}</span>}
                            </span>
                          </div>
                        </div>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{service.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Fixed Footer */}
            <div className="flex-shrink-0 px-6 py-4 border-t bg-white">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                <div className="text-sm text-gray-600">
                  {selectedDefaultServices.length > 0 && (
                    <span>
                      {selectedDefaultServices.length} hizmet seçildi
                    </span>
                  )}
                </div>
                <div className="flex gap-2 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedDefaultServices([]);
                      setIsDefaultServicesOpen(false);
                    }}
                    className="flex-1 sm:flex-none"
                  >
                    İptal
                  </Button>
                  <Button
                    onClick={addSelectedDefaultServices}
                    disabled={selectedDefaultServices.length === 0 || isCreating}
                    className="bg-green-600 hover:bg-green-700 flex-1 sm:flex-none"
                  >
                    {isCreating ? "Ekleniyor..." : `${selectedDefaultServices.length} Hizmeti Ekle`}
                  </Button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Service Form Modal */}
        <ServiceForm
          service={editingService || undefined}
          open={isFormOpen}
          onOpenChange={setIsFormOpen}
          onSubmit={editingService ? handleUpdate : handleCreate}
          isSubmitting={isCreating || isUpdating}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!deletingService} onOpenChange={() => setDeletingService(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Hizmeti Sil</AlertDialogTitle>
              <AlertDialogDescription>
                "{deletingService?.name}" hizmetini silmek istediğinizden emin misiniz? 
                Bu işlem geri alınamaz.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isDeleting ? "Siliniyor..." : "Sil"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Bulk Delete Confirmation Dialog */}
        <AlertDialog open={isBulkDeleteOpen} onOpenChange={() => setIsBulkDeleteOpen(false)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                Tüm Hizmetleri Sil
              </AlertDialogTitle>
              <AlertDialogDescription className="space-y-3">
                <p className="text-gray-700">
                  <strong>{services?.length || 0} adet hizmeti</strong> silmek istediğinizden emin misiniz?
                </p>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-800 text-sm font-medium">⚠️ Bu işlem geri alınamaz!</p>
                  <p className="text-red-700 text-sm mt-1">
                    Tüm hizmet bilgileri kalıcı olarak silinecek ve bu hizmetleri kullanan organizasyonlar etkilenebilir.
                  </p>
                </div>
                <p className="text-gray-600 text-sm">
                  Devam etmek için aşağıdaki "Tümünü Sil" butonuna tıklayın.
                </p>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isBulkDeleting}>İptal</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleBulkDelete}
                disabled={isBulkDeleting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isBulkDeleting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Siliniyor...
                  </div>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Tümünü Sil ({services?.length || 0})
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }
