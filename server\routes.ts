import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertServiceSchema, 
  createOrganizationSchema,
  type InsertService,
  type CreateOrganization 
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Services routes
  app.get("/api/services", async (req, res) => {
    try {
      const services = await storage.getServices();
      res.json(services);
    } catch (error) {
      res.status(500).json({ message: "Hizmetler yüklenirken hata oluştu" });
    }
  });

  app.post("/api/services", async (req, res) => {
    try {
      const validatedData = insertServiceSchema.parse(req.body);
      const service = await storage.createService(validatedData);
      res.status(201).json(service);
    } catch (error) {
      res.status(400).json({ message: "Geçersiz hizmet verisi" });
    }
  });

  app.put("/api/services/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const validatedData = insertServiceSchema.partial().parse(req.body);
      const service = await storage.updateService(id, validatedData);
      
      if (!service) {
        return res.status(404).json({ message: "Hizmet bulunamadı" });
      }
      
      res.json(service);
    } catch (error) {
      res.status(400).json({ message: "Geçersiz hizmet verisi" });
    }
  });

  app.delete("/api/services/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = await storage.deleteService(id);
      
      if (!deleted) {
        return res.status(404).json({ message: "Hizmet bulunamadı" });
      }
      
      res.json({ message: "Hizmet başarıyla silindi" });
    } catch (error) {
      res.status(500).json({ message: "Hizmet silinirken hata oluştu" });
    }
  });

  // Organizations routes
  app.get("/api/organizations", async (req, res) => {
    try {
      const organizations = await storage.getOrganizations();
      res.json(organizations);
    } catch (error) {
      res.status(500).json({ message: "Organizasyonlar yüklenirken hata oluştu" });
    }
  });

  app.get("/api/organizations/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const organization = await storage.getOrganization(id);
      
      if (!organization) {
        return res.status(404).json({ message: "Organizasyon bulunamadı" });
      }
      
      res.json(organization);
    } catch (error) {
      res.status(500).json({ message: "Organizasyon yüklenirken hata oluştu" });
    }
  });

  app.post("/api/organizations", async (req, res) => {
    try {
      const validatedData = createOrganizationSchema.parse(req.body);
      const organization = await storage.createOrganization(validatedData);
      res.status(201).json(organization);
    } catch (error) {
      console.error("Organization creation error:", error);
      res.status(400).json({ message: "Geçersiz organizasyon verisi" });
    }
  });

  app.patch("/api/organizations/:id/status", async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      if (!["Teklif", "Kesinleşti", "İptal"].includes(status)) {
        return res.status(400).json({ message: "Geçersiz durum" });
      }
      
      const updated = await storage.updateOrganizationStatus(id, status);
      
      if (!updated) {
        return res.status(404).json({ message: "Organizasyon bulunamadı" });
      }
      
      res.json({ message: "Durum başarıyla güncellendi" });
    } catch (error) {
      res.status(500).json({ message: "Durum güncellenirken hata oluştu" });
    }
  });

  // Calendar routes
  app.get("/api/calendar", async (req, res) => {
    try {
      const events = await storage.getCalendarEvents();
      res.json(events);
    } catch (error) {
      res.status(500).json({ message: "Ajanda verisi yüklenirken hata oluştu" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
