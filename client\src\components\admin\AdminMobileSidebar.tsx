import React from 'react';
import { Link, useLocation } from 'wouter';
import { cn } from '@/lib/utils';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import {
  LayoutDashboard,
  Users,
  BarChart3,
  Settings,
  FileText,
  Activity,
  Shield,
  Home,
  Database,
  Bell,
  LogOut,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useAuth } from '../../contexts/AuthContext';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  requiredRole?: 'super_admin' | 'admin' | 'moderator';
  requiredPermission?: string;
}

const navigation: NavItem[] = [
  {
    title: 'Admin Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi',
    href: '/admin/users',
    icon: Users,
    requiredRole: 'super_admin'
  },
  {
    title: 'Sistem İstatistikleri',
    href: '/admin/stats',
    icon: BarChart3,
  },
  {
    title: 'Sistem Ayarları',
    href: '/admin/settings',
    icon: Settings,
    requiredRole: 'super_admin'
  },
  {
    title: 'Sistem Logları',
    href: '/admin/logs',
    icon: FileText,
  },
  {
    title: 'Kullanıcı Aktiviteleri',
    href: '/admin/activities',
    icon: Activity,
  },
  {
    title: 'Admin Yönetimi',
    href: '/admin/admins',
    icon: Shield,
    requiredRole: 'super_admin'
  },
  {
    title: 'Bildirimler',
    href: '/admin/notifications',
    icon: Bell
  },
  {
    title: 'Raporlar',
    href: '/admin/reports',
    icon: Database,
  }
];

interface AdminMobileSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AdminMobileSidebar: React.FC<AdminMobileSidebarProps> = ({
  open,
  onOpenChange
}) => {
  const [location] = useLocation();
  const adminAuth = useAdminAuth();
  const { signOut } = useAuth();

  const filteredNavigation = navigation.filter(item => {
    if (item.requiredRole && !adminAuth.hasRole(item.requiredRole)) {
      return false;
    }
    if (item.requiredPermission && !adminAuth.hasPermission(item.requiredPermission)) {
      return false;
    }
    return true;
  });

  const handleSignOut = async () => {
    try {
      await signOut();
      onOpenChange(false);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleNavClick = () => {
    onOpenChange(false);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="left" className="w-64 p-0">
        <div className="flex flex-col h-full bg-white dark:bg-slate-900">
          {/* Header */}
          <SheetHeader className="flex flex-row items-center justify-between h-16 px-4 border-b border-slate-200 dark:border-slate-700">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <SheetTitle className="text-lg font-bold text-slate-900 dark:text-white">
                Admin Panel
              </SheetTitle>
            </div>
          </SheetHeader>

          {/* Admin Info */}
          <div className="p-4 border-b border-slate-200 dark:border-slate-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {adminAuth.displayName.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                  {adminAuth.displayName}
                </p>
                <div className="flex items-center space-x-1">
                  <Badge 
                    variant={adminAuth.role === 'super_admin' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {adminAuth.role === 'super_admin' ? 'Süper Admin' : 
                     adminAuth.role === 'admin' ? 'Admin' : 'Moderatör'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {filteredNavigation.map((item) => {
              const isActive = location === item.href || 
                (item.href !== '/admin' && location.startsWith(item.href));
              
              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    className={cn(
                      "w-full justify-start h-10 px-3",
                      isActive 
                        ? "bg-blue-600 text-white hover:bg-blue-700" 
                        : "text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800"
                    )}
                    onClick={handleNavClick}
                  >
                    <item.icon className="w-4 h-4 mr-3" />
                    <span className="flex-1 text-left">{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Button>
                </Link>
              );
            })}
          </nav>

          <Separator className="mx-4" />

          {/* Bottom Actions */}
          <div className="p-4 space-y-2">
            <Link href="/">
              <Button 
                variant="outline" 
                className="w-full justify-start h-10 px-3"
                onClick={handleNavClick}
              >
                <Home className="w-4 h-4 mr-3" />
                Ana Uygulamaya Dön
              </Button>
            </Link>
            
            <Button 
              variant="ghost" 
              className="w-full justify-start h-10 px-3 text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={handleSignOut}
            >
              <LogOut className="w-4 h-4 mr-3" />
              Çıkış Yap
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
