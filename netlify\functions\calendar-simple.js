// Simple calendar function without Supabase for testing
// Supabase configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json',
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod === 'GET') {
    try {
      // Fetch organizations with customer info for calendar events
      const response = await fetch(`${SUPABASE_URL}/rest/v1/eventflow_organizations?select=*,eventflow_customers(name)&order=event_date.asc`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Supabase error: ${response.status}`);
      }

      const supabaseOrganizations = await response.json();

      // Convert organizations to calendar events
      const events = supabaseOrganizations.map(org => {
        const eventDate = new Date(org.event_date);
        const endDate = new Date(eventDate.getTime() + 4 * 60 * 60 * 1000); // 4 hours default duration

        return {
          id: org.id,
          title: `${org.eventflow_customers?.name || 'Bilinmeyen'} - ${org.status}`,
          start: eventDate,
          end: endDate,
          organizationId: org.id,
          status: org.status
        };
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(events),
      };
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: 'Takvim etkinlikleri yüklenirken hata oluştu' }),
      };
    }
  }

  return {
    statusCode: 405,
    headers,
    body: JSON.stringify({ message: 'Method not allowed' }),
  };
};
