import React, { useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { AuthPage } from '../../pages/AuthPage'
import { EmergencyAuth } from './EmergencyAuth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth()
  const [forceShow, setForceShow] = React.useState(false)
  const [emergencyMode, setEmergencyMode] = React.useState(false)
  const [renderKey, setRenderKey] = React.useState(0)

  // Force re-render when user state changes (mobil fix)
  useEffect(() => {
    if (user && !loading) {
      setRenderKey(prev => prev + 1)
      setForceShow(false)
      setEmergencyMode(false)
    }
  }, [user, loading])

  // Session persistence için daha uzun timeout - 5 saniye sonra zorla <PERSON>, 15 saniye sonra emergency
  React.useEffect(() => {
    console.log('🛡️ ProtectedRoute - loading:', loading, 'user:', !!user)

    if (loading) {
      const timeout1 = setTimeout(() => {
        console.warn('⏰ Loading timeout - forcing show')
        setForceShow(true)
      }, 5000) // 5 saniye timeout - session restore için daha fazla süre

      const timeout2 = setTimeout(() => {
        console.error('🚨 Emergency timeout - showing emergency page')
        setEmergencyMode(true)
      }, 15000) // 15 saniye emergency timeout - daha uzun süre

      return () => {
        clearTimeout(timeout1)
        clearTimeout(timeout2)
      }
    } else {
      // Loading false olduğunda timeout'ları temizle
      setForceShow(false)
      setEmergencyMode(false)
    }
  }, [loading])

  // Emergency mode
  if (emergencyMode) {
    return <EmergencyAuth />
  }

  // Eğer 3 saniye geçtiyse ve hala loading'se, auth sayfasını göster
  if (forceShow && loading) {
    console.warn('🚨 Force showing auth page due to timeout')
    return <AuthPage />
  }

  if (loading && !forceShow) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-700 text-lg font-medium">Oturum kontrol ediliyor...</p>
          <p className="text-gray-500 text-sm mt-2">Lütfen bekleyiniz</p>
        </div>
      </div>
    )
  }

  if (!user) {
    console.log('👤 No user, showing auth page')
    return <AuthPage />
  }

  console.log('✅ User authenticated, showing app')
  return <div key={renderKey}>{children}</div>
}
