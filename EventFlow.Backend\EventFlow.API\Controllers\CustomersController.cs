using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EventFlow.Infrastructure.Services;
using EventFlow.Shared.DTOs;

namespace EventFlow.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class CustomersController : ControllerBase
{
    private readonly SupabaseService _supabaseService;
    private readonly ILogger<CustomersController> _logger;

    public CustomersController(SupabaseService supabaseService, ILogger<CustomersController> logger)
    {
        _supabaseService = supabaseService;
        _logger = logger;
    }

    /// <summary>
    /// Get all customers
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<CustomerDto>>> GetCustomers()
    {
        try
        {
            var customers = await _supabaseService.GetCustomersAsync();
            return Ok(customers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customers");
            return StatusCode(500, new { message = "Müşteriler yüklenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Get customer by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<CustomerDto>> GetCustomer(Guid id)
    {
        try
        {
            var customer = new CustomerDto
            {
                Id = id,
                Name = "Test Müşteri",
                Email = "<EMAIL>",
                Phone = "+90 ************",
                CreatedAt = DateTime.Now.AddDays(-10),
                UpdatedAt = DateTime.Now
            };

            return Ok(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer {CustomerId}", id);
            return StatusCode(500, new { message = "Müşteri yüklenirken hata oluştu" });
        }
    }

    /// <summary>
    /// Create a new customer
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<CustomerDto>> CreateCustomer(CreateCustomerDto createCustomerDto)
    {
        try
        {
            // Email alanı boş ise null olarak ayarla
            string emailValue = string.IsNullOrWhiteSpace(createCustomerDto.Email)
                ? null
                : createCustomerDto.Email;

            var customer = new CustomerDto
            {
                Id = Guid.NewGuid(),
                Name = createCustomerDto.FullName,
                Email = emailValue,
                Phone = createCustomerDto.PhoneNumber,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            return CreatedAtAction(nameof(GetCustomer), new { id = customer.Id }, customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer");

            // Daha açıklayıcı hata mesajları
            if (ex.Message.Contains("chk_email_format"))
            {
                return BadRequest(new { message = "Geçerli bir e-posta adresi giriniz veya boş bırakınız" });
            }

            return StatusCode(500, new { message = "Müşteri oluşturulurken hata oluştu" });
        }
    }
}
