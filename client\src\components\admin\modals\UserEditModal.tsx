import React, { useEffect } from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AdminUser } from "../../../types";
import { Edit, User, Mail, Building, Phone } from "lucide-react";

const editUserSchema = z.object({
  first_name: z.string().min(1, "Ad gereklidir").max(50, "Ad çok uzun"),
  last_name: z.string().min(1, "Soyad gereklidir").max(50, "Soyad çok uzun"),
  phone: z.string().optional(),
  company_name: z.string().optional(),
});

type EditUserFormData = z.infer<typeof editUserSchema>;

interface UserEditModalProps {
  user: AdminUser | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: EditUserFormData) => void;
  isLoading?: boolean;
}

export function UserEditModal({
  user,
  open,
  onOpenChange,
  onSave,
  isLoading = false,
}: UserEditModalProps) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<EditUserFormData>({
    resolver: zodResolver(editUserSchema),
  });

  useEffect(() => {
    if (user) {
      reset({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        phone: user.phone || "",
        company_name: user.company_name || "",
      });
    }
  }, [user, reset]);

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  const onSubmit = (data: EditUserFormData) => {
    onSave(data);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Kullanıcı Düzenle
          </DialogTitle>
          <DialogDescription>
            {user.first_name && user.last_name 
              ? `${user.first_name} ${user.last_name}` 
              : user.email
            } kullanıcısının bilgilerini güncelleyin
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Kişisel Bilgiler */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <User className="w-5 h-5" />
              Kişisel Bilgiler
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="first_name" className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Ad *
                </Label>
                <Input
                  id="first_name"
                  {...register("first_name")}
                  placeholder="Kullanıcının adı"
                  className={errors.first_name ? "border-red-500" : ""}
                />
                {errors.first_name && (
                  <p className="text-sm text-red-500">{errors.first_name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="last_name" className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Soyad *
                </Label>
                <Input
                  id="last_name"
                  {...register("last_name")}
                  placeholder="Kullanıcının soyadı"
                  className={errors.last_name ? "border-red-500" : ""}
                />
                {errors.last_name && (
                  <p className="text-sm text-red-500">{errors.last_name.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* İletişim Bilgileri */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Mail className="w-5 h-5" />
              İletişim Bilgileri
            </h3>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  Telefon
                </Label>
                <Input
                  id="phone"
                  {...register("phone")}
                  placeholder="+90 555 123 45 67"
                />
              </div>
            </div>
          </div>

          {/* Şirket Bilgileri */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg flex items-center gap-2">
              <Building className="w-5 h-5" />
              Şirket Bilgileri
            </h3>
            
            <div className="space-y-2">
              <Label htmlFor="company_name" className="flex items-center gap-2">
                <Building className="w-4 h-4" />
                Şirket Adı
              </Label>
              <Input
                id="company_name"
                {...register("company_name")}
                placeholder="Şirket adı (isteğe bağlı)"
              />
            </div>
          </div>

          {/* Kullanıcı ID (Sadece görüntüleme) */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <Label className="text-sm text-gray-600">Kullanıcı ID</Label>
            <p className="text-sm font-mono text-gray-800">{user.id}</p>
          </div>

          <DialogFooter className="gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleClose}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || !isDirty}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Edit className="w-4 h-4 mr-2" />
                  Değişiklikleri Kaydet
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
