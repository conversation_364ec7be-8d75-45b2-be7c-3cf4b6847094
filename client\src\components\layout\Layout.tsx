import { useState, useEffect } from "react";
import { Sidebar } from "./Sidebar";
import { Header } from "./Header";
import { MobileSidebar } from "./MobileSidebar";
import { useAuth } from "../../contexts/AuthContext";
import { useIsMobile } from "../../hooks/use-mobile";
import { cn } from "@/lib/utils";

interface LayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

export function Layout({ children, title, subtitle }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, loading } = useAuth();
  const isMobile = useIsMobile();

  // Force re-render when auth state changes (mobil fix)
  useEffect(() => {
    if (user && !loading) {
      // Auth state değiştiğinde component'i yeniden render et
      setSidebarOpen(false);
    }
  }, [user, loading]);

  return (
    <div className={cn(
      "flex bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800",
      isMobile ? "h-full min-h-screen" : "h-screen"
    )}>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex">
        <Sidebar />
      </div>

      {/* Mobile Sidebar */}
      <MobileSidebar open={sidebarOpen} onOpenChange={setSidebarOpen} />

      <div className={cn(
        "flex-1 flex flex-col",
        isMobile ? "min-h-screen" : "overflow-hidden"
      )}>
        <Header
          title={title}
          subtitle={subtitle}
          onMenuClick={() => setSidebarOpen(true)}
        />
        <main className={cn(
          "flex-1 p-4 sm:p-6",
          isMobile
            ? "main-content-mobile-padding"
            : "overflow-y-auto"
        )}>
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
