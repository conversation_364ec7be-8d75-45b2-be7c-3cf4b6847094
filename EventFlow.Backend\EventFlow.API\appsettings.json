{"ConnectionStrings": {"DefaultConnection": "Host=db.yqnhitvatsnrjdabsgzv.supabase.co;Port=6543;Database=postgres;Username=postgres;Password=**********;SSL Mode=Require;Trust Server Certificate=true;Pooling=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "Supabase": {"JwtSecret": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc"}, "Cors": {"AllowedOrigins": ["http://localhost:5000", "http://localhost:3000", "https://localhost:5001", "https://organizasyon.netlify.app", "https://bespoke-zabaione-dd3853.netlify.app"]}}