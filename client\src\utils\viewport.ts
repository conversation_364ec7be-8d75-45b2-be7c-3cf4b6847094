/**
 * Viewport utilities for mobile optimization
 * Handles dynamic viewport height calculation and mobile-specific fixes
 */

/**
 * Sets CSS custom properties for accurate viewport dimensions
 * Particularly useful for iOS Safari where 100vh doesn't work as expected
 */
export function setViewportProperties() {
  // Calculate the actual viewport height
  const vh = window.innerHeight * 0.01;
  const vw = window.innerWidth * 0.01;
  
  // Set CSS custom properties
  document.documentElement.style.setProperty('--vh', `${vh}px`);
  document.documentElement.style.setProperty('--vw', `${vw}px`);
}

/**
 * Initialize viewport handling with event listeners
 * Should be called once when the app starts
 */
export function initializeViewport() {
  // Set initial values
  setViewportProperties();
  
  // Update on resize
  window.addEventListener('resize', setViewportProperties);
  
  // Update on orientation change (mobile)
  window.addEventListener('orientationchange', () => {
    // Delay to allow orientation to complete
    setTimeout(setViewportProperties, 100);
  });
  
  // Update when page becomes visible (handles iOS Safari address bar)
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      setTimeout(setViewportProperties, 100);
    }
  });
  
  // Handle iOS Safari specific events
  if (typeof window !== 'undefined' && 'visualViewport' in window) {
    window.visualViewport?.addEventListener('resize', setViewportProperties);
  }
}

/**
 * Cleanup viewport event listeners
 */
export function cleanupViewport() {
  window.removeEventListener('resize', setViewportProperties);
  window.removeEventListener('orientationchange', setViewportProperties);
  document.removeEventListener('visibilitychange', setViewportProperties);
  
  if (typeof window !== 'undefined' && 'visualViewport' in window) {
    window.visualViewport?.removeEventListener('resize', setViewportProperties);
  }
}

/**
 * Check if device is mobile based on user agent and screen size
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUA = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  const isSmallScreen = window.innerWidth <= 768;
  
  return isMobileUA || isSmallScreen;
}

/**
 * Check if device is iOS
 */
export function isIOSDevice(): boolean {
  if (typeof window === 'undefined') return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * Prevent scroll chaining and improve mobile scroll behavior
 */
export function optimizeMobileScroll() {
  if (!isMobileDevice()) return;
  
  // Prevent overscroll bounce on body
  document.body.style.overscrollBehavior = 'none';
  
  // Improve momentum scrolling on iOS
  if (isIOSDevice()) {
    document.body.style.webkitOverflowScrolling = 'touch';
  }
  
  // Prevent horizontal scroll
  document.body.style.overflowX = 'hidden';
}
