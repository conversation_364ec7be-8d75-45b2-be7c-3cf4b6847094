import { supabase } from './supabase';
import { sendOrganizationNotification } from './telegramApi';

export interface ActivityData {
  activity_type: string;
  description: string;
  metadata?: Record<string, any>;
}

/**
 * Log user activity to the database with profile check
 */
export async function logUserActivity(activityData: ActivityData): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.warn('No authenticated user found for activity logging');
      return;
    }

    // First check if user has a profile (required for foreign key constraint)
    const { data: profile, error: profileError } = await supabase
      .from('eventflow_profiles')
      .select('id')
      .eq('id', user.id)
      .maybeSingle();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error checking user profile for activity logging:', profileError);
      return;
    }

    if (!profile) {
      console.warn('User profile not found, skipping activity logging. Profile must be created first.');
      return;
    }

    const { error } = await supabase
      .from('user_activities')
      .insert({
        user_id: user.id,
        activity_type: activityData.activity_type,
        description: activityData.description,
        metadata: activityData.metadata || {},
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to log user activity:', error);

      // If it's a foreign key constraint error, provide more helpful message
      if (error.code === '23503') {
        console.error('Foreign key constraint error: User profile may not exist. Activity logging skipped.');
      }
    } else {
      console.log('✅ User activity logged:', activityData.activity_type);
    }
  } catch (error) {
    console.error('Error logging user activity:', error);
  }
}

/**
 * Create system notification for admin panel and send to Telegram if enabled
 */
export async function createSystemNotification(
  title: string,
  message: string,
  type: 'info' | 'warning' | 'error' | 'success' = 'info'
): Promise<void> {
  try {
    // Create admin panel notification
    const { error } = await supabase
      .from('admin_notifications')
      .insert({
        title,
        message,
        type,
        is_read: false,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to create system notification:', error);
    } else {
      console.log('✅ System notification created:', title);
    }

    // Send to Telegram if enabled (don't wait for it to complete)
    sendOrganizationNotification(title, message, type).catch(error => {
      console.error('Failed to send Telegram notification:', error);
    });

  } catch (error) {
    console.error('Error creating system notification:', error);
  }
}

/**
 * Common activity types for easy usage
 */
export const ActivityTypes = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTER: 'register',
  PASSWORD_CHANGE: 'password_change',
  
  // Profile
  PROFILE_UPDATE: 'profile_update',
  PROFILE_VIEW: 'profile_view',
  
  // Organizations
  CREATE_ORGANIZATION: 'create_organization',
  UPDATE_ORGANIZATION: 'update_organization',
  DELETE_ORGANIZATION: 'delete_organization',
  VIEW_ORGANIZATION: 'view_organization',
  
  // Services
  CREATE_SERVICE: 'create_service',
  UPDATE_SERVICE: 'update_service',
  DELETE_SERVICE: 'delete_service',
  VIEW_SERVICE: 'view_service',
  
  // Customers
  CREATE_CUSTOMER: 'create_customer',
  UPDATE_CUSTOMER: 'update_customer',
  DELETE_CUSTOMER: 'delete_customer',
  VIEW_CUSTOMER: 'view_customer',
  
  // General
  PAGE_VIEW: 'page_view',
  SEARCH: 'search',
  EXPORT: 'export',
  IMPORT: 'import'
} as const;

/**
 * Helper functions for common activities
 */
export const ActivityLogger = {
  // Authentication activities
  login: () => logUserActivity({
    activity_type: ActivityTypes.LOGIN,
    description: 'Kullanıcı sisteme giriş yaptı'
  }),
  
  logout: () => logUserActivity({
    activity_type: ActivityTypes.LOGOUT,
    description: 'Kullanıcı sistemden çıkış yaptı'
  }),
  
  register: async (userInfo?: { email?: string; firstName?: string; lastName?: string; companyName?: string }) => {
    await logUserActivity({
      activity_type: ActivityTypes.REGISTER,
      description: 'Kullanıcı sisteme kayıt oldu'
    });

    // Create detailed admin notification for new user registration
    let notificationMessage = 'Sisteme yeni bir kullanıcı kaydoldu';

    if (userInfo) {
      const details = [];
      if (userInfo.email) details.push(`📧 Email: ${userInfo.email}`);
      if (userInfo.firstName && userInfo.lastName) {
        details.push(`👤 Ad Soyad: ${userInfo.firstName} ${userInfo.lastName}`);
      }
      if (userInfo.companyName) details.push(`🏢 Şirket: ${userInfo.companyName}`);

      if (details.length > 0) {
        notificationMessage = `Sisteme yeni bir kullanıcı kaydoldu:\n\n${details.join('\n')}`;
      }
    }

    await createSystemNotification(
      'Yeni Kullanıcı Kaydı',
      notificationMessage,
      'info'
    );
  },

  changePassword: async () => {
    await logUserActivity({
      activity_type: ActivityTypes.PASSWORD_CHANGE,
      description: 'Kullanıcı şifresini değiştirdi'
    });

    // Create admin notification for password change
    await createSystemNotification(
      'Şifre Değişikliği',
      'Bir kullanıcı şifresini değiştirdi',
      'warning'
    );
  },
  
  // Profile activities
  updateProfile: (changes: Record<string, any>) => logUserActivity({
    activity_type: ActivityTypes.PROFILE_UPDATE,
    description: 'Kullanıcı profil bilgilerini güncelledi',
    metadata: { changes }
  }),
  
  // Organization activities
  createOrganization: async (organizationName: string) => {
    await logUserActivity({
      activity_type: ActivityTypes.CREATE_ORGANIZATION,
      description: `Yeni organizasyon oluşturuldu: ${organizationName}`,
      metadata: { organization_name: organizationName }
    });

    // Create admin notification for high-value organization
    await createSystemNotification(
      'Yeni Organizasyon',
      `Yeni organizasyon oluşturuldu: ${organizationName}`,
      'success'
    );
  },
  
  updateOrganization: (organizationId: string, changes: Record<string, any>) => logUserActivity({
    activity_type: ActivityTypes.UPDATE_ORGANIZATION,
    description: 'Organizasyon güncellendi',
    metadata: { organization_id: organizationId, changes }
  }),
  
  deleteOrganization: async (organizationId: string) => {
    await logUserActivity({
      activity_type: ActivityTypes.DELETE_ORGANIZATION,
      description: 'Organizasyon silindi',
      metadata: { organization_id: organizationId }
    });

    // Create admin notification for organization deletion
    await createSystemNotification(
      'Organizasyon Silindi',
      'Bir organizasyon silindi',
      'warning'
    );
  },
  
  // Service activities
  createService: (serviceName: string) => logUserActivity({
    activity_type: ActivityTypes.CREATE_SERVICE,
    description: `Yeni hizmet oluşturuldu: ${serviceName}`,
    metadata: { service_name: serviceName }
  }),
  
  updateService: (serviceId: string, changes: Record<string, any>) => logUserActivity({
    activity_type: ActivityTypes.UPDATE_SERVICE,
    description: 'Hizmet güncellendi',
    metadata: { service_id: serviceId, changes }
  }),
  
  deleteService: (serviceId: string) => logUserActivity({
    activity_type: ActivityTypes.DELETE_SERVICE,
    description: 'Hizmet silindi',
    metadata: { service_id: serviceId }
  }),
  
  // Customer activities
  createCustomer: (customerName: string) => logUserActivity({
    activity_type: ActivityTypes.CREATE_CUSTOMER,
    description: `Yeni müşteri oluşturuldu: ${customerName}`,
    metadata: { customer_name: customerName }
  }),
  
  updateCustomer: (customerId: string, changes: Record<string, any>) => logUserActivity({
    activity_type: ActivityTypes.UPDATE_CUSTOMER,
    description: 'Müşteri bilgileri güncellendi',
    metadata: { customer_id: customerId, changes }
  }),
  
  deleteCustomer: (customerId: string) => logUserActivity({
    activity_type: ActivityTypes.DELETE_CUSTOMER,
    description: 'Müşteri silindi',
    metadata: { customer_id: customerId }
  }),
  
  // Page view activity
  pageView: (pageName: string) => logUserActivity({
    activity_type: ActivityTypes.PAGE_VIEW,
    description: `Sayfa görüntülendi: ${pageName}`,
    metadata: { page: pageName }
  })
};
