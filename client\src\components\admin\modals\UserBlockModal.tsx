import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AdminUser } from "../../../types";
import { Shield, Ban, Unlock, AlertTriangle } from "lucide-react";

interface UserBlockModalProps {
  user: AdminUser | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function UserBlockModal({
  user,
  open,
  onOpenChange,
  onConfirm,
  isLoading = false,
}: UserBlockModalProps) {
  if (!user) return null;

  const isBlocked = user.is_blocked;
  const action = isBlocked ? 'engelini kaldırma' : 'engelleme';
  const actionTitle = isBlocked ? 'En<PERSON><PERSON>' : '<PERSON>llanıcıyı Engelle';
  const actionIcon = isBlocked ? Unlock : Ban;
  const ActionIcon = actionIcon;
  
  const userName = user.first_name && user.last_name 
    ? `${user.first_name} ${user.last_name}` 
    : user.email;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className={`flex items-center gap-2 ${isBlocked ? 'text-green-600' : 'text-orange-600'}`}>
            <ActionIcon className="w-5 h-5" />
            {actionTitle}
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-2">
              <div>
                <strong>{userName}</strong> kullanıcısının {action} işlemini yapmak istediğinizden emin misiniz?
              </div>
            
            {isBlocked ? (
              <div className="bg-green-50 p-3 rounded-lg text-sm text-green-700">
                <div className="flex items-center gap-2 mb-2">
                  <Unlock className="w-4 h-4" />
                  <span className="font-medium">Engel kaldırıldığında:</span>
                </div>
                <ul className="list-disc list-inside space-y-1">
                  <li>Kullanıcı sisteme giriş yapabilecek</li>
                  <li>Tüm özellikleri kullanabilecek</li>
                  <li>Organizasyon oluşturabilecek</li>
                  <li>Normal kullanıcı haklarına sahip olacak</li>
                </ul>
              </div>
            ) : (
              <div className="bg-orange-50 p-3 rounded-lg text-sm text-orange-700">
                <div className="flex items-center gap-2 mb-2">
                  <Ban className="w-4 h-4" />
                  <span className="font-medium">Kullanıcı engellendiğinde:</span>
                </div>
                <ul className="list-disc list-inside space-y-1">
                  <li>Sisteme giriş yapamayacak</li>
                  <li>Mevcut oturumu sonlandırılacak</li>
                  <li>Hiçbir özelliği kullanamayacak</li>
                  <li>Veriler korunacak ancak erişilemeyecek</li>
                </ul>
              </div>
            )}

              {!isBlocked && (
                <div className="flex items-center gap-2 text-amber-600 mt-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">Bu işlem kullanıcının deneyimini etkileyecektir.</span>
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            İptal
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isLoading}
            className={isBlocked 
              ? "bg-green-600 hover:bg-green-700 focus:ring-green-600" 
              : "bg-orange-600 hover:bg-orange-700 focus:ring-orange-600"
            }
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                İşleniyor...
              </>
            ) : (
              <>
                <ActionIcon className="w-4 h-4 mr-2" />
                Evet, {isBlocked ? 'Engeli Kaldır' : 'Engelle'}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
