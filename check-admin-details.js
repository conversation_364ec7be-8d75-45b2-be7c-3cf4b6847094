// Script to check admin user details
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://yqnhitvatsnrjdabsgzv.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxbmhpdHZhdHNucmpkYWJzZ3p2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NjkyMTIsImV4cCI6MjA2ODQ0NTIxMn0.G68I35smsHLe2v7dr3uomUuApGtNBcBd5WMh3WHdCXc';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkAdminDetails() {
  try {
    console.log('🔍 Checking admin user details...');
    
    // Get admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*');
    
    if (adminError) {
      console.error('❌ Error fetching admin users:', adminError);
      return;
    }
    
    console.log('✅ Found admin users:', adminUsers?.length || 0);
    
    for (const admin of adminUsers || []) {
      console.log('\n--- Admin User ---');
      console.log('ID:', admin.id);
      console.log('User ID:', admin.user_id);
      console.log('Role:', admin.role);
      console.log('Active:', admin.is_active);
      console.log('Created:', admin.created_at);
      
      // Check if this user_id exists in eventflow_profiles
      const { data: profile, error: profileError } = await supabase
        .from('eventflow_profiles')
        .select('*')
        .eq('id', admin.user_id)
        .maybeSingle();
      
      if (profileError) {
        console.log('❌ Profile error:', profileError.message);
      } else if (profile) {
        console.log('✅ Profile found:', profile.first_name, profile.last_name);
      } else {
        console.log('❌ No profile found for this user_id');
      }
    }
    
    // Also check all profiles
    console.log('\n--- All Profiles ---');
    const { data: allProfiles, error: allProfilesError } = await supabase
      .from('eventflow_profiles')
      .select('*');
    
    if (allProfilesError) {
      console.error('❌ Error fetching profiles:', allProfilesError);
    } else {
      console.log('✅ Found profiles:', allProfiles?.length || 0);
      allProfiles?.forEach(profile => {
        console.log(`- ${profile.first_name} ${profile.last_name} (${profile.id})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkAdminDetails();
