import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Mail, ArrowRight, Shield, Clock } from 'lucide-react';
import { useLocation } from 'wouter';
import { supabase } from '../lib/supabase';

export default function EmailVerifiedSuccessPage() {
  const [, setLocation] = useLocation();
  const [countdown, setCountdown] = useState(5);
  const [verificationStatus, setVerificationStatus] = useState<'processing' | 'success' | 'error'>('processing');

  useEffect(() => {
    const processEmailVerification = async () => {
      try {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        
        const code = urlParams.get('code') || hashParams.get('code');
        const accessToken = urlParams.get('access_token') || hashParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token') || hashParams.get('refresh_token');
        
        console.log('📧 Processing email verification with code:', code);
        
        if (code || (accessToken && refreshToken)) {
          // Email verification successful
          setVerificationStatus('success');
          
          // Start countdown for redirect
          setTimeout(() => {
            const countdownTimer = setInterval(() => {
              setCountdown(prev => {
                if (prev <= 1) {
                  clearInterval(countdownTimer);
                  setLocation('/');
                  return 0;
                }
                return prev - 1;
              });
            }, 1000);
          }, 1000);
          
        } else {
          setVerificationStatus('error');
        }
        
      } catch (error) {
        console.error('❌ Email verification error:', error);
        setVerificationStatus('error');
      }
    };

    processEmailVerification();
  }, [setLocation]);

  const handleManualRedirect = () => {
    setLocation('/');
  };

  if (verificationStatus === 'processing') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto relative">
              <Mail className="w-10 h-10 text-white animate-pulse" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                E-posta Doğrulanıyor...
              </CardTitle>
              <p className="text-gray-600">
                Lütfen bekleyin, hesabınız aktif hale getiriliyor
              </p>
            </div>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-pink-50 to-rose-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mx-auto">
              <Mail className="w-10 h-10 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-red-600 mb-2">
                Doğrulama Hatası
              </CardTitle>
              <p className="text-gray-600">
                E-posta doğrulaması sırasında bir hata oluştu
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleManualRedirect}
              className="w-full h-12 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white font-medium rounded-lg"
            >
              Ana Sayfaya Dön
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Success Card */}
        <Card className="shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-6 pb-8">
            <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto relative">
              <CheckCircle className="w-12 h-12 text-white" />
              <div className="absolute inset-0 rounded-full border-4 border-green-200 animate-pulse"></div>
            </div>
            <div>
              <CardTitle className="text-3xl font-bold text-green-600 mb-3">
                🎉 E-mail Doğrulandı!
              </CardTitle>
              <p className="text-gray-600 text-lg">
                Hesabınız başarıyla aktif hale getirildi
              </p>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-green-900 mb-1">
                    Doğrulama Başarılı!
                  </p>
                  <p className="text-green-700">
                    E-posta adresiniz doğrulandı ve hesabınız aktif hale getirildi. 
                    Artık sisteme giriş yapabilirsiniz.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <Shield className="w-5 h-5 text-blue-600 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900 mb-1">
                    Güvenlik
                  </p>
                  <p className="text-blue-700">
                    Hesabınızın güvenliği için e-posta ve şifrenizle giriş yapmanız gerekiyor.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <p className="font-medium mb-1">
                    Giriş sayfasına yönlendiriliyorsunuz...
                  </p>
                  <p className="text-green-100">
                    {countdown} saniye içinde otomatik yönlendirme
                  </p>
                </div>
                <div className="text-2xl font-bold">
                  {countdown}
                </div>
              </div>
            </div>

            <Button
              onClick={handleManualRedirect}
              className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <span>Hemen Giriş Sayfasına Git</span>
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card className="shadow-lg border-0 bg-white/60 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 text-sm text-gray-600">
              <Clock className="w-4 h-4 flex-shrink-0" />
              <p>
                Ana sayfada e-posta ve şifrenizle giriş yaparak hesabınıza erişebilirsiniz.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
