import { useState, useEffect } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isSameDay, addMonths, subMonths, startOfWeek, endOfWeek } from "date-fns";
import { tr } from "date-fns/locale";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Plus, Filter, Search, X, Clock, User, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { api } from "../../lib/api";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { useSwipeGesture, useDeviceCapabilities, useCalendarAnimations } from "../../hooks/useSwipeGesture";

interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: string;
  customerName?: string;
  eventType?: string;
}

type ViewMode = 'month' | 'week' | 'day' | 'agenda';

export function ModernCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('month');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [showEventModal, setShowEventModal] = useState(false);

  // Device capabilities and animations
  const { isMobile, isTouchDevice } = useDeviceCapabilities();
  const { isAnimating, triggerAnimation } = useCalendarAnimations();

  const { data: events, isLoading, error } = useQuery({
    queryKey: ["calendar"],
    queryFn: api.calendar.getEvents,
    staleTime: 10 * 1000,
    refetchOnWindowFocus: true,
  });

  const calendarEvents: CalendarEvent[] = events?.map(event => ({
    ...event,
    start: new Date(event.start),
    end: new Date(event.end),
  })) || [];

  // Filter events based on search and filter
  const filteredEvents = calendarEvents.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.customerName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || event.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    return filteredEvents.filter(event => isSameDay(event.start, date));
  };

  // Handle event click - switch to day view in month view, show details in day view
  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent date selection

    if (viewMode === 'month') {
      // Switch to day view and set the event's date as current date
      triggerAnimation(() => {
        setCurrentDate(event.start);
        setSelectedDate(event.start);
        setViewMode('day');
      });
    } else {
      // Show event details modal
      setSelectedEvent(event);
      setShowEventModal(true);
    }
  };

  // Handle event modal close
  const handleCloseEventModal = () => {
    setShowEventModal(false);
    setSelectedEvent(null);
  };

  // Navigation functions with animations
  const navigatePrevious = () => {
    triggerAnimation(() => {
      if (viewMode === 'month') {
        setCurrentDate(subMonths(currentDate, 1));
      } else if (viewMode === 'week') {
        const newDate = new Date(currentDate);
        newDate.setDate(newDate.getDate() - 7);
        setCurrentDate(newDate);
      } else if (viewMode === 'day') {
        const newDate = new Date(currentDate);
        newDate.setDate(newDate.getDate() - 1);
        setCurrentDate(newDate);
      }
    });
  };

  const navigateNext = () => {
    triggerAnimation(() => {
      if (viewMode === 'month') {
        setCurrentDate(addMonths(currentDate, 1));
      } else if (viewMode === 'week') {
        const newDate = new Date(currentDate);
        newDate.setDate(newDate.getDate() + 7);
        setCurrentDate(newDate);
      } else if (viewMode === 'day') {
        const newDate = new Date(currentDate);
        newDate.setDate(newDate.getDate() + 1);
        setCurrentDate(newDate);
      }
    });
  };

  const navigateToday = () => {
    triggerAnimation(() => {
      setCurrentDate(new Date());
    });
  };

  // Swipe gesture setup for mobile navigation
  const swipeRef = useSwipeGesture({
    onSwipeLeft: isMobile ? navigateNext : undefined,
    onSwipeRight: isMobile ? navigatePrevious : undefined,
    threshold: 50
  });

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Kesinleşti":
        return "bg-green-500";
      case "İptal":
        return "bg-red-500";
      case "Teklif":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Kesinleşti":
        return "default";
      case "İptal":
        return "destructive";
      case "Teklif":
        return "secondary";
      default:
        return "outline";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-96">
        <p className="text-red-500">Ajanda yüklenirken hata oluştu</p>
      </div>
    );
  }

  return (
    <div
      ref={swipeRef}
      className={cn(
        "bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden modern-calendar",
        isAnimating && "view-transition"
      )}
    >
      {/* Header */}
      <div className="p-4 sm:p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
        {/* Top Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Etkinlik ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/80 backdrop-blur-sm"
            />
          </div>
          
          {/* Filter */}
          <div className="flex gap-2">
            <Button
              variant={selectedFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('all')}
              className="text-xs"
            >
              Tümü
            </Button>
            <Button
              variant={selectedFilter === 'Kesinleşti' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('Kesinleşti')}
              className="text-xs"
            >
              Kesinleşti
            </Button>
            <Button
              variant={selectedFilter === 'Teklif' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('Teklif')}
              className="text-xs"
            >
              Teklif
            </Button>
          </div>
        </div>

        {/* Navigation and View Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Navigation */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={navigatePrevious}
              className="p-2"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={navigateToday}
              className="px-3 py-2 text-sm font-medium"
            >
              Bugün
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={navigateNext}
              className="p-2"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
            
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 ml-3">
              {viewMode === 'month' && format(currentDate, "MMMM yyyy", { locale: tr })}
              {viewMode === 'week' && `${format(startOfWeek(currentDate, { locale: tr }), "d MMM", { locale: tr })} - ${format(endOfWeek(currentDate, { locale: tr }), "d MMM yyyy", { locale: tr })}`}
              {viewMode === 'day' && format(currentDate, "d MMMM yyyy", { locale: tr })}
              {viewMode === 'agenda' && "Ajanda Görünümü"}
            </h2>
          </div>

          {/* View Mode Switcher */}
          <div className="flex bg-white rounded-lg p-1 shadow-sm">
            {(['month', 'week', 'day', 'agenda'] as ViewMode[]).map((mode) => (
              <Button
                key={mode}
                variant={viewMode === mode ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode(mode)}
                className="text-xs px-3 py-1.5"
              >
                {mode === 'month' && 'Ay'}
                {mode === 'week' && 'Hafta'}
                {mode === 'day' && 'Gün'}
                {mode === 'agenda' && 'Ajanda'}
              </Button>
            ))}
          </div>
        </div>

        {/* Mobile Swipe Indicator */}
        {isMobile && (
          <div className="flex justify-center mt-2">
            <div className="flex items-center gap-1 text-xs text-gray-400">
              <div className="w-1 h-1 bg-gray-300 rounded-full swipe-indicator"></div>
              <span>Kaydırarak gezin</span>
              <div className="w-1 h-1 bg-gray-300 rounded-full swipe-indicator"></div>
            </div>
          </div>
        )}
      </div>

      {/* Calendar Content */}
      <div className="p-4 sm:p-6">
        {viewMode === 'month' && (
          <MonthView
            currentDate={currentDate}
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            getEventsForDate={getEventsForDate}
            getStatusColor={getStatusColor}
            onEventClick={handleEventClick}
            isMobile={isMobile}
            isAnimating={isAnimating}
          />
        )}

        {viewMode === 'week' && (
          <WeekView
            currentDate={currentDate}
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
            getEventsForDate={getEventsForDate}
            getStatusColor={getStatusColor}
            getStatusBadgeVariant={getStatusBadgeVariant}
            onEventClick={handleEventClick}
            isMobile={isMobile}
            isAnimating={isAnimating}
          />
        )}

        {viewMode === 'day' && (
          <DayView
            currentDate={currentDate}
            getEventsForDate={getEventsForDate}
            getStatusBadgeVariant={getStatusBadgeVariant}
            onEventClick={handleEventClick}
            isMobile={isMobile}
            isAnimating={isAnimating}
          />
        )}

        {viewMode === 'agenda' && (
          <AgendaView
            events={filteredEvents}
            getStatusBadgeVariant={getStatusBadgeVariant}
          />
        )}
      </div>

      {/* Event Details Modal */}
      <Dialog open={showEventModal} onOpenChange={setShowEventModal}>
        <DialogContent className="max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CalendarIcon className="w-5 h-5" />
              Etkinlik Detayları
            </DialogTitle>
          </DialogHeader>

          {selectedEvent && (
            <div className="space-y-4">
              {/* Event Title */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {selectedEvent.title}
                </h3>
                <Badge variant={getStatusBadgeVariant(selectedEvent.status)}>
                  {selectedEvent.status}
                </Badge>
              </div>

              {/* Event Details */}
              <div className="space-y-3">
                {selectedEvent.customerName && (
                  <div className="flex items-center gap-3">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">{selectedEvent.customerName}</span>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-700">
                    {format(selectedEvent.start, "d MMMM yyyy, HH:mm", { locale: tr })} - {format(selectedEvent.end, "HH:mm", { locale: tr })}
                  </span>
                </div>

                {selectedEvent.eventType && (
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">{selectedEvent.eventType}</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCloseEventModal}
                  className="flex-1"
                >
                  Kapat
                </Button>
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    // TODO: Navigate to organization details
                    handleCloseEventModal();
                  }}
                >
                  Detayları Gör
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Month View Component
interface MonthViewProps {
  currentDate: Date;
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  getEventsForDate: (date: Date) => CalendarEvent[];
  getStatusColor: (status: string) => string;
  onEventClick: (event: CalendarEvent, e: React.MouseEvent) => void;
  isMobile: boolean;
  isAnimating: boolean;
}

function MonthView({ currentDate, selectedDate, onDateSelect, getEventsForDate, getStatusColor, onEventClick, isMobile, isAnimating }: MonthViewProps) {
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const calendarStart = startOfWeek(monthStart, { locale: tr });
  const calendarEnd = endOfWeek(monthEnd, { locale: tr });

  const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  const weekDays = ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'];

  return (
    <div className={cn(
      "space-y-4 calendar-fade-in",
      isAnimating && "opacity-50 transition-opacity duration-300"
    )}>
      {/* Week Headers */}
      <div className="grid grid-cols-7 gap-1 sm:gap-2">
        {weekDays.map((day) => (
          <div key={day} className="text-center text-xs sm:text-sm font-medium text-gray-500 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className={cn(
        "grid grid-cols-7 gap-1 sm:gap-2 calendar-grid",
        isMobile && "gap-1"
      )}>
        {days.map((day) => {
          const dayEvents = getEventsForDate(day);
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isSelected = selectedDate && isSameDay(day, selectedDate);
          const isTodayDate = isToday(day);

          return (
            <div
              key={day.toISOString()}
              onClick={() => onDateSelect(day)}
              className={cn(
                "min-h-[60px] sm:min-h-[100px] p-1 sm:p-2 border border-gray-100 rounded-lg cursor-pointer date-cell",
                "transition-all duration-200 hover:bg-gray-50 hover:shadow-sm",
                !isCurrentMonth && "text-gray-300 bg-gray-50/50",
                isSelected && "ring-2 ring-blue-500 bg-blue-50 shadow-md",
                isTodayDate && "bg-blue-100 border-blue-300 shadow-sm",
                isMobile && "touch-action-manipulation active:scale-95"
              )}
            >
              {/* Date Number */}
              <div className={cn(
                "text-xs sm:text-sm font-medium mb-1",
                isTodayDate && "text-blue-600 font-bold",
                isSelected && "text-blue-600"
              )}>
                {format(day, "d")}
              </div>

              {/* Events */}
              <div className="space-y-1">
                {dayEvents.slice(0, isMobile ? 2 : 3).map((event, index) => (
                  <div
                    key={event.id}
                    onClick={(e) => onEventClick(event, e)}
                    className={cn(
                      "text-xs px-1 py-0.5 rounded text-white truncate event-card cursor-pointer",
                      "transition-all duration-200 hover:shadow-sm hover:scale-105",
                      getStatusColor(event.status),
                      isMobile && "text-[10px] px-1 py-0.5"
                    )}
                    title={`${event.title} - Gün görünümü için tıklayın`}
                  >
                    {event.title}
                  </div>
                ))}
                {dayEvents.length > (isMobile ? 2 : 3) && (
                  <div className={cn(
                    "text-xs text-gray-500 px-1 font-medium",
                    isMobile && "text-[10px]"
                  )}>
                    +{dayEvents.length - (isMobile ? 2 : 3)} daha
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Week View Component
interface WeekViewProps {
  currentDate: Date;
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  getEventsForDate: (date: Date) => CalendarEvent[];
  getStatusColor: (status: string) => string;
  getStatusBadgeVariant: (status: string) => "default" | "destructive" | "secondary" | "outline";
  onEventClick: (event: CalendarEvent, e: React.MouseEvent) => void;
  isMobile: boolean;
  isAnimating: boolean;
}

function WeekView({ currentDate, selectedDate, onDateSelect, getEventsForDate, getStatusColor, getStatusBadgeVariant, onEventClick, isMobile, isAnimating }: WeekViewProps) {
  const weekStart = startOfWeek(currentDate, { locale: tr });
  const weekDays = eachDayOfInterval({
    start: weekStart,
    end: new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000)
  });

  const timeSlots = Array.from({ length: 24 }, (_, i) => i);

  return (
    <div className={cn(
      "space-y-4 calendar-fade-in",
      isAnimating && "opacity-50 transition-opacity duration-300"
    )}>
      {/* Week Header */}
      <div className="grid grid-cols-8 gap-1 sm:gap-2">
        <div className="text-center text-xs sm:text-sm font-medium text-gray-500 py-2">
          Saat
        </div>
        {weekDays.map((day) => (
          <div key={day.toISOString()} className="text-center">
            <div className="text-xs sm:text-sm font-medium text-gray-500">
              {format(day, "EEE", { locale: tr })}
            </div>
            <div className={cn(
              "text-lg sm:text-xl font-bold mt-1 p-2 rounded-lg cursor-pointer transition-all duration-200",
              isSameDay(day, currentDate) && "bg-blue-100 text-blue-600",
              selectedDate && isSameDay(day, selectedDate) && "bg-blue-500 text-white",
              isToday(day) && "bg-green-100 text-green-600"
            )}
            onClick={() => onDateSelect(day)}
            >
              {format(day, "d")}
            </div>
          </div>
        ))}
      </div>

      {/* Week Grid */}
      <div className="max-h-[400px] sm:max-h-[500px] overflow-y-auto">
        <div className="grid grid-cols-8 gap-1 sm:gap-2">
          {timeSlots.map((hour) => (
            <div key={hour} className="contents">
              {/* Time Label */}
              <div className="text-xs text-gray-500 py-2 text-right pr-2 border-r border-gray-100">
                {hour.toString().padStart(2, '0')}:00
              </div>

              {/* Day Columns */}
              {weekDays.map((day) => {
                const dayEvents = getEventsForDate(day).filter(event => {
                  const eventHour = event.start.getHours();
                  return eventHour === hour;
                });

                return (
                  <div
                    key={`${day.toISOString()}-${hour}`}
                    className={cn(
                      "min-h-[40px] sm:min-h-[50px] p-1 border border-gray-100 hover:bg-gray-50 transition-colors duration-200",
                      isToday(day) && "bg-blue-50/30"
                    )}
                  >
                    {dayEvents.map((event) => (
                      <div
                        key={event.id}
                        onClick={(e) => onEventClick(event, e)}
                        className={cn(
                          "text-xs px-1 py-0.5 rounded text-white truncate mb-1 event-card cursor-pointer",
                          "hover:scale-105 transition-transform duration-200",
                          getStatusColor(event.status),
                          isMobile && "text-[10px]"
                        )}
                        title={`${event.title} - ${event.customerName} - Gün görünümü için tıklayın`}
                      >
                        {event.title}
                      </div>
                    ))}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Day View Component
interface DayViewProps {
  currentDate: Date;
  getEventsForDate: (date: Date) => CalendarEvent[];
  getStatusBadgeVariant: (status: string) => "default" | "destructive" | "secondary" | "outline";
  onEventClick: (event: CalendarEvent, e: React.MouseEvent) => void;
  isMobile: boolean;
  isAnimating: boolean;
}

function DayView({ currentDate, getEventsForDate, getStatusBadgeVariant, onEventClick, isMobile, isAnimating }: DayViewProps) {
  const dayEvents = getEventsForDate(currentDate);
  const timeSlots = Array.from({ length: 24 }, (_, i) => i);

  return (
    <div className={cn(
      "space-y-4 calendar-fade-in",
      isAnimating && "opacity-50 transition-opacity duration-300"
    )}>
      {/* Day Header */}
      <div className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
        <h3 className="text-lg sm:text-xl font-bold text-gray-900">
          {format(currentDate, "d MMMM yyyy", { locale: tr })}
        </h3>
        <p className="text-sm text-gray-600">
          {format(currentDate, "EEEE", { locale: tr })}
        </p>
        <div className="mt-2">
          <Badge variant="outline">
            {dayEvents.length} etkinlik
          </Badge>
        </div>
      </div>

      {/* Day Schedule */}
      <div className="max-h-[500px] overflow-y-auto">
        <div className="space-y-2">
          {timeSlots.map((hour) => {
            const hourEvents = dayEvents.filter(event => {
              const eventHour = event.start.getHours();
              return eventHour === hour;
            });

            return (
              <div key={hour} className="flex gap-4">
                {/* Time */}
                <div className="w-16 text-right text-sm text-gray-500 py-2">
                  {hour.toString().padStart(2, '0')}:00
                </div>

                {/* Events */}
                <div className="flex-1 min-h-[50px] border-l-2 border-gray-100 pl-4">
                  {hourEvents.length > 0 ? (
                    <div className="space-y-2">
                      {hourEvents.map((event) => (
                        <div
                          key={event.id}
                          onClick={(e) => onEventClick(event, e)}
                          className="bg-gray-50 rounded-lg p-3 event-card hover:bg-gray-100 transition-all duration-200 hover:shadow-md cursor-pointer"
                        >
                          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900 mb-1">{event.title}</h4>
                              {event.customerName && (
                                <p className="text-sm text-gray-600 mb-2">{event.customerName}</p>
                              )}
                              <div className="flex items-center gap-4 text-xs text-gray-500">
                                <span>
                                  {format(event.start, "HH:mm", { locale: tr })} - {format(event.end, "HH:mm", { locale: tr })}
                                </span>
                                {event.eventType && <span>{event.eventType}</span>}
                              </div>
                            </div>
                            <Badge variant={getStatusBadgeVariant(event.status)}>
                              {event.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-2 text-gray-400 text-sm">
                      Bu saatte etkinlik yok
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// Agenda View Component
interface AgendaViewProps {
  events: CalendarEvent[];
  getStatusBadgeVariant: (status: string) => "default" | "destructive" | "secondary" | "outline";
}

function AgendaView({ events, getStatusBadgeVariant }: AgendaViewProps) {
  // Group events by date
  const groupedEvents = events.reduce((groups, event) => {
    const dateKey = format(event.start, "yyyy-MM-dd");
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(event);
    return groups;
  }, {} as Record<string, CalendarEvent[]>);

  const sortedDates = Object.keys(groupedEvents).sort();

  if (sortedDates.length === 0) {
    return (
      <div className="text-center py-12">
        <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Gösterilecek etkinlik bulunamadı</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 calendar-fade-in">
      {sortedDates.map((dateKey) => {
        const date = new Date(dateKey);
        const dayEvents = groupedEvents[dateKey];

        return (
          <div key={dateKey} className="space-y-3">
            {/* Date Header */}
            <div className="flex items-center gap-3">
              <div className="text-lg font-semibold text-gray-900">
                {format(date, "d MMMM yyyy", { locale: tr })}
              </div>
              <div className="text-sm text-gray-500">
                {format(date, "EEEE", { locale: tr })}
              </div>
              <div className="flex-1 h-px bg-gray-200"></div>
            </div>

            {/* Events for this date */}
            <div className="space-y-2">
              {dayEvents.map((event) => (
                <div
                  key={event.id}
                  className="bg-gray-50 rounded-lg p-4 event-card hover:bg-gray-100 transition-all duration-200 hover:shadow-md"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-1">{event.title}</h3>
                      {event.customerName && (
                        <p className="text-sm text-gray-600 mb-2">{event.customerName}</p>
                      )}
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>
                          {format(event.start, "HH:mm", { locale: tr })} - {format(event.end, "HH:mm", { locale: tr })}
                        </span>
                        {event.eventType && <span>{event.eventType}</span>}
                      </div>
                    </div>
                    <Badge variant={getStatusBadgeVariant(event.status)}>
                      {event.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}
