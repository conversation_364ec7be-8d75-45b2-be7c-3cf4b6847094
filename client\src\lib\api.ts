import { apiRequest } from "./queryClient";
import { mockApi } from "./mockApi";
import { supabaseApi } from "./supabaseApi";
import type {
  Service,
  OrganizationDetailsDto,
  CalendarEventDto,
  CreateOrganizationDto
} from "../types";

// FORCE Supabase API usage - bypass all environment variables
const USE_SUPABASE_DIRECT = true;
const USE_MOCK_API = false;

export const api = {
  // Services
  services: {
    getAll: async (): Promise<Service[]> => {
      console.log('🔗 FORCED: Using Supabase Direct API');
      return supabaseApi.services.getAll();
    },
    create: async (service: Omit<Service, "id" | "createdAt">): Promise<Service> => {
      return supabaseApi.services.create(service);
    },
    update: async (id: string, service: Partial<Omit<Service, "id" | "createdAt">>): Promise<Service> => {
      return supabaseApi.services.update(id, service);
    },
    delete: async (id: string): Promise<void> => {
      return supabaseApi.services.delete(id);
    }
  },

  // Organizations
  organizations: {
    getAll: async (): Promise<OrganizationDetailsDto[]> => {
      return supabaseApi.organizations.getAll();
    },
    getById: async (id: string): Promise<OrganizationDetailsDto> => {
      // TODO: Implement getById in supabaseApi
      throw new Error("getById not implemented yet");
    },
    create: async (organization: CreateOrganizationDto): Promise<OrganizationDetailsDto> => {
      return supabaseApi.organizations.create(organization);
    },
    update: async (id: string, organization: any): Promise<OrganizationDetailsDto> => {
      return supabaseApi.organizations.update(id, organization);
    },
    updateStatus: async (id: string, status: string): Promise<void> => {
      return supabaseApi.organizations.updateStatus(id, status);
    },
    delete: async (id: string): Promise<void> => {
      return supabaseApi.organizations.delete(id);
    }
  },

  // Calendar
  calendar: {
    getEvents: async (): Promise<CalendarEventDto[]> => {
      return supabaseApi.calendar.getEvents();
    }
  }
};
