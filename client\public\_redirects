# API redirects (must be first)
/api/health /.netlify/functions/health 200!
/api/organizations/calendar /.netlify/functions/calendar-simple 200!
/api/organizations/:id/status /.netlify/functions/organizations-simple 200!
/api/organizations/:id /.netlify/functions/organizations-simple 200!
/api/organizations/* /.netlify/functions/organizations-simple 200!
/api/organizations /.netlify/functions/organizations-simple 200!
/api/services/:id /.netlify/functions/services-simple 200!
/api/services/* /.netlify/functions/services-simple 200!
/api/services /.netlify/functions/services-simple 200!

# SPA routing - redirect all routes to index.html (must be last)
/* /index.html 200
