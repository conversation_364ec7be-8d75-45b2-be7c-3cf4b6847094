import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Alert, AlertDescription } from '../ui/alert'
import { Loader2, User, Building, Mail, Phone, Lock, Eye, EyeOff, UserPlus, CheckCircle } from 'lucide-react'

interface RegisterFormProps {
  onToggleMode: () => void
}

export const RegisterForm: React.FC<RegisterFormProps> = ({ onToggleMode }) => {
  const { signUp } = useAuth()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    companyName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [needsConfirmation, setNeedsConfirmation] = useState(false)
  const [userEmail, setUserEmail] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError('Şifreler eşleşmiyor')
      setLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError('Şifre en az 6 karakter olmalıdır')
      setLoading(false)
      return
    }

    const { error, needsConfirmation } = await signUp(formData.email, formData.password, {
      firstName: formData.firstName,
      lastName: formData.lastName,
      companyName: formData.companyName,
      phone: formData.phone || undefined,
    })

    if (error) {
      setError(error.message)
    } else if (needsConfirmation) {
      setNeedsConfirmation(true)
      setUserEmail(formData.email)
    } else {
      setSuccess(true)
    }

    setLoading(false)
  }

  // Email confirmation needed
  if (needsConfirmation) {
    return (
      <Card className="w-full max-w-md mx-auto shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Mail className="w-10 h-10 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-blue-600 mb-2">
            Email Doğrulaması Gerekli 📧
          </CardTitle>
          <CardDescription className="text-gray-600 text-base">
            Hesabınızı aktif hale getirmek için email adresinizi doğrulamanız gerekiyor
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800 mb-2">
              <strong>{userEmail}</strong> adresine doğrulama emaili gönderildi.
            </p>
            <p className="text-sm text-blue-700">
              Email kutunuzu kontrol edin ve doğrulama linkine tıklayın.
              Link sizi <strong>gezdim.net</strong> adresine yönlendirecek.
              Spam klasörünü de kontrol etmeyi unutmayın.
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={onToggleMode}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
            >
              Giriş Sayfasına Dön
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/email-confirmation'}
              className="w-full"
            >
              Email Doğrulama Sayfasına Git
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (success) {
    return (
      <Card className="w-full max-w-md mx-auto shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="text-center pb-8">
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-green-600 mb-2">
            Kayıt Başarılı! 🎉
          </CardTitle>
          <CardDescription className="text-gray-600 text-base leading-relaxed">
            Hesabınız başarıyla oluşturuldu. E-posta adresinizi kontrol edin ve hesabınızı doğrulayın.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={onToggleMode}
            className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
          >
            Giriş Sayfasına Dön
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto shadow-2xl border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="text-center pb-6">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
          <UserPlus className="w-8 h-8 text-white" />
        </div>
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
          Hesap Oluşturun
        </CardTitle>
        <CardDescription className="text-gray-600 text-base">
          Ücretsiz hesabınızı oluşturun ve organizasyon yönetimine başlayın
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-5">
          {error && (
            <Alert variant="destructive" className="border-red-200 bg-red-50">
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-gray-700 font-medium">
                Ad
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                  disabled={loading}
                  className="pl-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                  placeholder="Adınız"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName" className="text-gray-700 font-medium">
                Soyad
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                  disabled={loading}
                  className="pl-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                  placeholder="Soyadınız"
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="companyName" className="text-gray-700 font-medium">
              Organizasyon/Şirket Adı
            </Label>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                id="companyName"
                name="companyName"
                value={formData.companyName}
                onChange={handleChange}
                required
                disabled={loading}
                className="pl-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                placeholder="Şirket veya organizasyon adınız"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-gray-700 font-medium">
              E-posta Adresi
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                disabled={loading}
                className="pl-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="text-gray-700 font-medium">
              Cep Telefonu <span className="text-gray-400">(opsiyonel)</span>
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                disabled={loading}
                className="pl-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                placeholder="0555 123 45 67"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-gray-700 font-medium">
                Şifre
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  required
                  disabled={loading}
                  className="pl-10 pr-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                  placeholder="En az 6 karakter"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-gray-700 font-medium">
                Şifre Tekrar
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  disabled={loading}
                  className="pl-10 pr-10 h-11 border-2 border-gray-200 focus:border-purple-500 focus:ring-purple-500 rounded-lg transition-all duration-200"
                  placeholder="Şifreyi tekrar girin"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Hesap oluşturuluyor...
              </>
            ) : (
              <>
                <UserPlus className="mr-2 h-5 w-5" />
                Ücretsiz Hesap Oluştur
              </>
            )}
          </Button>

          <div className="text-center pt-4">
            <p className="text-gray-600 mb-2">Zaten hesabınız var mı?</p>
            <Button
              type="button"
              variant="outline"
              onClick={onToggleMode}
              disabled={loading}
              className="border-2 border-gray-200 hover:border-purple-500 hover:text-purple-600 transition-all duration-200 rounded-xl"
            >
              Giriş Yapın
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
