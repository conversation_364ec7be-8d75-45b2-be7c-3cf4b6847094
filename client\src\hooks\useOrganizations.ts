import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { api } from "../lib/api";
import { supabase } from "../lib/supabase";
import type { CreateOrganizationDto } from "../types";

export function useOrganizations() {
  const queryClient = useQueryClient();

  const organizationsQuery = useQuery({
    queryKey: ["organizations"],
    queryFn: api.organizations.getAll,
    staleTime: 10 * 1000, // 10 saniye fresh tut
    refetchOnWindowFocus: true,
  });

  // Realtime subscription for organizations
  useEffect(() => {
    const channel = supabase
      .channel('organizations-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'eventflow_organizations'
        },
        (payload) => {
          console.log('🔄 Organizations realtime update:', payload);
          // Invalidate queries to refetch data
          queryClient.invalidateQueries({ queryKey: ["organizations"] });
          queryClient.invalidateQueries({ queryKey: ["calendar"] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  const createOrganizationMutation = useMutation({
    mutationFn: api.organizations.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["calendar"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      api.organizations.updateStatus(id, status),
    onMutate: async ({ id, status }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["organizations"] });

      // Snapshot previous value
      const previousOrganizations = queryClient.getQueryData(["organizations"]);

      // Optimistically update
      queryClient.setQueryData(["organizations"], (old: any) => {
        if (!old) return old;
        return old.map((org: any) =>
          org.id === id ? { ...org, status } : org
        );
      });

      return { previousOrganizations };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousOrganizations) {
        queryClient.setQueryData(["organizations"], context.previousOrganizations);
      }
      // Error handling will be done at component level
    },
    onSuccess: () => {
      // Success handling will be done at component level
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["calendar"] });
    },
  });

  const updateOrganizationMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      api.organizations.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["calendar"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  const deleteOrganizationMutation = useMutation({
    mutationFn: api.organizations.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["calendar"] });
    },
    onError: () => {
      // Error handling will be done at component level
    },
  });

  return {
    organizations: organizationsQuery.data || [],
    isLoading: organizationsQuery.isLoading,
    error: organizationsQuery.error,
    createOrganization: createOrganizationMutation.mutate,
    updateOrganization: updateOrganizationMutation.mutate,
    updateStatus: updateStatusMutation.mutate,
    deleteOrganization: deleteOrganizationMutation.mutate,
    isCreating: createOrganizationMutation.isPending,
    isUpdating: updateOrganizationMutation.isPending || updateStatusMutation.isPending,
    isDeleting: deleteOrganizationMutation.isPending,
  };
}

export function useOrganization(id: string) {
  return useQuery({
    queryKey: ["organizations", id],
    queryFn: () => api.organizations.getById(id),
    enabled: !!id,
  });
}
