import { useState, useEffect } from "react";
import { Layout } from "../components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "../lib/supabase";
import { useAuth } from "../contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { ActivityLogger } from "../lib/activityLogger";
import { 
  Database, 
  Server, 
  Wifi, 
  WifiOff, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Settings as SettingsIcon,
  Info
} from "lucide-react";

interface ConnectionStatus {
  backend: boolean;
  supabase: boolean;
  lastChecked: Date;
}

export default function SettingsPage() {
  const { user, profile } = useAuth();
  const { toast } = useToast();

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    backend: false,
    supabase: false,
    lastChecked: new Date()
  });
  const [isChecking, setIsChecking] = useState(false);

  // Profile form state
  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    companyName: "",
    phone: "",
    email: ""
  });

  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Initialize profile data when profile is loaded
  useEffect(() => {
    if (profile) {
      setProfileData({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        companyName: profile.companyName || "",
        phone: profile.phone || "",
        email: user?.email || ""
      });
    } else if (user && !profile) {
      // If user exists but no profile, set default values for the form
      setProfileData({
        firstName: "",
        lastName: "",
        companyName: "",
        phone: "",
        email: user.email || ""
      });
    }
  }, [profile, user]);



  const updateProfile = async () => {
    if (!user) return;

    setIsUpdatingProfile(true);
    try {
      // First try to update, if no rows affected, insert
      const { data: updateData, error: updateError } = await supabase
        .from('eventflow_profiles')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          company_name: profileData.companyName,
          phone: profileData.phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select();

      if (updateError) {
        console.error('Update error:', updateError);
        throw updateError;
      }

      // If no rows were updated, insert new profile
      if (!updateData || updateData.length === 0) {
        const { error: insertError } = await supabase
          .from('eventflow_profiles')
          .insert({
            id: user.id,
            first_name: profileData.firstName,
            last_name: profileData.lastName,
            company_name: profileData.companyName,
            phone: profileData.phone,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Insert error:', insertError);
          throw insertError;
        }
      }

      // Log user activity
      ActivityLogger.updateProfile({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        companyName: profileData.companyName,
        phone: profileData.phone
      });

      toast({
        title: "✅ Başarılı",
        description: "Profil bilgileri güncellendi",
        variant: "success",
      });
    } catch (error) {
      console.error('Profile update error:', error);
      toast({
        title: "❌ Hata",
        description: "Profil güncellenirken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  const changePassword = async () => {
    if (!user) return;

    // Validation
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      toast({
        title: "❌ Hata",
        description: "Tüm alanları doldurun",
        variant: "destructive",
      });
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: "❌ Hata",
        description: "Yeni şifreler eşleşmiyor",
        variant: "destructive",
      });
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast({
        title: "❌ Hata",
        description: "Yeni şifre en az 6 karakter olmalıdır",
        variant: "destructive",
      });
      return;
    }

    setIsChangingPassword(true);
    try {
      // First verify current password by trying to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email!,
        password: passwordData.currentPassword
      });

      if (signInError) {
        toast({
          title: "❌ Hata",
          description: "Mevcut şifre yanlış",
          variant: "destructive",
        });
        return;
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) {
        console.error('Password update error:', updateError);
        throw updateError;
      }

      // Clear form
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      });

      // Log user activity
      ActivityLogger.changePassword();

      toast({
        title: "✅ Başarılı",
        description: "Şifre başarıyla değiştirildi",
        variant: "success",
      });
    } catch (error) {
      console.error('Password change error:', error);
      toast({
        title: "❌ Hata",
        description: "Şifre değiştirilirken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  const checkConnections = async () => {
    setIsChecking(true);

    try {
      // Backend bağlantısını test et (Netlify Functions)
      const backendResponse = await fetch('/api/health');
      const backendStatus = backendResponse.ok;

      // Supabase bağlantısını test et (doğrudan Supabase API üzerinden)
      const { data: { user } } = await supabase.auth.getUser();
      const supabaseStatus = !!user;

      setConnectionStatus({
        backend: backendStatus,
        supabase: supabaseStatus,
        lastChecked: new Date()
      });
    } catch (error) {
      console.error('Connection check failed:', error);
      setConnectionStatus({
        backend: false,
        supabase: false,
        lastChecked: new Date()
      });
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnections();
  }, []);

  const StatusBadge = ({ status, label }: { status: boolean; label: string }) => (
    <Badge variant={status ? "default" : "destructive"} className="flex items-center gap-2">
      {status ? (
        <CheckCircle className="w-4 h-4" />
      ) : (
        <XCircle className="w-4 h-4" />
      )}
      {label}
    </Badge>
  );

  return (
    <Layout title="Ayarlar" subtitle="Profil ve sistem ayarları">
      <div className="max-w-4xl mx-auto space-y-6">

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile">Profil Bilgileri</TabsTrigger>
            <TabsTrigger value="system">Sistem Bilgileri</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            {/* Profil Bilgileri */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="w-5 h-5" />
                  Profil Bilgileri
                </CardTitle>
                <CardDescription>
                  Hesap bilgilerinizi görüntüleyin ve güncelleyin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Ad</Label>
                    <Input
                      id="firstName"
                      value={profileData.firstName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                      placeholder="Adınız"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">Soyad</Label>
                    <Input
                      id="lastName"
                      value={profileData.lastName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                      placeholder="Soyadınız"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="companyName">Şirket Adı</Label>
                    <Input
                      id="companyName"
                      value={profileData.companyName}
                      onChange={(e) => setProfileData(prev => ({ ...prev, companyName: e.target.value }))}
                      placeholder="Şirket adınız"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Telefon</Label>
                    <Input
                      id="phone"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="Telefon numaranız"
                    />
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="email">E-posta</Label>
                    <Input
                      id="email"
                      value={profileData.email}
                      disabled
                      placeholder="E-posta adresiniz"
                      className="bg-gray-50"
                    />
                    <p className="text-sm text-gray-500">E-posta adresi değiştirilemez</p>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button
                    onClick={updateProfile}
                    disabled={isUpdatingProfile}
                    className="flex items-center gap-2"
                  >
                    {isUpdatingProfile ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <CheckCircle className="w-4 h-4" />
                    )}
                    {isUpdatingProfile ? "Güncelleniyor..." : "Profili Güncelle"}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Şifre Değiştirme */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="w-5 h-5" />
                  Şifre Değiştirme
                </CardTitle>
                <CardDescription>
                  Hesap şifrenizi güvenli bir şekilde değiştirin
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Mevcut Şifre</Label>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                      placeholder="Mevcut şifrenizi girin"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="newPassword">Yeni Şifre</Label>
                    <Input
                      id="newPassword"
                      type="password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                      placeholder="Yeni şifrenizi girin (en az 6 karakter)"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Yeni Şifre (Tekrar)</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="Yeni şifrenizi tekrar girin"
                    />
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  <Button
                    onClick={changePassword}
                    disabled={isChangingPassword}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    {isChangingPassword ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <SettingsIcon className="w-4 h-4" />
                    )}
                    {isChangingPassword ? "Değiştiriliyor..." : "Şifreyi Değiştir"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            {/* Bağlantı Durumu */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wifi className="w-5 h-5" />
              Bağlantı Durumu
            </CardTitle>
            <CardDescription>
              Sistem bileşenlerinin bağlantı durumunu kontrol edin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Server className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="font-medium">Netlify Functions API</p>
                  <p className="text-sm text-gray-500">/.netlify/functions</p>
                </div>
              </div>
              <StatusBadge 
                status={connectionStatus.backend} 
                label={connectionStatus.backend ? "Bağlı" : "Bağlantı Yok"} 
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Database className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Supabase PostgreSQL</p>
                  <p className="text-sm text-gray-500">REST API üzerinden</p>
                </div>
              </div>
              <StatusBadge 
                status={connectionStatus.supabase} 
                label={connectionStatus.supabase ? "Bağlı" : "Bağlantı Yok"} 
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Son kontrol: {connectionStatus.lastChecked.toLocaleString('tr-TR')}
              </div>
              <Button 
                onClick={checkConnections} 
                disabled={isChecking}
                variant="outline"
                size="sm"
              >
                {isChecking ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                Yenile
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Sistem Bilgileri */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              Sistem Bilgileri
            </CardTitle>
            <CardDescription>
              EventFlow sistem detayları
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium text-sm text-gray-500">Frontend</p>
                <p className="text-lg">React + TypeScript</p>
              </div>
              <div>
                <p className="font-medium text-sm text-gray-500">Backend</p>
                <p className="text-lg">Supabase + Netlify</p>
              </div>
              <div>
                <p className="font-medium text-sm text-gray-500">Veritabanı</p>
                <p className="text-lg">Supabase PostgreSQL</p>
              </div>
              <div>
                <p className="font-medium text-sm text-gray-500">Hosting</p>
                <p className="text-lg">Netlify</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Veritabanı Tabloları
            </CardTitle>
            <CardDescription>
              Supabase PostgreSQL tabloları
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <code className="text-sm">Supabase REST API</code>
                <Badge variant="default">Aktif</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <code className="text-sm">eventflow_services</code>
                <Badge variant="outline">Tablo</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <code className="text-sm">eventflow_organizations</code>
                <Badge variant="outline">Tablo</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <code className="text-sm">eventflow_customers</code>
                <Badge variant="outline">Tablo</Badge>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <code className="text-sm">eventflow_profiles</code>
                <Badge variant="outline">Tablo</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bağlantı Sorunları */}
        {(!connectionStatus.backend || !connectionStatus.supabase) && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <WifiOff className="w-5 h-5" />
                Bağlantı Sorunları
              </CardTitle>
            </CardHeader>
            <CardContent className="text-red-700">
              <div className="space-y-2">
                {!connectionStatus.backend && (
                  <p>• Netlify Functions API'ye bağlanılamıyor. Deployment durumunu kontrol edin.</p>
                )}
                {!connectionStatus.supabase && (
                  <p>• Supabase veritabanına bağlanılamıyor. Giriş yapın veya environment variables'ları kontrol edin.</p>
                )}
              </div>
            </CardContent>
          </Card>
            )}
          </TabsContent>
        </Tabs>

      </div>
    </Layout>
  );
}
