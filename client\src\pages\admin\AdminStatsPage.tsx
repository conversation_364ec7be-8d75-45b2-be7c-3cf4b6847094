import React from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import {
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Activity,
  BarChart3,
  PieChart,
  LineChart,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { formatCurrency } from '../../utils/formatters';
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
  <PERSON>hart as RechartsPieChart,
  Cell,
  Legend
} from 'recharts';

export default function AdminStatsPage() {
  // Fetch system statistics
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['admin', 'stats'],
    queryFn: adminApi.stats.getSystemStats,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <AdminLayout
        title="Sistem İstatistikleri"
        subtitle="Detaylı sistem metrikleri ve analizler"
      >
        <div className="space-y-6">
          {/* Loading skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[1, 2].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-64 bg-gray-200 rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout
        title="Sistem İstatistikleri"
        subtitle="Detaylı sistem metrikleri ve analizler"
      >
        <Card className="border-red-200">
          <CardContent className="p-6 text-center">
            <BarChart3 className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              İstatistikler Yüklenemedi
            </h3>
            <p className="text-red-600">
              Sistem istatistikleri yüklenirken bir hata oluştu.
            </p>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  if (!stats) {
    return (
      <AdminLayout
        title="Sistem İstatistikleri"
        subtitle="Detaylı sistem metrikleri ve analizler"
      >
        <Card>
          <CardContent className="p-6 text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">İstatistik verisi bulunamadı</p>
          </CardContent>
        </Card>
      </AdminLayout>
    );
  }

  // Calculate growth percentages
  const userGrowthPercentage = stats.userGrowth.length > 1 ? 
    ((stats.newUsersThisMonth / (stats.totalUsers - stats.newUsersThisMonth)) * 100) : 0;
  
  const orgGrowthPercentage = stats.organizationGrowth.length > 1 ? 
    ((stats.organizationsThisMonth / (stats.totalOrganizations - stats.organizationsThisMonth)) * 100) : 0;

  const revenueGrowthPercentage = stats.revenueGrowth.length > 1 ? 
    ((stats.revenueThisMonth / (stats.totalRevenue - stats.revenueThisMonth)) * 100) : 0;

  return (
    <AdminLayout
      title="Sistem İstatistikleri"
      subtitle="Detaylı sistem metrikleri ve analizler"
    >
      <div className="space-y-6">
        {/* Main Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-800">Toplam Kullanıcı</CardTitle>
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <Users className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-900">{stats.totalUsers}</div>
              <div className="flex items-center text-sm mt-1">
                {userGrowthPercentage > 0 ? (
                  <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={userGrowthPercentage > 0 ? "text-green-600" : "text-red-600"}>
                  {Math.abs(userGrowthPercentage).toFixed(1)}%
                </span>
                <span className="text-blue-700 ml-1">bu ay</span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                +{stats.newUsersThisMonth} yeni kullanıcı
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-800">Toplam Organizasyon</CardTitle>
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <Calendar className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-900">{stats.totalOrganizations}</div>
              <div className="flex items-center text-sm mt-1">
                {orgGrowthPercentage > 0 ? (
                  <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={orgGrowthPercentage > 0 ? "text-green-600" : "text-red-600"}>
                  {Math.abs(orgGrowthPercentage).toFixed(1)}%
                </span>
                <span className="text-green-700 ml-1">bu ay</span>
              </div>
              <p className="text-xs text-green-600 mt-1">
                +{stats.organizationsThisMonth} yeni organizasyon
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-800">Toplam Gelir</CardTitle>
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                <DollarSign className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900">
                {formatCurrency(stats.totalRevenue)}
              </div>
              <div className="flex items-center text-sm mt-1">
                {revenueGrowthPercentage > 0 ? (
                  <ArrowUpRight className="h-4 w-4 text-green-600 mr-1" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-600 mr-1" />
                )}
                <span className={revenueGrowthPercentage > 0 ? "text-green-600" : "text-red-600"}>
                  {Math.abs(revenueGrowthPercentage).toFixed(1)}%
                </span>
                <span className="text-purple-700 ml-1">bu ay</span>
              </div>
              <p className="text-xs text-purple-600 mt-1">
                {formatCurrency(stats.revenueThisMonth)} bu ay
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-800">Aktif Kullanıcı</CardTitle>
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <Activity className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-900">{stats.activeUsers}</div>
              <p className="text-xs text-orange-600 mt-1">
                Son 30 gün içinde aktif
              </p>
              <div className="w-full bg-orange-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-orange-500 h-2 rounded-full" 
                  style={{ width: `${(stats.activeUsers / stats.totalUsers) * 100}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Growth Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Kullanıcı Büyümesi (Son 12 Ay)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats.userGrowth && stats.userGrowth.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart data={stats.userGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => {
                          const [year, month] = value.split('-');
                          return `${month}/${year.slice(2)}`;
                        }}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip
                        labelFormatter={(value) => {
                          const [year, month] = value.split('-');
                          const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                          return `${monthNames[parseInt(month) - 1]} ${year}`;
                        }}
                        formatter={(value) => [value, 'Yeni Kullanıcı']}
                      />
                      <Line
                        type="monotone"
                        dataKey="count"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6 }}
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Henüz veri bulunmuyor</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Kullanıcı kayıtları oldukça grafik görünecek
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Gelir Büyümesi (Son 12 Ay)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats.revenueGrowth && stats.revenueGrowth.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart data={stats.revenueGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => {
                          const [year, month] = value.split('-');
                          return `${month}/${year.slice(2)}`;
                        }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => `₺${(value / 1000).toFixed(0)}K`}
                      />
                      <Tooltip
                        labelFormatter={(value) => {
                          const [year, month] = value.split('-');
                          const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                          return `${monthNames[parseInt(month) - 1]} ${year}`;
                        }}
                        formatter={(value) => [formatCurrency(value), 'Gelir']}
                      />
                      <Bar
                        dataKey="amount"
                        fill="#10b981"
                        radius={[4, 4, 0, 0]}
                      />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Henüz veri bulunmuyor</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Organizasyon gelirleri oldukça grafik görünecek
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Organization Growth Chart */}
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Organizasyon Büyümesi (Son 12 Ay)
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats.organizationGrowth && stats.organizationGrowth.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart data={stats.organizationGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="date"
                        tick={{ fontSize: 12 }}
                        tickFormatter={(value) => {
                          const [year, month] = value.split('-');
                          return `${month}/${year.slice(2)}`;
                        }}
                      />
                      <YAxis tick={{ fontSize: 12 }} />
                      <Tooltip
                        labelFormatter={(value) => {
                          const [year, month] = value.split('-');
                          const monthNames = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
                          return `${monthNames[parseInt(month) - 1]} ${year}`;
                        }}
                        formatter={(value) => [value, 'Yeni Organizasyon']}
                      />
                      <Bar
                        dataKey="count"
                        fill="#f59e0b"
                        radius={[4, 4, 0, 0]}
                      />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Henüz veri bulunmuyor</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Organizasyonlar oluşturuldukça grafik görünecek
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Additional Statistics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Hizmet İstatistikleri</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Toplam Hizmet</span>
                  <span className="font-semibold">{stats.totalServices}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Ortalama Hizmet/Org.</span>
                  <span className="font-semibold">
                    {stats.totalOrganizations > 0 
                      ? (stats.totalServices / stats.totalOrganizations).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Ortalama Gelir/Org.</span>
                  <span className="font-semibold">
                    {stats.totalOrganizations > 0 
                      ? formatCurrency(stats.totalRevenue / stats.totalOrganizations)
                      : formatCurrency(0)
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Kullanıcı Aktivitesi</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Aktiflik Oranı</span>
                  <span className="font-semibold">
                    {stats.totalUsers > 0 
                      ? `${((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}%`
                      : '0%'
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Org./Kullanıcı</span>
                  <span className="font-semibold">
                    {stats.totalUsers > 0 
                      ? (stats.totalOrganizations / stats.totalUsers).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Gelir/Kullanıcı</span>
                  <span className="font-semibold">
                    {stats.totalUsers > 0 
                      ? formatCurrency(stats.totalRevenue / stats.totalUsers)
                      : formatCurrency(0)
                    }
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Bu Ay Performans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Yeni Kullanıcı</span>
                  <span className="font-semibold text-green-600">
                    +{stats.newUsersThisMonth}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Yeni Organizasyon</span>
                  <span className="font-semibold text-blue-600">
                    +{stats.organizationsThisMonth}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Bu Ay Gelir</span>
                  <span className="font-semibold text-purple-600">
                    {formatCurrency(stats.revenueThisMonth)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}
