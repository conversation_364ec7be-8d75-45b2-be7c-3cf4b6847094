using System.ComponentModel.DataAnnotations;

namespace EventFlow.Domain.Entities;

public class Service : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Base price must be non-negative")]
    public decimal BasePrice { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<OrganizationService> OrganizationServices { get; set; } = new List<OrganizationService>();
}
