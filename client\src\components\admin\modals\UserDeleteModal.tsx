import React, { useEffect, useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AdminUser } from "../../../types";
import { Trash2, AlertTriangle, Building, Briefcase, User, Database, Loader2 } from "lucide-react";
import { adminApi } from "@/lib/adminApi";

interface DeletionPreview {
  success: boolean;
  user: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    company_name?: string;
    created_at: string;
  };
  will_delete: {
    organizations: number;
    services: number;
    profile: number;
    auth_record: number;
  };
}

interface UserDeleteModalProps {
  user: AdminUser | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function UserDeleteModal({
  user,
  open,
  onOpenChange,
  onConfirm,
  isLoading = false,
}: UserDeleteModalProps) {
  const [preview, setPreview] = useState<DeletionPreview | null>(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);

  // Load deletion preview when modal opens
  useEffect(() => {
    if (open && user) {
      loadDeletionPreview();
    }
  }, [open, user]);

  const loadDeletionPreview = async () => {
    if (!user) return;

    setPreviewLoading(true);
    setPreviewError(null);

    try {
      const previewData = await adminApi.users.getDeletionPreview(user.id);
      setPreview(previewData);
    } catch (error) {
      console.error('❌ Error loading deletion preview:', error);
      setPreviewError((error as Error).message);
    } finally {
      setPreviewLoading(false);
    }
  };

  if (!user) return null;

  const userName = user.first_name && user.last_name 
    ? `${user.first_name} ${user.last_name}` 
    : user.email;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="w-5 h-5" />
            Kullanıcıyı Sil
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-amber-600">
                <AlertTriangle className="w-4 h-4" />
                <span className="font-medium">Bu işlem geri alınamaz!</span>
              </div>

              <div>
                <strong>{user.first_name && user.last_name
                  ? `${user.first_name} ${user.last_name}`
                  : user.email
                }</strong> kullanıcısını ve tüm ilişkili verilerini kalıcı olarak silmek istediğinizden emin misiniz?
              </div>

              {previewLoading && (
                <div className="flex items-center gap-2 text-blue-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Silinecek veriler kontrol ediliyor...</span>
                </div>
              )}

              {previewError && (
                <div className="bg-red-50 p-3 rounded-lg text-sm text-red-700">
                  <div className="font-semibold mb-1">Hata:</div>
                  <div>{previewError}</div>
                </div>
              )}

              {preview?.success && (
                <div className="bg-red-50 p-4 rounded-lg text-sm">
                  <div className="font-semibold mb-3 text-red-800 flex items-center gap-2">
                    <Database className="w-4 h-4" />
                    Silinecek veriler:
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-red-700">
                      <Building className="w-4 h-4" />
                      <span>{preview.will_delete.organizations} organizasyon</span>
                    </div>
                    <div className="flex items-center gap-2 text-red-700">
                      <Briefcase className="w-4 h-4" />
                      <span>{preview.will_delete.services} hizmet</span>
                    </div>
                    <div className="flex items-center gap-2 text-red-700">
                      <User className="w-4 h-4" />
                      <span>Kullanıcı profili ve hesap bilgileri</span>
                    </div>
                  </div>

                  {(preview.will_delete.organizations > 0 || preview.will_delete.services > 0) && (
                    <div className="mt-3 p-2 bg-red-100 rounded border-l-4 border-red-400">
                      <div className="text-red-800 font-medium text-xs">
                        ⚠️ Bu kullanıcının oluşturduğu tüm organizasyonlar ve hizmetler de silinecektir!
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            İptal
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Siliniyor...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                Evet, Sil
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
