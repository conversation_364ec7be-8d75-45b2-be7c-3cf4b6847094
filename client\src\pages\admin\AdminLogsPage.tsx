import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  FileText,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  User,
  Activity,
  AlertCircle,
  Trash2,
  Eye,
  X
} from 'lucide-react';

export default function AdminLogsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedLog, setSelectedLog] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const pageSize = 50;

  // Fetch system logs
  const { data: logs, isLoading, error, refetch } = useQuery({
    queryKey: ['admin', 'logs', currentPage, pageSize],
    queryFn: () => adminApi.logs.getAll(pageSize, currentPage * pageSize),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Clear all logs mutation
  const clearLogsMutation = useMutation({
    mutationFn: adminApi.logs.clearAll,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'logs'] });
      setShowClearDialog(false);
      toast({
        title: "✅ Loglar Temizlendi",
        description: "Tüm sistem logları başarıyla temizlendi.",
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "❌ Temizleme Hatası",
        description: 'Loglar temizlenirken hata oluştu: ' + error.message,
        variant: "destructive",
      });
    },
  });

  // Filter logs based on search and action filter
  const filteredLogs = logs?.filter(log => {
    const matchesSearch = searchTerm === '' || 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesAction = actionFilter === 'all' || log.action === actionFilter;
    
    return matchesSearch && matchesAction;
  }) || [];

  const getActionBadgeVariant = (action: string) => {
    if (action.includes('create')) return 'default';
    if (action.includes('update')) return 'secondary';
    if (action.includes('delete')) return 'destructive';
    if (action.includes('login') || action.includes('signin')) return 'outline';
    return 'secondary';
  };

  const getActionIcon = (action: string) => {
    if (action.includes('create')) return '➕';
    if (action.includes('update')) return '✏️';
    if (action.includes('delete')) return '🗑️';
    if (action.includes('login') || action.includes('signin')) return '🔐';
    if (action.includes('admin')) return '👑';
    return '📝';
  };

  const handleExportLogs = () => {
    // TODO: Implement log export functionality
    console.log('Export logs');
  };

  const handleViewDetails = (log: any) => {
    setSelectedLog(log);
    setShowDetailsModal(true);
  };

  const handleClearLogs = () => {
    setShowClearDialog(true);
  };

  const confirmClearLogs = () => {
    clearLogsMutation.mutate();
  };

  return (
    <AdminLayout
      title="Sistem Logları"
      subtitle="Sistem aktivitelerini ve kullanıcı işlemlerini görüntüle"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Log ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Eylem filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Eylemler</SelectItem>
                <SelectItem value="create_admin_user">Admin Oluştur</SelectItem>
                <SelectItem value="update_admin_user">Admin Güncelle</SelectItem>
                <SelectItem value="delete_admin_user">Admin Sil</SelectItem>
                <SelectItem value="update_system_setting">Ayar Güncelle</SelectItem>
                <SelectItem value="admin_panel_setup">Panel Kurulum</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Yenile
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportLogs}>
              <Download className="h-4 w-4 mr-2" />
              Dışa Aktar
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleClearLogs}
              disabled={clearLogsMutation.isPending}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {clearLogsMutation.isPending ? 'Temizleniyor...' : 'Tüm Logları Temizle'}
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Log</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{logs?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bugün</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {logs?.filter(log => {
                  const today = new Date().toDateString();
                  const logDate = new Date(log.created_at).toDateString();
                  return today === logDate;
                }).length || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aktif Kullanıcı</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(logs?.map(log => log.user_id).filter(Boolean)).size || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Hata Sayısı</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {logs?.filter(log => log.action.includes('error') || log.action.includes('fail')).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Logs Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Sistem Logları
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600 mb-2">Loglar yüklenirken hata oluştu</p>
                <p className="text-sm text-gray-500 mb-4">
                  {error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu'}
                </p>
                <p className="text-xs text-gray-400 mb-4">
                  Veritabanı ilişkileri eksik olabilir. database-fixes.sql dosyasını çalıştırın.
                </p>
                <Button variant="outline" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Tekrar Dene
                </Button>
              </div>
            ) : filteredLogs.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {searchTerm || actionFilter !== 'all' 
                    ? 'Filtrelere uygun log bulunamadı' 
                    : 'Henüz sistem logu bulunmuyor'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tarih/Saat</TableHead>
                      <TableHead>Kullanıcı</TableHead>
                      <TableHead>Eylem</TableHead>
                      <TableHead>Kaynak</TableHead>
                      <TableHead>Detaylar</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">
                              {new Date(log.created_at).toLocaleDateString('tr-TR')}
                            </div>
                            <div className="text-gray-500">
                              {new Date(log.created_at).toLocaleTimeString('tr-TR')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">
                              {log.user_name || 'Sistem'}
                            </div>
                            <div className="text-gray-500">
                              {log.user_email || '-'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getActionIcon(log.action)}</span>
                            <Badge variant={getActionBadgeVariant(log.action)}>
                              {log.action}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {log.resource_type || '-'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {log.details && Object.keys(log.details).length > 0 ? (
                              <>
                                <div className="text-sm max-w-[200px] truncate">
                                  {JSON.stringify(log.details).substring(0, 50)}...
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleViewDetails(log)}
                                  className="h-6 px-2"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </>
                            ) : (
                              <span className="text-sm text-gray-500">-</span>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {filteredLogs.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {filteredLogs.length} log gösteriliyor
            </p>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                disabled={currentPage === 0}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Önceki
              </Button>
              <span className="text-sm">
                Sayfa {currentPage + 1}
              </span>
              <Button 
                variant="outline" 
                size="sm"
                disabled={filteredLogs.length < pageSize}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Sonraki
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Log Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Log Detayları
            </DialogTitle>
            <DialogDescription>
              Sistem log kaydının detaylı bilgileri
            </DialogDescription>
          </DialogHeader>
          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Tarih</label>
                  <p className="text-sm">
                    {new Date(selectedLog.created_at).toLocaleString('tr-TR')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Kullanıcı</label>
                  <p className="text-sm">
                    {selectedLog.user_name || selectedLog.user_email || 'Sistem'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Eylem</label>
                  <Badge variant="outline" className="text-xs">
                    {selectedLog.action}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Kaynak Türü</label>
                  <p className="text-sm">{selectedLog.resource_type || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">IP Adresi</label>
                  <p className="text-sm">{selectedLog.ip_address || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">User Agent</label>
                  <p className="text-sm text-xs break-all">
                    {selectedLog.user_agent || '-'}
                  </p>
                </div>
              </div>

              {selectedLog.details && Object.keys(selectedLog.details).length > 0 && (
                <div>
                  <label className="text-sm font-medium text-gray-500 mb-2 block">
                    Detaylar
                  </label>
                  <div className="bg-gray-50 rounded-lg p-4 border">
                    <pre className="text-xs whitespace-pre-wrap break-words">
                      {JSON.stringify(selectedLog.details, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Clear Logs Confirmation Dialog */}
      <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              Tüm Logları Temizle
            </AlertDialogTitle>
            <AlertDialogDescription>
              <strong>Tüm sistem loglarını</strong> kalıcı olarak silmek istediğinizden emin misiniz?
              <br /><br />
              <span className="text-red-600 font-medium">
                ⚠️ Bu işlem geri alınamaz ve tüm log geçmişi kaybolacak.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={clearLogsMutation.isPending}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmClearLogs}
              disabled={clearLogsMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {clearLogsMutation.isPending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Temizleniyor...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Evet, Tümünü Temizle
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
