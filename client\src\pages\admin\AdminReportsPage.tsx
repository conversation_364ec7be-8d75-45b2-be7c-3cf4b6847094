import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  Download,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  FileText,
  PieChart,
  Activity,
  Clock
} from 'lucide-react';

const reportTypes = [
  {
    id: 'user-activity',
    title: 'Kullanıcı Aktivite Raporu',
    description: 'Kullanıcı giriş/çıkış ve aktivite detayları',
    icon: Users,
    color: 'bg-blue-500',
    estimatedTime: '2-3 dakika'
  },
  {
    id: 'revenue-report',
    title: '<PERSON><PERSON><PERSON> Raporu',
    description: 'Aylık ve yıllık gelir analizi',
    icon: DollarSign,
    color: 'bg-green-500',
    estimatedTime: '1-2 dakika'
  },
  {
    id: 'organization-stats',
    title: 'Organizasyon İstatistikleri',
    description: 'Organizasyon oluşturma ve katılım verileri',
    icon: Calendar,
    color: 'bg-purple-500',
    estimatedTime: '3-4 dakika'
  },
  {
    id: 'system-performance',
    title: 'Sistem Performans Raporu',
    description: 'Sistem kullanımı ve performans metrikleri',
    icon: Activity,
    color: 'bg-orange-500',
    estimatedTime: '2-3 dakika'
  },
  {
    id: 'user-growth',
    title: 'Kullanıcı Büyüme Analizi',
    description: 'Kullanıcı kayıt trendleri ve büyüme oranları',
    icon: TrendingUp,
    color: 'bg-indigo-500',
    estimatedTime: '1-2 dakika'
  },
  {
    id: 'service-usage',
    title: 'Hizmet Kullanım Raporu',
    description: 'En çok kullanılan hizmetler ve kategoriler',
    icon: PieChart,
    color: 'bg-pink-500',
    estimatedTime: '2-3 dakika'
  }
];

const quickStats = [
  {
    title: 'Bu Ay Oluşturulan Raporlar',
    value: '24',
    icon: FileText,
    change: '+12%',
    changeType: 'positive' as const
  },
  {
    title: 'Toplam İndirme',
    value: '156',
    icon: Download,
    change: '+8%',
    changeType: 'positive' as const
  },
  {
    title: 'Ortalama Oluşturma Süresi',
    value: '2.3 dk',
    icon: Clock,
    change: '-15%',
    changeType: 'positive' as const
  },
  {
    title: 'Aktif Rapor Türü',
    value: '6',
    icon: BarChart3,
    change: '0%',
    changeType: 'neutral' as const
  }
];

export default function AdminReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('last-30-days');
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [generatingReports, setGeneratingReports] = useState<string[]>([]);

  const handleGenerateReport = async (reportId: string) => {
    setGeneratingReports(prev => [...prev, reportId]);
    
    // Simulate report generation
    setTimeout(() => {
      setGeneratingReports(prev => prev.filter(id => id !== reportId));
      // Here you would typically trigger the actual download
      console.log(`Generated report: ${reportId}`);
    }, 3000);
  };

  const isGenerating = (reportId: string) => generatingReports.includes(reportId);

  return (
    <AdminLayout
      title="Raporlar"
      subtitle="Sistem raporları oluştur ve indir"
    >
      <div className="space-y-6">
        {/* Header Controls */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Zaman Aralığı
              </label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-7-days">Son 7 Gün</SelectItem>
                  <SelectItem value="last-30-days">Son 30 Gün</SelectItem>
                  <SelectItem value="last-3-months">Son 3 Ay</SelectItem>
                  <SelectItem value="last-6-months">Son 6 Ay</SelectItem>
                  <SelectItem value="last-year">Son 1 Yıl</SelectItem>
                  <SelectItem value="all-time">Tüm Zamanlar</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Format
              </label>
              <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF</SelectItem>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickStats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs mt-1">
                  <span className={`${
                    stat.changeType === 'positive' ? 'text-green-600' : 
                    stat.changeType === 'negative' ? 'text-red-600' : 
                    'text-gray-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-gray-600 ml-1">önceki aya göre</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Report Types */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportTypes.map((report) => (
            <Card key={report.id} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 ${report.color} rounded-lg flex items-center justify-center`}>
                    <report.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{report.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600 text-sm">
                  {report.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{report.estimatedTime}</span>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {selectedFormat.toUpperCase()}
                  </Badge>
                </div>
                
                <Button 
                  className="w-full" 
                  onClick={() => handleGenerateReport(report.id)}
                  disabled={isGenerating(report.id)}
                >
                  {isGenerating(report.id) ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Oluşturuluyor...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4 mr-2" />
                      Rapor Oluştur
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Son Oluşturulan Raporlar
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                {
                  name: 'Kullanıcı Aktivite Raporu - Aralık 2024',
                  type: 'PDF',
                  size: '2.4 MB',
                  date: '2 saat önce',
                  status: 'completed'
                },
                {
                  name: 'Gelir Raporu - Q4 2024',
                  type: 'Excel',
                  size: '1.8 MB',
                  date: '5 saat önce',
                  status: 'completed'
                },
                {
                  name: 'Organizasyon İstatistikleri - Kasım 2024',
                  type: 'PDF',
                  size: '3.1 MB',
                  date: '1 gün önce',
                  status: 'completed'
                },
                {
                  name: 'Sistem Performans Raporu',
                  type: 'CSV',
                  size: '856 KB',
                  date: '2 gün önce',
                  status: 'completed'
                }
              ].map((report, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">{report.name}</p>
                      <p className="text-xs text-gray-600">
                        {report.type} • {report.size} • {report.date}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {report.status === 'completed' ? 'Tamamlandı' : 'İşleniyor'}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
