# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["EventFlow.API/EventFlow.API.csproj", "EventFlow.API/"]
COPY ["EventFlow.Application/EventFlow.Application.csproj", "EventFlow.Application/"]
COPY ["EventFlow.Domain/EventFlow.Domain.csproj", "EventFlow.Domain/"]
COPY ["EventFlow.Infrastructure/EventFlow.Infrastructure.csproj", "EventFlow.Infrastructure/"]
COPY ["EventFlow.Shared/EventFlow.Shared.csproj", "EventFlow.Shared/"]

RUN dotnet restore "EventFlow.API/EventFlow.API.csproj"

# Copy the rest of the source code
COPY . .

# Build the application
WORKDIR "/src/EventFlow.API"
RUN dotnet build "EventFlow.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "EventFlow.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Use the official .NET 8 runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Copy the published application
COPY --from=publish /app/publish .

# Expose the port
EXPOSE 8080

# Set environment variables
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

# Start the application
ENTRYPOINT ["dotnet", "EventFlow.API.dll"]
