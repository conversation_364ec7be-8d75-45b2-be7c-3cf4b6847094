# Organizasyon Defteri - Organizasyon Yönetim Sistemi

Modern ve kullanıcı dostu arayüz ile etkinlik ve organizasyon yönetimini kolaylaştıran web uygulaması.

## 🚀 Demo

**Frontend:** [https://bespoke-zabaione-dd3853.netlify.app](https://bespoke-zabaione-dd3853.netlify.app)

## 📋 Özellikler

- 📅 Etkinlik ve organizasyon yönetimi
- 🎯 Hizmet katalogu yönetimi
- 📊 Dashboard ve raporlama
- 📱 Responsive tasarım
- 🔐 Gü<PERSON>li kullanıcı yönetimi

## 🛠️ Teknolojiler

### Frontend
- **React 18** - Modern UI framework
- **TypeScript** - Type safety
- **Vite** - Hızlı build tool
- **Tailwind CSS** - Utility-first CSS
- **Radix UI** - Accessible UI components
- **React Query** - Server state management
- **React Hook Form** - Form yönetimi

### Backend
- **.NET Core** - Web API
- **Entity Framework** - ORM
- **PostgreSQL** - Database

## 🚀 Kurulum

### Frontend (Development)

```bash
# Bağımlılıkları yükle
npm install

# Development server'ı başlat
npm run dev
```

### Production Build

```bash
# Production build
npm run build

# Netlify için build
npm run build:netlify
```

## 🌐 Netlify Deployment

### Otomatik Deployment

1. **GitHub Repository'yi Netlify'a bağlayın**
2. **Build ayarları:**
   - Build command: `npm run build:netlify`
   - Publish directory: `dist/public`
   - Functions directory: `netlify/functions`

### Environment Variables

Netlify dashboard'da aşağıdaki environment variable'ları ekleyin:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Alternative names (for compatibility)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Manuel Deployment

```bash
# Build projeyi
npm run build:netlify

# Netlify CLI ile deploy et
npx netlify deploy --prod --dir=dist/public

# Build dosyalarını serve et
npm run preview
```

## 🌐 Deployment

### Frontend (Netlify)

1. GitHub'a push edin
2. Netlify'de projeyi bağlayın
3. Build ayarları:
   - **Build command:** `npm run build`
   - **Publish directory:** `dist/public`

### Backend Deployment Seçenekleri

#### 1. Railway (Önerilen)
```bash
# Railway CLI yükle
npm install -g @railway/cli

# Login
railway login

# Deploy
railway deploy
```

#### 2. Render
1. GitHub repo'yu Render'a bağlayın
2. .NET Core service olarak deploy edin

#### 3. Azure App Service
1. Azure portal'da App Service oluşturun
2. GitHub Actions ile CI/CD kurun

## 🔧 Environment Variables

### Frontend (.env)
```bash
# Backend URL (production)
VITE_API_BASE_URL=https://your-backend-url.com

# Development backend URL
VITE_DEV_API_BASE_URL=http://localhost:5153
```

### Backend
```bash
# Database connection
DATABASE_URL=your_postgresql_connection_string

# JWT settings
JWT_SECRET=your_jwt_secret
JWT_ISSUER=EventFlow
JWT_AUDIENCE=EventFlow

# CORS settings
CORS_ORIGINS=https://bespoke-zabaione-dd3853.netlify.app
```

## 📝 API Endpoints

```
GET    /api/organizations     - Tüm organizasyonları listele
POST   /api/organizations     - Yeni organizasyon oluştur
GET    /api/organizations/:id - Organizasyon detayı
PUT    /api/organizations/:id - Organizasyon güncelle
DELETE /api/organizations/:id - Organizasyon sil

GET    /api/services          - Tüm hizmetleri listele
POST   /api/services          - Yeni hizmet oluştur
PUT    /api/services/:id      - Hizmet güncelle
DELETE /api/services/:id      - Hizmet sil

GET    /api/organizations/calendar - Takvim etkinlikleri
```

## 🧪 Test

```bash
# Frontend testleri
npm run test

# Backend testleri (.NET)
cd EventFlow.Backend
dotnet test
```

## 🔧 Sorun Giderme

### CORS ve Tarayıcı Politikası Sorunları

Eğer hizmet ekleme, düzenleme veya silme işlemlerinde sorun yaşıyorsanız:

#### 1. Tarayıcı Cache'ini Temizleyin
```bash
# Hard refresh yapın
Ctrl + F5 (Windows) veya Cmd + Shift + R (Mac)

# Veya tarayıcı geliştirici araçlarında:
F12 → Network sekmesi → "Disable cache" işaretleyin
```

#### 2. Kullanıcı Yetkilendirmesini Kontrol Edin
- Sadece hizmet sahibi o hizmeti düzenleyebilir/silebilir
- Farklı kullanıcı hesabıyla giriş yapmış olabilirsiniz

#### 3. Tarayıcı Uyumluluğu
- Chrome 90+, Firefox 88+, Safari 14+ kullanın
- Eski tarayıcılar modern JavaScript özelliklerini desteklemeyebilir

#### 4. Network Bağlantısını Kontrol Edin
- İnternet bağlantınızın stabil olduğundan emin olun
- VPN kullanıyorsanız geçici olarak kapatmayı deneyin

#### 5. Console Hatalarını Kontrol Edin
```bash
# Tarayıcıda F12 → Console sekmesinde hata mesajlarını kontrol edin
# Kırmızı hata mesajları varsa bunları not alın
```

## 📁 Proje Yapısı

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/         # Page components
│   │   ├── lib/           # Utilities & API
│   │   └── types/         # TypeScript types
│   └── public/            # Static files
├── EventFlow.Backend/     # .NET Core backend (Organizasyon Defteri)
│   ├── EventFlow.API/     # Web API
│   ├── EventFlow.Domain/  # Domain models
│   └── EventFlow.Infrastructure/ # Data access
├── server/                # Node.js proxy (development)
└── shared/                # Shared types
```

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
