import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Activity,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  User,
  TrendingUp,
  Clock,
  AlertCircle,
  Trash2
} from 'lucide-react';

export default function AdminActivitiesPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [activityFilter, setActivityFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(0);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const pageSize = 50;

  // Fetch user activities
  const { data: activities, isLoading, error, refetch } = useQuery({
    queryKey: ['admin', 'activities', currentPage, pageSize],
    queryFn: () => adminApi.userActivities.getAll(pageSize, currentPage * pageSize),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Clear all activities mutation
  const clearActivitiesMutation = useMutation({
    mutationFn: adminApi.userActivities.clearAll,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'userActivities'] });
      setShowClearDialog(false);
      toast({
        title: "✅ Aktiviteler Temizlendi",
        description: "Tüm kullanıcı aktiviteleri başarıyla temizlendi.",
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "❌ Temizleme Hatası",
        description: 'Aktiviteler temizlenirken hata oluştu: ' + error.message,
        variant: "destructive",
      });
    },
  });

  // Filter activities based on search and activity filter
  const filteredActivities = activities?.filter(activity => {
    const matchesSearch = searchTerm === '' || 
      activity.activity_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.user_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.user_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesActivity = activityFilter === 'all' || activity.activity_type === activityFilter;
    
    return matchesSearch && matchesActivity;
  }) || [];

  const getActivityBadgeVariant = (activityType: string) => {
    if (activityType.includes('login')) return 'default';
    if (activityType.includes('create')) return 'secondary';
    if (activityType.includes('update')) return 'outline';
    if (activityType.includes('delete')) return 'destructive';
    return 'secondary';
  };

  const getActivityIcon = (activityType: string) => {
    if (activityType.includes('login')) return '🔐';
    if (activityType.includes('create')) return '➕';
    if (activityType.includes('update')) return '✏️';
    if (activityType.includes('delete')) return '🗑️';
    if (activityType.includes('view')) return '👁️';
    if (activityType.includes('export')) return '📤';
    return '📝';
  };

  const handleExportActivities = () => {
    // TODO: Implement activity export functionality
    console.log('Export activities');
  };

  const handleClearActivities = () => {
    setShowClearDialog(true);
  };

  const confirmClearActivities = () => {
    clearActivitiesMutation.mutate();
  };

  return (
    <AdminLayout
      title="Kullanıcı Aktiviteleri"
      subtitle="Kullanıcı işlemlerini ve aktivitelerini izle"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex flex-1 items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Aktivite ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={activityFilter} onValueChange={setActivityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Aktivite filtrele" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Aktiviteler</SelectItem>
                <SelectItem value="login">Giriş</SelectItem>
                <SelectItem value="logout">Çıkış</SelectItem>
                <SelectItem value="create_organization">Organizasyon Oluştur</SelectItem>
                <SelectItem value="update_organization">Organizasyon Güncelle</SelectItem>
                <SelectItem value="create_service">Hizmet Oluştur</SelectItem>
                <SelectItem value="update_profile">Profil Güncelle</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Yenile
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportActivities}>
              <Download className="h-4 w-4 mr-2" />
              Dışa Aktar
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleClearActivities}
              disabled={clearActivitiesMutation.isPending}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {clearActivitiesMutation.isPending ? 'Temizleniyor...' : 'Aktiviteleri Temizle'}
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Aktivite</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activities?.length || 0}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bugün</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {activities?.filter(activity => {
                  const today = new Date().toDateString();
                  const activityDate = new Date(activity.created_at).toDateString();
                  return today === activityDate;
                }).length || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aktif Kullanıcı</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(activities?.map(activity => activity.user_id)).size || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Son 1 Saat</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {activities?.filter(activity => {
                  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
                  const activityDate = new Date(activity.created_at);
                  return activityDate > oneHourAgo;
                }).length || 0}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activities Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Kullanıcı Aktiviteleri
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <p className="text-red-600 mb-2">Aktiviteler yüklenirken hata oluştu</p>
                <p className="text-sm text-gray-500 mb-4">
                  {error instanceof Error ? error.message : 'Bilinmeyen bir hata oluştu'}
                </p>
                <p className="text-xs text-gray-400 mb-4">
                  Veritabanı ilişkileri eksik olabilir. database-fixes.sql dosyasını çalıştırın.
                </p>
                <Button variant="outline" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Tekrar Dene
                </Button>
              </div>
            ) : filteredActivities.length === 0 ? (
              <div className="text-center py-8">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {searchTerm || activityFilter !== 'all' 
                    ? 'Filtrelere uygun aktivite bulunamadı' 
                    : 'Henüz kullanıcı aktivitesi bulunmuyor'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tarih/Saat</TableHead>
                      <TableHead>Kullanıcı</TableHead>
                      <TableHead>Aktivite</TableHead>
                      <TableHead>Açıklama</TableHead>
                      <TableHead>Metadata</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredActivities.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">
                              {new Date(activity.created_at).toLocaleDateString('tr-TR')}
                            </div>
                            <div className="text-gray-500">
                              {new Date(activity.created_at).toLocaleTimeString('tr-TR')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                              <span className="text-white text-sm font-semibold">
                                {activity.user_name?.charAt(0)?.toUpperCase() || 
                                 activity.user_email?.charAt(0)?.toUpperCase() || 'U'}
                              </span>
                            </div>
                            <div className="text-sm">
                              <div className="font-medium">
                                {activity.user_name || 'Bilinmeyen Kullanıcı'}
                              </div>
                              <div className="text-gray-500">
                                {activity.user_email || '-'}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getActivityIcon(activity.activity_type)}</span>
                            <Badge variant={getActivityBadgeVariant(activity.activity_type)}>
                              {activity.activity_type}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {activity.description || '-'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm max-w-xs truncate">
                            {activity.metadata && Object.keys(activity.metadata).length > 0 
                              ? JSON.stringify(activity.metadata)
                              : '-'
                            }
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {filteredActivities.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {filteredActivities.length} aktivite gösteriliyor
            </p>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                disabled={currentPage === 0}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                Önceki
              </Button>
              <span className="text-sm">
                Sayfa {currentPage + 1}
              </span>
              <Button 
                variant="outline" 
                size="sm"
                disabled={filteredActivities.length < pageSize}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Sonraki
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Clear Activities Confirmation Dialog */}
      <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              Tüm Aktiviteleri Temizle
            </AlertDialogTitle>
            <AlertDialogDescription>
              <strong>Tüm kullanıcı aktivitelerini</strong> kalıcı olarak silmek istediğinizden emin misiniz?
              <br /><br />
              <span className="text-red-600 font-medium">
                ⚠️ Bu işlem geri alınamaz ve tüm aktivite geçmişi kaybolacak.
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={clearActivitiesMutation.isPending}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmClearActivities}
              disabled={clearActivitiesMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {clearActivitiesMutation.isPending ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Temizleniyor...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Evet, Tümünü Temizle
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
