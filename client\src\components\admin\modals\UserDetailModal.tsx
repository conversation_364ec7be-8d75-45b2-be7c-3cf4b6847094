import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AdminUser } from "../../../types";
import { UserWithStats } from "../../../lib/adminApi";
import { 
  User, 
  Mail, 
  Building, 
  Calendar, 
  Clock, 
  Shield,
  Phone,
  Globe
} from "lucide-react";

interface UserDetailModalProps {
  user: AdminUser | UserWithStats | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserDetailModal({
  user,
  open,
  onOpenChange,
}: UserDetailModalProps) {
  if (!user) return null;

  const getStatusBadge = (user: AdminUser | UserWithStats) => {
    console.log('🔍 getStatusBadge - user.email_confirmed_at:', user.email_confirmed_at);
    if (user.email_confirmed_at) {
      return <Badge variant="default">Doğrulanmış</Badge>;
    }
    return <Badge variant="secondary">Doğrulanmamış</Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Kullanıcı Detayları
          </DialogTitle>
          <DialogDescription>
            {user.first_name && user.last_name
              ? `${user.first_name} ${user.last_name}`
              : user.email.split('@')[0]
            } - Detaylı Bilgiler
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Temel Bilgiler */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <User className="w-5 h-5" />
              Temel Bilgiler
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Ad Soyad</p>
                  <p className="font-medium">
                    {user.first_name && user.last_name 
                      ? `${user.first_name} ${user.last_name}` 
                      : 'Belirtilmemiş'
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">E-posta</p>
                  <p className="font-medium">{user.email}</p>
                </div>
              </div>

              {user.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Telefon</p>
                    <p className="font-medium">{user.phone}</p>
                  </div>
                </div>
              )}

              {user.company_name && (
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Şirket</p>
                    <p className="font-medium">{user.company_name}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Hesap Durumu */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Hesap Durumu
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Durum</p>
                <div className="mt-1">{getStatusBadge(user)}</div>
              </div>
              
              {user.role && (
                <div>
                  <p className="text-sm text-gray-500">Rol</p>
                  <Badge variant="outline" className="mt-1">{user.role}</Badge>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Tarih Bilgileri */}
          <div>
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Tarih Bilgileri
            </h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Kayıt Tarihi</p>
                  <p className="font-medium">{formatDate(user.created_at)}</p>
                </div>
              </div>

              {user.email_confirmed_at && (
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email Doğrulama</p>
                    <p className="font-medium">{formatDate(user.email_confirmed_at)}</p>
                  </div>
                </div>
              )}

              {user.last_sign_in_at && (
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Son Giriş</p>
                    <p className="font-medium">{formatDate(user.last_sign_in_at)}</p>
                  </div>
                </div>
              )}

              {user.updated_at && (
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Son Güncelleme</p>
                    <p className="font-medium">{formatDate(user.updated_at)}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Kullanıcı ID */}
          <Separator />
          <div className="text-sm text-gray-500">
            <p><strong>Kullanıcı ID:</strong> {user.id}</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
