import React, { useState } from 'react';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminApi } from '../../lib/adminApi';
import { securityHelper } from '../../lib/securityHelper';
import { supabase } from '../../lib/supabase';
import {
  Shield,
  AlertTriangle,
  Users,
  Globe,
  RefreshCw,
  Trash2,
  Eye,
  Lock,
  Activity,
  Clock,
  Unlock,
  Ban
} from 'lucide-react';

export default function AdminSecurityPage() {
  const [showDebug, setShowDebug] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch security stats from Supabase
  const { data: stats, isLoading, error, refetch } = useQuery({
    queryKey: ['admin', 'security', 'stats'],
    queryFn: adminApi.security.getSecurityStats,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch locked accounts from database
  const { data: lockedAccounts = [], refetch: refetchLocked } = useQuery({
    queryKey: ['admin', 'security', 'locked-accounts'],
    queryFn: adminApi.security.getLockedAccounts,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch rate limited IPs from database
  const { data: rateLimitedIPs = [], refetch: refetchIPs } = useQuery({
    queryKey: ['admin', 'security', 'rate-limited-ips'],
    queryFn: adminApi.security.getRateLimitedIPs,
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Clean expired locks periodically
  React.useEffect(() => {
    const cleanExpired = async () => {
      try {
        console.log('🧹 Running automatic cleanup of expired locks...');

        // Use database function for cleanup
        const { data, error } = await supabase.rpc('cleanup_expired_locks');

        if (error) {
          console.error('❌ Cleanup error:', error);
        } else {
          console.log('✅ Cleanup completed:', data);

          // Show notification if locks were cleaned
          if (data?.expired_accounts > 0 || data?.expired_ips > 0) {
            toast({
              title: "🧹 Otomatik Temizlik",
              description: `${data.expired_accounts} hesap kilidi ve ${data.expired_ips} IP kısıtlaması otomatik olarak kaldırıldı`,
              variant: "success",
            });
          }
        }

        // Refresh data
        await refetchLocked();
        await refetchIPs();
      } catch (error) {
        console.error('Failed to clean expired locks:', error);
      }
    };

    // Clean on mount and every 2 minutes
    cleanExpired();
    const interval = setInterval(cleanExpired, 2 * 60 * 1000);
    return () => clearInterval(interval);
  }, [refetchLocked, refetchIPs, toast]);

  // Unlock account mutation
  const unlockAccountMutation = useMutation({
    mutationFn: async (email: string) => {
      console.log('🔓 Unlocking account:', email);

      // Clear from database
      await adminApi.security.unlockAccount(email);

      // Clear from localStorage
      securityHelper.unlockAccount(email);

      console.log('✅ Account unlocked from both database and localStorage');
    },
    onSuccess: async (_, email) => {
      await refetchLocked();
      toast({
        title: "✅ Hesap Kilidi Kaldırıldı",
        description: `${email} hesabının kilidi kaldırıldı. Kullanıcı artık giriş yapabilir.`,
        variant: "success",
      });
    },
    onError: (error: any) => {
      console.error('❌ Unlock error:', error);
      toast({
        title: "❌ Hata",
        description: error.message || "Hesap kilidi kaldırılırken hata oluştu",
        variant: "destructive",
      });
    }
  });

  // Remove IP limit mutation
  const removeIPLimitMutation = useMutation({
    mutationFn: async (ip: string) => {
      await adminApi.security.removeIPRateLimit(ip);
      securityHelper.removeIPLimit(ip); // Also clear from localStorage
    },
    onSuccess: async (_, ip) => {
      await refetchIPs();
      toast({
        title: "✅ IP Kısıtlaması Kaldırıldı",
        description: `${ip} IP adresinin kısıtlaması kaldırıldı`,
        variant: "success",
      });
    },
    onError: (error: any) => {
      toast({
        title: "❌ Hata",
        description: error.message || "IP kısıtlaması kaldırılırken hata oluştu",
        variant: "destructive",
      });
    }
  });

  const refreshStats = async () => {
    await refetch();
    await refetchLocked();
    await refetchIPs();
    await adminApi.security.cleanExpiredLocks();
    toast({
      title: "✅ Güncellendi",
      description: "Güvenlik verileri yenilendi",
      variant: "success",
    });
  };

  const clearAllAttempts = async () => {
    if (confirm('Tüm güvenlik kayıtları silinecek (giriş denemeleri, kilitli hesaplar, IP kısıtlamaları). Bu işlem geri alınamaz. Emin misiniz?')) {
      try {
        // Clear login attempts
        await adminApi.security.clearOldAttempts(0);

        // Clear all locked accounts
        await supabase
          .from('locked_accounts')
          .update({ is_active: false })
          .eq('is_active', true);

        // Clear all rate limited IPs
        await supabase
          .from('rate_limited_ips')
          .update({ is_active: false })
          .eq('is_active', true);

        // Also clear localStorage
        securityHelper.clearAllAttempts();

        // Refresh all data
        await refetch();
        await refetchLocked();
        await refetchIPs();

        toast({
          title: "✅ Temizlendi",
          description: "Tüm güvenlik kayıtları silindi",
          variant: "success",
        });
      } catch (error: any) {
        toast({
          title: "❌ Hata",
          description: error.message || "Kayıtlar silinirken hata oluştu",
          variant: "destructive",
        });
      }
    }
  };

  const getSecurityLevel = () => {
    const activeThreats = lockedAccounts.length + rateLimitedIPs.length;

    if (activeThreats > 5) return { level: 'Yüksek Risk', color: 'destructive', icon: AlertTriangle };
    if (activeThreats > 2) return { level: 'Orta Risk', color: 'warning', icon: Eye };
    return { level: 'Düşük Risk', color: 'success', icon: Shield };
  };

  const securityLevel = getSecurityLevel();
  const SecurityIcon = securityLevel.icon;

  if (isLoading) {
    return (
      <AdminLayout title="Güvenlik Yönetimi" subtitle="Yükleniyor...">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-gray-400" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Güvenlik Yönetimi"
      subtitle="Brute force koruması ve güvenlik istatistikleri"
    >
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant={securityLevel.color as any} className="flex items-center gap-1">
              <SecurityIcon className="w-3 h-3" />
              {securityLevel.level}
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshStats}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Yenile
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDebug(!showDebug)}
              className="flex items-center gap-2"
            >
              <Eye className="w-4 h-4" />
              Debug
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={clearAllAttempts}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Kayıtları Temizle
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Kilitli Hesaplar</CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{lockedAccounts.length}</div>
              <p className="text-xs text-muted-foreground">
                Aktif hesap kilitleri
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Kısıtlı IP'ler</CardTitle>
              <Ban className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{rateLimitedIPs.length}</div>
              <p className="text-xs text-muted-foreground">
                Rate limit uygulanmış
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Deneme</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalAttempts || 0}</div>
              <p className="text-xs text-muted-foreground">
                Son 24 saat içinde
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Locked Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Kilitli Hesaplar ({lockedAccounts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {lockedAccounts.length === 0 ? (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <p className="text-gray-500">Şu anda kilitli hesap bulunmuyor</p>
              </div>
            ) : (
              <div className="space-y-3">
                {lockedAccounts.map((account, index) => {
                  const remainingMinutes = Math.ceil((new Date(account.locked_until).getTime() - Date.now()) / 1000 / 60);
                  return (
                    <div
                      key={account.id}
                      className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <Lock className="h-4 w-4 text-red-600" />
                        <div>
                          <p className="font-medium text-red-900">{account.email}</p>
                          <p className="text-sm text-red-600">
                            {account.attempt_count} başarısız deneme • {Math.max(0, remainingMinutes)} dakika kaldı
                          </p>
                          <p className="text-xs text-red-500">
                            Kilitlenme: {new Date(account.locked_at).toLocaleString('tr-TR')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive" className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {Math.max(0, remainingMinutes)}dk
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => unlockAccountMutation.mutate(account.email)}
                          disabled={unlockAccountMutation.isPending}
                          className="flex items-center gap-1"
                        >
                          <Unlock className="w-3 h-3" />
                          Kilidi Kaldır
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Rate Limited IPs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ban className="h-5 w-5" />
              Kısıtlı IP Adresleri ({rateLimitedIPs.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {rateLimitedIPs.length === 0 ? (
              <div className="text-center py-8">
                <Globe className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <p className="text-gray-500">Şu anda kısıtlı IP adresi bulunmuyor</p>
              </div>
            ) : (
              <div className="space-y-3">
                {rateLimitedIPs.map((ipData, index) => {
                  const remainingMinutes = Math.ceil((new Date(ipData.limited_until).getTime() - Date.now()) / 1000 / 60);
                  return (
                    <div
                      key={ipData.id}
                      className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <Ban className="h-4 w-4 text-orange-600" />
                        <div>
                          <p className="font-medium text-orange-900">{ipData.ip_address}</p>
                          <p className="text-sm text-orange-600">
                            {ipData.attempt_count} başarısız deneme • {Math.max(0, remainingMinutes)} dakika kaldı
                          </p>
                          <p className="text-xs text-orange-500">
                            Kısıtlama: {new Date(ipData.limited_at).toLocaleString('tr-TR')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="warning" className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {Math.max(0, remainingMinutes)}dk
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeIPLimitMutation.mutate(ipData.ip_address)}
                          disabled={removeIPLimitMutation.isPending}
                          className="flex items-center gap-1"
                        >
                          <Unlock className="w-3 h-3" />
                          Kısıtlamayı Kaldır
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Güvenlik Ayarları Özeti
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-blue-700">📧 Email Koruması</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>• <strong>5 deneme</strong> sonrası kilitleme</p>
                  <p>• <strong>15 dakika</strong> kilitleme süresi</p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-orange-700">🌐 IP Koruması</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>• <strong>5 deneme</strong> sonrası kısıtlama</p>
                  <p>• <strong>15 dakika</strong> kısıtlama süresi</p>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-green-700">🔔 Bildirimler</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>• <strong>3. denemede</strong> uyarı</p>
                  <p>• <strong>5. denemede</strong> kilitleme</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Debug Panel */}
        {showDebug && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Debug Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">LocalStorage Durumu</h4>
                  <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-64">
                    {JSON.stringify({
                      hasLocalStorage: typeof localStorage !== 'undefined',
                      loginAttemptsRaw: localStorage.getItem('login_attempts'),
                      parsedAttempts: JSON.parse(localStorage.getItem('login_attempts') || '[]'),
                      lockedAccounts: lockedAccounts,
                      rateLimitedIPs: rateLimitedIPs,
                      currentTime: new Date().toISOString(),
                      securityHelperTest: {
                        testEmail: '<EMAIL>',
                        isLocked: securityHelper.isEmailLockedOut('<EMAIL>')
                      }
                    }, null, 2)}
                  </pre>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Test Fonksiyonları</h4>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={() => {
                        const testData = [];
                        // Add 5 failed attempts for test email to trigger lockout
                        for (let i = 0; i < 5; i++) {
                          testData.push({
                            email: '<EMAIL>',
                            timestamp: Date.now() - (i * 1000), // Spread over last 5 seconds
                            success: false,
                            ip: '***********'
                          });
                        }
                        localStorage.setItem('login_attempts', JSON.stringify(testData));
                        refreshLocalData();
                      }}
                    >
                      Test Kilitli Hesap Ekle
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        console.log('🔍 Manual <NAME_EMAIL>:');
                        console.log('isEmailLockedOut:', securityHelper.isEmailLockedOut('<EMAIL>'));
                        console.log('getLockedAccounts:', securityHelper.getLockedAccounts());
                        refreshLocalData();
                      }}
                    >
                      Test Gerçek Hesap
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        console.log('Current localStorage:', localStorage.getItem('login_attempts'));
                        alert('Console\'u kontrol edin');
                      }}
                    >
                      Console'a Yazdır
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Security Tips */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Güvenlik Önerileri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ Aktif Korumalar</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Brute force koruması aktif</li>
                  <li>• Rate limiting uygulanıyor</li>
                  <li>• Otomatik bildirimler çalışıyor</li>
                  <li>• Başarısız denemeler kaydediliyor</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-blue-700">💡 Ek Öneriler</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Güçlü şifre politikası uygulayın</li>
                  <li>• 2FA (İki faktörlü doğrulama) ekleyin</li>
                  <li>• Düzenli güvenlik denetimleri yapın</li>
                  <li>• Şüpheli aktiviteleri takip edin</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
